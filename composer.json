{"name": "laravel/laravel", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "type": "project", "require": {"php": "^7.1.3", "arcanedev/log-viewer": "~4.4", "aws/aws-sdk-php": "^3.134", "bacon/bacon-qr-code": "^2.0", "barryvdh/laravel-cors": "^0.11.0", "doctrine/dbal": "^2.8", "elasticsearch/elasticsearch": "7.9", "fideloper/proxy": "^4.0", "firebase/php-jwt": "^5.0", "gloudemans/shoppingcart": "^2.6", "google/apiclient": "^2.14", "guzzlehttp/guzzle": "^6.3", "intervention/image": "^2.4", "jenssegers/agent": "^2.6", "kalnoy/nestedset": "4.3.4", "laravel/framework": "5.7.*", "laravel/socialite": "^4.0", "laravel/tinker": "~1.0", "laravelcollective/html": "^5.7", "league/omnipay": "^3", "maatwebsite/excel": "^3.1", "mews/purifier": "^3.2", "paquettg/php-html-parser": "3.0.0", "php-ffmpeg/php-ffmpeg": "0.18.0", "phpoffice/phpword": "^1.3", "pragmarx/google2fa": "^7.0", "predis/predis": "^1.1", "prettus/l5-repository": "^2.7", "sentry/sentry-laravel": "^1.0", "spatie/image-optimizer": "1.1.0", "spatie/pdf-to-image": "^2.2", "staudenmeir/eloquent-eager-limit": "^1.0", "torann/geoip": "1.0.5", "tymon/jwt-auth": "1.0.0", "unisharp/laravel-filemanager": "~1.8"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.1", "filp/whoops": "~2.0", "fzaninotto/faker": "~1.4", "mockery/mockery": "~1.0", "phpunit/phpunit": "^7.0"}, "autoload": {"classmap": ["database/seeds", "database/factories", "module/users/database/seeds"], "psr-4": {"App\\": "app/", "Base\\": "module/base/src", "Auth\\": "module/auth/src", "Acl\\": "module/acl/src", "Users\\": "module/users/src", "Hook\\": "module/hook/src", "Course\\": "module/course/src", "ClassLevel\\": "module/classlevel/src", "Subject\\": "module/subject/src", "Book\\": "module/book/src", "Level\\": "module/level/src", "PriceTier\\": "module/pricetier/src", "Tag\\": "module/tag/src", "Media\\": "module/media/src", "Cart\\": "module/cart/src", "MultipleChoices\\": "module/multiplechoices/src", "Coupon\\": "module/coupon/src", "Rating\\": "module/rating/src", "Qa\\": "module/qa/src", "Setting\\": "module/setting/src", "Notify\\": "module/notify/src", "Category\\": "module/category/src", "Advertise\\": "module/advertise/src", "Season\\": "module/season/src", "Admission\\": "module/admission/src", "Livestream\\": "module/livestream/src", "Product\\": "module/product/src", "Omnipay\\OnePay\\": "app/Services/Payment", "Omnipay\\VNPay\\": "app/Services/Payment", "Zizaco\\Entrust\\": "app/Services/RolePermissions"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "extra": {"laravel": {"dont-discover": []}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true}}