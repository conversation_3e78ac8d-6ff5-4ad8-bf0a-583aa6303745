#page{
    background: #fff;
    position: relative;
    font-family: "UTM Avo", sans-serif;
    font-size: 14px;
}
.wrapper {
    width: 1302px;
    margin-left: auto !important;
    margin-right: auto !important;
}
.cta-box {
    text-align: center;
    margin-bottom: 25px;
}
.cta {
    color: #333;
    display: inline-block;
    padding: 9px 30px 11px;
    border-radius: 100px;
    font-size: 24px;
    font-weight: bold;
    border: 4px solid;
}
.cta:hover, .cta:focus {
    color: #333;
}
.cta-white {
    background: #fff;
    border-color: #f68e56;
    color: #ff4228;
}
.cta-white:hover, .cta-white:focus {
    color: #ff4228;
}
.cta-yellow {
    background: #fff200;
    color: #64079d;
    border-color: #14163c;
}
.cta-yellow:hover {
    color: #64079d;
}
.cta-red {
    background: #f24445;
    color: #ffffff;
    border: 2px solid #ebebeb;
}
.cta-red:hover, .cta-red:focus {
    color: #ffffff;
}
.cta-blue{
    background: #157de1;
    -webkit-box-shadow: 0px 6px #0d5fad;
    box-shadow: 0px 6px #0d5fad;
    color: #ffffff;
    font-size: 36px;
    font-family: "UTM Avo", sans-serif;
    font-weight: bold;
}
.justify {
    text-align: justify;
    font-weight: 400;
    font-style: italic;
    padding: 10px;
    font-size: 20px;
    line-height: 1.4;
}
a{
    text-decoration: none;
}
img{
    margin: 0;
    padding: 0;
    border: 0;
    font: inherit;
    max-width: 100%;
    height: auto;
}
a:hover, a:focus{
    text-decoration: none;
    outline: none;
}
h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
    line-height: inherit;
}
.ln li {
    list-style: none;
}
.icielbrushup {
    font-family: iCielBrushUp, "UTM Avo", sans-serif;
}
.roboto {
    font-family: Roboto, "UTM Avo", sans-serif;
}
@media only screen and (max-width : 1300px) {

}
@media only screen and (max-width : 1023px) {

}
@media only screen and (max-width : 991px) {

}
@media only screen and (max-width : 767px) {

}
@media only screen and (max-width : 639px) {

}
@media only screen and (max-width : 479px) {
    #page {
        max-width: 380px;
        margin: 0 auto;
    }
    .wrapper {
        width: 293px;
    }
    .cta {
        font-size: 20px;
    }
}
