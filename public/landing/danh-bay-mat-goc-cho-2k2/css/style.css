html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
    display: block;
}

body {
    line-height: 1;
}

ol,
ul {
    list-style: none;
}

blockquote,
q {
    quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
    content: '';
    content: none;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

a {
    text-decoration: none;
}

* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

img {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
    max-width: 100%;
}

body {
    min-width: 320px;
}

.grid {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-flow: row wrap;
    flex-flow: row wrap;
}

.clear,
.clr {
    clear: both;
    display: block;
    overflow: hidden;
    visibility: hidden;
    width: 0;
    height: 0;
}

.clr {
    content: '';
    display: block;
    clear: both;
}

#page {
    overflow: hidden;
    position: relative;
    font-family: "UTM Avo", Roboto, sans-serif;
}

.container {
    width: 100%;
    max-width: 1210px;
    margin: 0 auto;
}

.cta-box {
    text-align: center;
    margin-bottom: 25px;
}

.cta {
    color: #333;
    display: inline-block;
    padding: 10px 35px 20px;
    border-radius: 8px;
    font-size: 30px;
    font-weight: bold;
    -webkit-box-shadow: inset 0 -6px 0 rgba(0, 0, 0, 0.15);
    box-shadow: inset 0 -6px 0 rgba(0, 0, 0, 0.15);
}

.cta-yellow {
    background: #f7ed6a;
    color: #000;
    font-family: "UTM Avo", Roboto, sans-serif;
    display: inline-block;
    border-radius: 8px;
    font-weight: bold;
}

.cta-yellow:hover {
    color: #673AB7;
}

.cta-red {
    background: #e83c3c;
    color: #ffffff;
    font-size: 36px;
    font-family: "UTM Avo", Roboto, sans-serif;
    font-weight: bold;
    /*padding: 13px 35px 17px;*/
    border-radius: 15px;
}


.cta-blue {
    background: #157de1;
    color: #ffffff;
    font-size: 36px;
    font-family: "UTM Avo", Roboto, sans-serif;
    font-weight: bold;
}

.cta-green {
    background: #41b867;
    color: #ffffff;
    font-size: 36px;
    font-family: "UTM Avo", Roboto, sans-serif;
    font-weight: bold;
}

a:hover,
a:focus {
    text-decoration: none;
}

.cta-red:hover,
.cta-blue:hover {
    color: #FFEB3B;
}

.alert-danger {
    background-color: #ffd907;
    border: none;
    color: #720a0a;
    width: 255px;
    bottom: 20px !important;
    z-index: 999909 !important;
}

@media only screen and (max-width : 767px) {
    .cta {
        -webkit-box-shadow: inset 0 -3px 0 rgba(0, 0, 0, 0.15);
        box-shadow: inset 0 -3px 0 rgba(0, 0, 0, 0.15);
    }

    .alert-danger {
        border: none;
        color: #720a0a;
        width: 200px;
    }
}
