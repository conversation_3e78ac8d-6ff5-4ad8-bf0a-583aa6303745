/* ------------- Outline --------------- */
.outline-tabs > li {
    margin-bottom: 0;
}

.outline-tabs li a {
    border-top: 4px solid #c1c9cd;
    background-color: #b0b8bc;
    position: relative;
    width: 122px;
    padding: 12px 10px 10px;
    text-align: center;
    font-size: 18px;
    color: #fff;
    border-radius: 6px 6px 0 0;
    font-weight: bold;
    text-transform: uppercase;
    margin-right: 0;
}

.outline-tabs li a:hover, .outline-tabs li.active a, .outline-tabs > li.active > a:focus,
.outline-tabs > li.active > a, .outline-tabs > li.active > a:hover, .outline-tabs > li.active > a:focus {
    padding-bottom: 25px;
    padding-top: 22px;
    margin-top: -25px;
    background-color: #ffc707;
    border-color: #ffc500 !important;
    color: #fff;
    border-top: 4px solid rgba(221, 221, 221, 0);
}

.outline-tabs li a:before {
    background: url(../images/down-2.png) center 0 no-repeat;
    width: 5px;
    height: 13px;
    position: absolute;
    top: -4px;
    left: 45px;
    content: '';
}

.outline-tabs li a:hover:before {
    background: url(../images/down-1.png) center 0 no-repeat;
    width: 6px;
    height: 18px;
    position: absolute;
    top: -4px;
    left: 45px;
    content: '';
}

.outline-tabs li.active a:before {
    background: url(../images/down-1.png) center 0 no-repeat;
    width: 6px;
    height: 18px;
    position: absolute;
    top: -4px;
    left: 45px;
    content: '';
}

.outline-tabs {
    padding: 0;
    margin: 35px auto 0;
    border-bottom: 0 solid #eb5522;
    width: 926px;
}

.outline-tabs li:nth-child(1) {
    padding-left: 0;
}

.outline-tabs li:nth-last-child(1) {
    padding-right: 0;
}

.outline-tabs li {
    padding: 0 6px;
    float: left;
}
.outline-teacher-name span{
    text-transform: uppercase;
}

.outline-teacher-tabs {
    width: 926px;
    border-bottom: none;
    text-align: left;
    margin: 10px auto 12px;
}

.outline-teacher-tabs li {
    padding: 0 12px;
    float: none;
    display: inline-block;
}

.outline-teacher-tabs li a {
    background: none;
    border: none;
    padding: 0;
    color: #333;
    text-transform: capitalize;
    border-bottom: 1px solid rgba(47, 163, 84, 0);
    font-size: 12px;
    margin: 0;
    border-radius: 0;
    display: inline-block;
    font-weight: bold;
}

.outline-teacher-tabs li.active a, .outline-teacher-tabs > li > a:hover, .outline-teacher-tabs > li > a:focus {
    outline: none;
    background: none;
    padding: 0;
    margin: 0;
    border: none !important;
    color: #2fa354 !important;
    border-bottom: 1px solid #2fa354 !important;
}

.outline-teacher-toan {
    background: #4ab4eb;
}

.outline-teacher-title {
    color: white;
    text-transform: uppercase;
    font-weight: bold;
    height: 76px;
    line-height: 76px;
    margin: 0 auto;
    position: relative;
    text-align: center;
}

.tbghost > thead > tr > th:nth-child(odd) {
    background: #195272;
}

.tbghost > thead > tr > th:nth-child(even) {
    background: #1fa5c8;
}

.tbghost > thead > tr > th {
    color: white;
    text-align: center;
    padding: 10px 0;
    border-bottom: none;
}

.tbghost > tbody > tr > td.s1 {
    background: #1F82B6;
    color: white;
    font-weight: bold;
    font-size: 16px;
    text-transform: uppercase;
    text-align: center;
    border-right: 1px solid #d1d1d1;
    padding: 20px 10px;
}

.tbghost > tbody > tr > td {
    background: white;
    border: 1px solid #757575;
    color: #000;
    text-align: left;
    padding-left: 20px;
}

.tbghost > tbody > tr > td.s2 {
    font-weight: bold;
    text-align: center;
    padding: 20px 0 0 0;
}

.tbghost > tbody > tr > td.s3 {
    background: #e1e2e3;
    color: #474747;
    font-weight: bold;
    font-size: 15px;
    text-transform: uppercase;
    text-align: center;
    border-right: 1px solid #d1d1d1;
    border-left: 1px solid #d1d1d1;
    padding: 20px 10px;
}

div[id^="outline-teacher"] td:nth-last-child(1) {
    border-left: 1px solid #ddd;
}

.summary-content {
    max-height: 620px;
    margin-bottom: 30px;
    overflow-x: hidden;
    overflow-y: auto;
}

.outline-title {
    background: url(../images/bg-outline-title.png) center no-repeat;
    width: 741px;
    height: 98px;
    color: #fff;
    font-size: 29px;
    font-weight: bold;
    margin: 0 auto;
    padding: 43px 20px 10px;
    text-transform: uppercase;
    text-align: center;
}

#outline {
    background: transparent url(../images/bg_3.png) no-repeat center 50px;
    padding-bottom: 50px;
    padding-top: 20px;
    position: relative;
}

.close_tab-content {
    display: none;
}

.open_tab-content {
    background: #ffc707;
    width: 926px;
    margin: 0 auto;
    font-weight: bold;
    color: #fff;
    padding: 6px 5px 10px;
    position: relative;
    text-align: center;
    cursor: pointer;
}

.open_tab-content img {
    display: inline-block;
    position: relative;
    top: 4px;
    padding-right: 5px;
}

.open_tab-content .os {
    position: absolute;
    top:10px;
    right: 10px;
}

.outline-teacher-toan:before {
    background: url(../images/subject/toan.png) no-repeat;
}

.outline-teacher-title::before {
    content: ".";
    display: inline-block;
    height: 76px;
    position: relative;
    text-indent: -999em;
    width: 86px;
}

.outline-teacher-title span {
    display: inline-block;
}

.outline-teacher-li {
    background: #2CA390;
}

.outline-teacher-li:before {
    background: url(../images/subject/li.png) no-repeat;
}

.outline-teacher-hoa {
    background: #1f82b6;
    color: #fff;
}

.outline-teacher-hoa:before {
    background: url(../images/subject/hoa.png) no-repeat;
}

.outline-teacher-anh {
    background: #5897CA;
}

.outline-teacher-anh:before {
    background: url(../images/subject/anh.png) no-repeat;
}

.outline-teacher-van {
    background: #DE4547;
}

.outline-teacher-van:before {
    background: url(../images/subject/van.png) no-repeat;
}

.outline-teacher-sinh {
    background: #F89752;
}

.outline-teacher-sinh:before {
    background: url(../images/subject/sinh.png) no-repeat;
}

.outline-teacher-xa-hoi {
    background: #9A1F61;
}

.outline-teacher-xa-hoi:before {
    background: url(../images/subject/su.png) no-repeat;
    width: 142px;
}

.tbghost.tbli > thead > tr > th.odd {
    background: #488f7b;
}

.tbghost.tbli > thead > tr > th.even {
    background: #8cc36b;
}

.tbghost.tbli > tbody > tr > td.s1 {
    background: #2CA390;
}

.tbghost.tbhoa > thead > tr > th.odd {
    background: #d89237;
}

.tbghost.tbhoa > tbody > tr > td.s1 {
    background: #FCB809;
}

.tbghost.tbhoa > thead > tr > th.even {
    background: #faad30;
}

.tbghost.tbsinh > thead > tr > th.odd {
    background: #d89237;
}

.tbghost.tbsinh > tbody > tr > td.s1 {
    background: #F89752;
}

.tbghost.tbvan > thead > tr > th.odd {
    background: #5f2d2f;
}

.tbghost.tbvan > tbody > tr > td.s1 {
    background: #DE4547;
}

.tbghost.tbanh > thead > tr > th.odd {
    background: #144e75;
}

.tbghost.tbanh > tbody > tr > td.s1 {
    background: #5897CA;
}

.tbghost.tbsu > thead > tr > th.odd {
    background: #7f0353;
}

.tbghost.tbsu > tbody > tr > td.s1 {
    background: #9A1F61;
}

.tbghost.tbsinh > thead > tr > th.even {
    background: #faad30;
}

.tbghost.tbvan > thead > tr > th.even {
    background: #f2817a;
}

.tbghost.tbanh > thead > tr > th.even {
    background: #388dab;
}

.tbghost.tbsu > thead > tr > th.even {
    background: #b7186a;
}

/* ------------- Responsive ------------- */

@media only screen  and (max-width : 1199px) {
    .tab-content.outline-teachers-content {
        padding: 0 20px;
    }
}

@media only screen and (min-width : 1024px) and (max-width : 1223px) {
    /* Styles */
}

@media only screen and (min-width : 768px) and (max-width : 1023px) {
    /* Styles */
}

@media only screen  and (max-width : 991px) {
    .outline-layout{
        display: none;
    }
}

@media only screen and (min-width : 640px) and (max-width : 767px) {
    /* Styles */
}

@media only screen and (max-width : 639px) {
    .outline-layout {
        display: none;
    }
}

@media only screen and (min-width : 480px) and (max-width : 639px) {
    /* Styles */
}

@media only screen and (max-width : 479px) {
    /* Styles */
}

@media only screen and (min-width : 320px) and (max-width : 479px) {
    /* Styles */
}

.wapper-radius-2 {
    position: absolute;
    left: 0;
    bottom: -48px;
    z-index: 3;
}

.radius-3 {
    width: 169px;
    height: 169px;
    background: #ffc708;
    border-radius: 50%;
}

.radius-4 {
    width: 101px;
    height: 101px;
    background: #005e9b;
    border-radius: 50%;
    position: absolute;
    top: 32%;
    right: -37px;
}