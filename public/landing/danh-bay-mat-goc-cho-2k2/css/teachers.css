#teachers {
    padding-bottom: 70px;
    position: relative;
}

.teacher {
    position: relative;
    margin: 0 -50px;
    z-index: 10;
}

.nav-box3 {
    text-align: center;
    position: relative;
}

.header-nav-teacher {
    background: #ffc707;
    height: 45px;
    padding: 0 40px;
}

.list-dot-teacher {
    height: 100%;
    display: flex;
    align-items: center;
}

.list-dot-teacher li {
    display: inline-block;
    background: #FFFFff;
    width: 13px;
    height: 13px;
    border-radius: 50%;
    margin-right: 15px;
}

.subject-nav-tabs {
    text-align: center;
}

.subject-nav-tabs li {
    display: inline-block;
    position: relative;
}

.subject-nav-tabs-div {
    background: #dde1e8;
}

.bigdes-mobile {
    display: none;
}

.nav-box3 li.active {}

.nav-box3 ul li:first-child {
    /*margin-left: 0;*/
}

.nav-box3 ul li:last-child {
    /*margin-right: 0;*/
}

.content-teacher {
    background: #FFFFff;
    padding-top: 55px;
    padding-bottom: 25px;
    padding-left: 35px;
    padding-right: 35px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    position: relative;
    z-index: 99;
}

.ava-teacher {
    display: none;
}

.ava-teacher.active {
    display: block;
}

.header-big-teacher {
    text-align: center;
    line-height: 1.3;
    font-family: 'Roboto';
    font-weight: 900;
}

.line1-header-teacher {
    font-size: 26px;
    color: #ff0707;
    text-transform: uppercase;
}

.line2-header-teacher {
    font-size: 26px;
    color: #171b21;
    text-transform: uppercase;
}

.title-list-desc-teacher-1 {
    font-family: 'Roboto';
    font-weight: 900;
    color: #000000;
    font-size: 17px;
}

.list-desc-teacher-1 {
    font-family: 'Roboto';
    color: #000000;
    font-size: 17px;
}

.list-desc-teacher-1 li {
    line-height: 1.3;
}

.wapper-list-desc-teacher-1 {
    margin-bottom: 25px;
}

.wapper-list-desc-teacher-2 {
    display: flex;
    margin-bottom: 30px;
}

.list-desc-teacher-2 {
    width: 100%;
    padding: 10px 15px;
    text-align: center;
    color: #000000;
    font-size: 18px;
    border: 3px solid #4ab3eb;
    border-radius: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 1.3;
    font-family: 'Roboto';
    margin: 0 2px;
}

.list-ava-teacher {
    float: left;
}

.list-content {
    float: right;
    width: 650px;
}

.big-image-teacher {
    position: relative;
    z-index: 9;
    max-width: 354px;
}

.angled_container {
    height: 95%;
    overflow: hidden;
    position: absolute;
    top: 40px;
    width: 100%;
}

.angled_container:before,
.angled_container:after {
    content: "";
    width: 100%;
    height: 100%;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
}

.angled_container:before {
    background-color: #4ab4eb;
    transform-origin: left top;
}

.angled_container:after {
    background-color: #4ab4eb;
    transform-origin: right top;
}

.bg-2-teacher {
    background: url(../images/bg-feedback-student.png) no-repeat;
    width: 283px;
    height: 193px;
    position: absolute;
    top: -108px;
    right: -222px;
    -webkit-transform: scaleX(-1);
    transform: scaleX(-1);
    filter: FlipH;
    -ms-filter: "FlipH";
}

.bg-3-teacher {
    background: url(../images/bg-2-feedback.png) no-repeat;
    width: 130px;
    height: 270px;
    position: absolute;
    top: 90%;
    left: -270px;
    z-index: 3;
}

.buttum-excerpt {
    text-align: center;
    margin-bottom: 30px;
}

.cta-teacher:hover {
    color: #FFFFff;
}

.cta-teacher {
    min-width: 200px;
    display: inline-block;
    width: fit-content;
    margin: 0 8px;
    color: #ffffff;
    font-family: "UTM Avo", Roboto, sans-serif;
    font-weight: bold;
    border-radius: 30px;
    font-size: 23px;
    height: 50px;
    padding: 15px 25px;

}

.cta-teacher-1 {
    background: #4ab3eb;
    box-shadow: 0 6px 0 #2a99d4;
}

.cta-teacher-2 {
    background: #e83c3c;
    box-shadow: 0 6px 0 #d00b0b;
}

.name-teacher-sm {
    font-size: 13px;
    color: #464646;
}

.list-teacher-sm {
    text-align: center;
}

.list-teacher-sm ul {
    display: inline-block;
}

.list-teacher-sm li {
    float: right;
    text-align: center;
    margin: 0 10px;
}

.item-wapper-list-teacher {
    display: none;
}

.item-wapper-list-teacher.active {
    display: block;
}

.item-content-teacher {
    display: none;
}

.item-content-teacher.active {
    display: block;
}

.bottom-ava-teacher {
    display: none;
}

.teacher-title-mb {
    display: none;
}

.nav-box3 a {
    display: block;
    min-width: 119px;
    text-align: center;
    line-height: 47px;
    font-family: 'Roboto';
    font-weight: 900;
    font-size: 28px;
    color: #464646;
    border-radius: 10px;
    text-transform: uppercase;
}

.nav-box3 .active a {
    background: #e83c3c;
    color: #FFFFff;
}

.bigdesc-teacher {
    color: #396cff;
    font-size: 16px;
    line-height: 1.3;
    margin-bottom: 20px;
    text-transform: uppercase;
    position: relative;
    font-weight: 700;
}

.bigdesc-teacher:before {
    content: " ";
    background: url(../images/ngoackep.png) no-repeat;
    width: 34px;
    height: 21px;
    position: absolute;
    left: -31px;
    top: -14px;
}

.wapper-count-teacer {
    position: relative;
    z-index: 10;
    background: #fff;
}

.number-count-teacher {
    text-align: center;
    font-size: 20px;
    color: #464646;
    font-weight: bold;
    padding: 10px 10px 17px;
    border: 3px solid #e1e1e1;
    border-radius: 10px;
    position: relative;
    margin-top: -26px;
    margin-left: 30px;
    margin-right: 30px;
}

.number-count-teacher span {
    color: #e83c3c;
    font-size: 49px;
    display: block;
    margin-bottom: 8px;
}

.text-count-teacher {}

.bigdesc-teacher-mobile {
    font-size: 14px;
    color: #3dac54;
    font-weight: bold;
    line-height: 1.3;
    margin-bottom: 15px;
}

.right-mobile-ava-big-teacher {
    padding: 20px;
    border: 1px solid #e1e1e1;
    display: none;
}

.buttum-excerpt-mobile {
    display: none;
}

.list-teacher {
    display: none;
    position: absolute;
    z-index: 999;
    width: 280px;
    background: #fff;
    padding: 20px;
    text-align: left;
    left: -17px;
    -webkit-box-shadow: 8px 6px 19px -10px rgba(0, 0, 0, 0.47);
    -moz-box-shadow: 8px 6px 19px -10px rgba(0, 0, 0, 0.47);
    box-shadow: 8px 6px 19px -10px rgba(0, 0, 0, 0.47);
}

.list-teacher li {
    width: 100%;
}

.list-teacher li a {
    font-size: 16px !important;
    display: block;
    background: none !important;
    color: #464646 !important;
    text-align: left;
    line-height: 28px;
}

@media only screen and (max-width : 1024px) {
    .teacher {
        margin: 0;
    }

    .content-teacher {
        padding-left: 15px;
        padding-right: 15px;
    }

    .list-content {
        width: 570px;
    }
}

@media only screen and (max-width : 812px) {
    .nav-box3 a {
        min-width: 80px;
    }

    .list-ava-teacher {
        width: 100%;
        float: none;
    }

    .line1-header-teacher {
        display: inline-block;
        position: relative;
    }

    .line1-header-teacher:after {
        content: " - ";
    }

    .line2-header-teacher {
        display: inline-block;
    }

    .bigdesc-teacher {
        display: none;
    }

    .wapper-list-desc-teacher-1 {
        display: none;
    }

    .buttum-excerpt {
        display: none;
    }

    .list-content {
        width: 100%;
        float: none;
    }

    .list-desc-teacher-2 {
        margin-left: 2px;
        margin-right: 2px;
    }

    .left-main-ava-big-teacher {
        float: left;
        width: 40%;
    }

    .right-mobile-ava-big-teacher {
        display: block;
        margin-left: auto;
        width: 56%;
    }

    .wapper-list-desc-teacher-2 {
        width: 100%;
    }

    .wapper-main-ava-big-teacher {
        display: flex;
        flex-flow: row wrap;
        margin-bottom: 15px;
    }

    .bigdesc-teacher-mobile {
        font-size: 22px;
    }

    .list-desc-teacher-1 {
        font-size: 23px;
    }

    .title-list-desc-teacher-1 {
        font-size: 30px;
    }

    .buttum-excerpt-mobile .cta-teacher {
        width: 100%;
        text-align: center;
    }

    .buttum-excerpt-mobile .cta-teacher-1 {
        /*background: #ff7e00;*/
        /*box-shadow: 0 6px 0 #ad5500;*/
        background: #e83c3c;
        box-shadow: 0 6px 0 #d00b0b;
        margin-bottom: 15px;
    }

    .buttum-excerpt-mobile .cta-teacher-2 {
        background: #27b67b;
        box-shadow: 0 6px 0 #1c885b;
    }

    .buttum-excerpt-mobile {
        display: block;
        margin-bottom: 7px;
        margin-top: 10px;
    }

    .header-big-teacher {
        margin-bottom: 20px;
    }

    .number-count-teacher {
        border: none;
        padding-left: 0;
        padding-right: 0;
        font-size: 35px;
    }

    .number-count-teacher span {
        border: 3px solid #e1e1e1;
        border-radius: 20px;
        padding: 5px;
    }

    .bg-4-teacher {
        display: none;
    }

    .cta-teacher {
        padding: 15px 5px;
    }
}

@media only screen and (max-width : 767px) {

    .bigdes-mobile {
        display: block;
    }

    .gridbg:before {
        display: none;
    }

    .bottom-ava-teacher {
        display: block;
    }

    .buttum-excerpt {
        display: none;
    }

    .teacher-title-mb {
        display: block;
    }

    .excerpt-teacher:after {
        content: none;
    }

    .list-review-teacher ul li:nth-child(2):after {
        display: none;
    }
}

@media only screen and (max-width : 639px) {
    .bg-1-teacher {
        display: none;
    }

    .bg-2-teacher {
        display: none;
    }

    .header-nav-teacher {
        display: none;
    }

    .teacher {
        margin: 0 -15px;
    }

    .nav-box3 a {
        font-size: 10px;
        line-height: 25px;
        min-width: 40px;
    }

    .subject-nav-tabs-div {
        padding: 3px 0;
    }

    .content-teacher {
        padding-top: 16px;
    }

    .line2-header-teacher {
        font-size: 12px;
    }

    .line1-header-teacher {
        font-size: 12px;
    }

    .content-teacher {
        padding-left: 0;
        padding-right: 0;
    }

    .right-mobile-ava-big-teacher {
        padding: 10px;
    }

    .bigdesc-teacher-mobile {
        font-size: 12px;
    }

    .title-list-desc-teacher-1 {
        font-size: 12px;
    }

    .list-desc-teacher-1 {
        font-size: 12px;
    }

    .number-count-teacher {
        padding-left: 0;
        padding-right: 0;
        margin-left: 0;
        margin-right: 0;
        font-size: 12px;
        margin-top: 0;
    }

    .number-count-teacher span {
        font-size: 16px;
    }

    .number-count-teacher {}

    .cta-teacher {
        font-size: 9px;
        min-width: 100px;
        padding: 10px 6px;
        height: 30px;
        margin-left: 0;
        margin-right: 0;
    }

    .left-main-ava-big-teacher {
        padding-left: 2px;
    }

    .right-mobile-ava-big-teacher {
        margin-right: 2px;
    }

    .list-desc-teacher-2 {
        font-size: 12px;
    }

    .list-desc-teacher-2 {
        padding-left: 2px;
        padding-right: 2px;
    }

    .name-teacher-sm {
        font-size: 8px;
    }

    .list-teacher-sm li {
        margin: 0 3px;
        width: 70px;
    }

    #teachers {
        background: #FFFFff;
    }

    #teachers {
        padding-bottom: 0;
    }

    .angled_container {
        display: none;
    }

    .bg-3-teacher {
        display: none;
    }

    .left-main-ava-big-teacher {
        width: 33%;
    }

    .right-mobile-ava-big-teacher {
        width: 62%;
    }
}

@media only screen and (max-width : 568px) {

    li.active:nth-child(5) .list-teacher,
    li.active:nth-child(6) .list-teacher,
    li.active:last-child .list-teacher {
        left: -160px;
    }

    li.active:nth-child(4) .list-teacher {
        left: -100px;
    }
}

.wapper-radius-1 {
    position: absolute;
    top: 95%;
    right: -150px;
}

.radius-1 {
    width: 210px;
    height: 210px;
    background: #ffc708;
    border-radius: 50%;
}

.radius-2 {
    width: 143px;
    height: 143px;
    background: #4ab4eb;
    border-radius: 50%;
    position: absolute;
    margin: 0 auto;
    left: 48px;
    right: 0;
    bottom: -87px;
}
