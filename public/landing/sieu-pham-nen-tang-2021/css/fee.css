.fee {
    background-image: url("../images/l5/bg.png"), linear-gradient(90deg,rgba(0,0,0,0) 0%,#ff3203 360%)!important;
    padding: 60px 0 65px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
}

.packages-wr {
    width: 798px;
    float: left;
    padding: 0 18px;
}
.fee-title {
    font-size: 24px;
    color: #f24445;
    text-align: center;
    margin: 25px 0 90px;
}
.title-2-fee {
    padding-top: 57px;
    color: #ff0807;
    font-size: 36px;
    font-weight: bold;
    text-align: center;
    font-family: 'UTM Avo';
    margin-bottom: 15px;
}
.package {
    color: #fff;
    width: 230px;
    min-height: 160px;
    background: #3c297b;
    border-radius: 10px;
    float: left;
    margin: 0 12px 80px;
    text-align: center;
    padding: 35px 5px;
    position: relative;
    box-shadow: 0px 9px 13px 0px rgba(0, 0, 0, 0.33);
}
.item-price-1 {
    background: #4fd0bd;
}

.item-price-2 {
    background: #f4d422;
}

.item-price-3 {
    background: #353c8d;
}

.package-name {
    font-size: 24px;
    font-weight: bold;
    text-transform: uppercase;
    margin-bottom: 10px;
}
.text1-package {
    color: #ffffff;
    font-size: 14px;
    /*font-family: 'UTM Avo Bold';*/
    margin-bottom: 5px;
}

.text2-package {
    font-size: 15px;
    font-family: 'UTM Avo';
    margin-bottom: 20px;
    color: #FFFFff;
}

.package-list-price {
    font-size: 16px;
    font-weight: bold;
    position: relative;
    margin-bottom: 20px;
    text-decoration: line-through;
}
.package-sell-price {
    font-size: 28px;
    font-weight: bold;
    color: #fff;
    margin-bottom: 20px;
}
.link-price a {
    background: #FFFFff;
    display: inline-block;
    font-family: 'UTM Avo Bold';
    font-size: 18px;
    color: #353c8d;
    border-radius: 30px;
    height: 49px;
    line-height: 45px;
    padding: 0 28px;
    border: 2px solid #353c8d;
}

.hover-eff-buttom:hover {
    box-shadow: 0 20px 5px -10px rgba(0, 0, 0, 0.4);
    transform: translateY(10px);
}

.hover-eff-buttom {
    transition: 0.4s;
}

.item-price-2 .package-name,
.item-price-2 .text1-package,
.item-price-2 .text2-package,
.item-price-2 .package-sell-price {
     color: #353c8d; 
}

.package-highlight {
    background: transparent url("../images/l5/i1.png") no-repeat center bottom;
    position: absolute;
    height: 71px;
    width: 130px;
    bottom: -71px;
    left: 50%;
    margin-left: -65px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
    padding: 10px 20px;
}
.package:nth-child(3) .package-highlight {
    background: transparent url("../images/l5/i2.png") no-repeat center bottom;
}
.fee-form-wr {
    float: left;
    padding: 0 20px;
}
@media only screen and (max-width : 1300px) {

}
@media only screen and (max-width : 1023px) {

}
@media only screen and (max-width : 991px) {

}
@media only screen and (max-width : 767px) {

}
@media only screen and (max-width : 639px) {
    .packages-wr {
        width: 100%;
    }
}
@media only screen and (max-width : 479px) {
    .fee {
        background: #ebf0f3 url("../images/l5/bgm.png") no-repeat center 0;
        padding: 20px 0 30px;
    }
    .fee-form-wr {
        padding: 0;
        width: 100%;
        left: 0;
    }
    .packages-wr {
        width: auto;
    }
    .fee-title {
        font-size: 18px;
        color: #5644a5;
        margin: 0 0 35px;
    }
    .title-2-fee {
        font-size: 21px;
        padding-top: 10px;
    }
    .fee-title span {
        display: inline-block;
        color: #f24445;
    }
    .package {
        width: 100%;
        margin: 0 auto;
        margin-bottom: 95px;
    }
    .package:first-child {
        margin-bottom: 39px;
    }
}