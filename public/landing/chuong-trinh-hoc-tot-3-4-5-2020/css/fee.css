.fee {
    background: #ffd8eb85 url("../images/l5/bg.png") no-repeat center 0;
    padding: 60px 0 65px;
}
.packages-wr {
    width: 798px;
    float: left;
    padding: 0 18px;
}
.fee-title {
    font-size: 30px;
    color: #f24445;
    text-align: center;
    margin: 25px 0 90px;
}
.package {
    color: #fff;
    width: 230px;
    min-height: 270px;
    background: #3c297b;
    border-radius: 10px;
    float: left;
    margin: 0 12px 80px;
    text-align: center;
    padding: 20px 5px;
    position: relative;
    box-shadow: 0px 9px 13px 0px rgba(0, 0, 0, 0.33);
}
.package-name {
    font-size: 24px;
    font-weight: bold;
    text-transform: uppercase;
    margin-bottom: 25px;
}
.package-list-price {
    font-size: 16px;
    font-weight: bold;
    position: relative;
    margin-bottom: 20px;
    text-decoration: line-through;
}
.package-sell-price {
    font-size: 28px;
    font-weight: bold;
    color: #130a9c;
}
.package-highlight {
    background: transparent url("../images/l5/i1.png") no-repeat center bottom;
    position: absolute;
    height: 71px;
    width: 130px;
    bottom: -71px;
    left: 50%;
    margin-left: -65px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
    padding: 10px 20px;
}
.package:nth-child(3) .package-highlight {
    background: transparent url("../images/l5/i2.png") no-repeat center bottom;
}
.fee-form-wr {
    float: left;
    padding: 0 20px;
}
@media only screen and (max-width : 1300px) {

}
@media only screen and (max-width : 1023px) {

}
@media only screen and (max-width : 991px) {

}
@media only screen and (max-width : 767px) {

}
@media only screen and (max-width : 639px) {

}
@media only screen and (max-width : 479px) {
    .fee {
        background: #ffd8eb85 url("../images/l5/bgm.png") no-repeat center 0;
        padding: 20px 0 30px;
    }
    .packages-wr {
        width: auto;
    }
    .fee-title {
        font-size: 24px;
        color: #5644a5;
        margin: 0 0 35px;
    }
    .fee-title span {
        display: inline-block;
        color: #f24445;
    }
    .package {
        margin-bottom: 95px;
    }
    .package:first-child {
        margin-bottom: 39px;
    }
}