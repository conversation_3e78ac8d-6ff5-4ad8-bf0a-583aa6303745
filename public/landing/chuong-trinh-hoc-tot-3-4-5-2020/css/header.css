.hightlight {
    background: transparent url("../images/banner.png") no-repeat center center;
    background-size: cover;
    height: 900px;
    position: relative;
}
.hightlight .wrapper {
    padding: 100px 39px;
}
.hl-info {
    float: left;
    width: 780px;
    color: #fff;
    padding: 90px 0 0 0;
    text-align: center;
    margin-top: -100px;
}
.hl-title {
    color: #fff;
    font-size: 60px;
    line-height: 1.25;
    text-transform: uppercase;
    margin: 0 0 20px;
    font-weight: bold;
    text-shadow: 3px -2px 0 #ec1c24,
    -4px -1px 0 #ec1c24, -4px 0px 0 #ec1c24,
    -4px 1px 0 #ec1c24, -4px 2px 0 #ec1c24,
    -3px -3px 0 #ec1c24, -3px -2px 0 #ec1c24,
    -3px -1px 0 #ec1c24, -3px 0px 0 #ec1c24,
    -3px 1px 0 #ec1c24, -3px 2px 0 #ec1c24,
    -3px 3px 0 #ec1c24, -2px -4px 0 #ec1c24,
    -2px -3px 0 #ec1c24, -2px -2px 0 #ec1c24,
    -2px -1px 0 #ec1c24, -2px 0px 0 #ec1c24,
    -2px 1px 0 #ec1c24, -2px 2px 0 #ec1c24,
    -2px 3px 0 #ec1c24, -2px 4px 0 #ec1c24,
    -1px -4px 0 #ec1c24, -1px -3px 0 #ec1c24,
    -1px -2px 0 #ec1c24, -1px -1px 0 #ec1c24,
    -1px 0px 0 #ec1c24, -1px 1px 0 #ec1c24,
    -1px 2px 0 #ec1c24, -1px 3px 0 #ec1c24,
    -1px 4px 0 #ec1c24, 0px -4px 0 #ec1c24,
    0px -3px 0 #ec1c24, 0px -2px 0 #ec1c24,
    0px -1px 0 #ec1c24, 0px 0px 0 #ec1c24,
    0px 1px 0 #ec1c24, 0px 2px 0 #ec1c24,
    0px 3px 0 #ec1c24, 0px 4px 0 #ec1c24,
    1px -4px 0 #ec1c24, 1px -3px 0 #ec1c24,
    1px -2px 0 #ec1c24, 1px -1px 0 #ec1c24,
    1px 0px 0 #ec1c24, 1px 1px 0 #ec1c24,
    1px 2px 0 #ec1c24, 1px 3px 0 #ec1c24,
    1px 4px 0 #ec1c24, 2px -4px 0 #ec1c24,
    2px -3px 0 #ec1c24, 2px -2px 0 #ec1c24,
    2px -1px 0 #ec1c24, 2px 0px 0 #ec1c24,
    2px 1px 0 #ec1c24, 2px 2px 0 #ec1c24,
    2px 3px 0 #ec1c24, 2px 4px 0 #ec1c24,
    3px -3px 0 #ec1c24, 3px -2px 0 #ec1c24,
    3px -1px 0 #ec1c24, 3px 0px 0 #ec1c24,
    3px 1px 0 #ec1c24, 3px 2px 0 #ec1c24,
    3px 3px 0 #ec1c24, 4px -2px 0 #ec1c24,
    4px -1px 0 #ec1c24, 4px 0px 0 #ec1c24,
    4px 1px 0 #ec1c24, 4px 2px 0 #ec1c24;
}
.hl-desc {
    font-size: 42px;
    font-weight: bold;
    margin: 0 0 40px;
    color: #04820e;
    line-height: 1.1;
}
.hl-form {
    background: transparent url("../images/l1/fbg.png") no-repeat bottom center;
    float: right;
    padding: 0 30px 15px;
    margin: -10px -30px auto auto;
}
.bg_cloud {
    background: url(../images/bg_cloud.png) no-repeat center bottom -20px;
    background-size: cover;
    width: 100%;
    height: calc(100vw * .125);
    position: absolute;
    bottom: 0;
    z-index: auto;
}
/* --------- Form --------- */
.register-form-wr {
    background: #03b4c5 url("../images/form-bg.png") no-repeat bottom center;
    /*background: #03b4c5;*/
    background-size: cover;
    width: 410px;
    padding: 50px 25px 30px;
    border-radius: 20px;
    box-shadow: 0 0 12px rgba(0, 0, 0, 0.5);
}
.register-form-title {
    color: #fff;
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    text-transform: uppercase;
    margin: 0 0 30px;
}
.reg-form-group label {
    color: #fff;
    font-size: 14px;
    font-weight: bold;
}
.reg-form-group {
    position: relative;
    padding: 0 0 25px;
}
.reg-form-input {
    display: block;
    width: 100%;
    border: 1px solid #14163c;
    background: #fff;
    border-radius: 30px;
    height: 42px;
    padding: 5px 20px;
}
.reg-form-group .error-message {
    position: absolute;
    font-size: 11px;
    margin: 0;
}
select.reg-form-input {
    -webkit-appearance: none;
    -moz-appearance: none;
    background: #fff url("../images/l1/select-arrow.png") no-repeat 93% center;
    background-size: 3%;
}
.reg-form-cta-wr {
    padding: 20px 0 0;
}
.cta.btn-register {
    padding: 6px 30px 8px;
}
@media only screen and (max-width : 1300px) {

}
@media only screen and (max-width : 1023px) {

}
@media only screen and (max-width : 991px) {

}
@media only screen and (max-width : 767px) {

}
@media only screen and (max-width : 639px) {

}
@media only screen and (max-width : 479px) {
    .hightlight {
        height: inherit;
        padding-bottom: 30px;
    }
    .hightlight .wrapper {
        padding: 50px 0 0;
    }
    .logo-wr {
        text-align: center;
    }
    .hl-info {
        float: none;
        width: 100%;
        padding: 35px 0 0;
        text-align: center;
        margin-left: auto;
        margin-top: 0;
    }
    .hl-title {
        font-size: 26px;
    }
    .hl-desc {
        font-size: 16px;
    }
    .hl-form {
        background: transparent;
        padding: 0;
        margin: 0;
    }
    .register-form-wr {
        padding: 30px;
        width: 100%;
        border-radius: 10px;
    }
    .register-form-title {
        font-size: 16px;
        margin-bottom: 15px;
    }
    .reg-form-group label {
        font-size: 12px;
    }
    .reg-form-input {
        height: 32px;
        font-size: 10px;
        padding: 2px 15px;
    }
    .reg-form-group {
        padding: 0 0 20px;
    }
    select.reg-form-input {
        background: #fff url("../images/l1/select-arrow-m.png") no-repeat 93% center;
        background-size: 3%;
    }
    .btn-register {
        font-size: 17px;
    }
}
