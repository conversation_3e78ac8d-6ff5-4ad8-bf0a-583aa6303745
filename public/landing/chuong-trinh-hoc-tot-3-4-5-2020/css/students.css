.feedback-layout {
    background: transparent url("../images/l6/bg.png") no-repeat center 188px;
    padding: 75px 0 0;
}
.feedback-info-wr {
    float: right;
    text-align: center;
}
.feedback-header {
    font-size: 36px;
    color: #3c297b;
    font-weight: bold;
    margin: 0 0 35px;
}
.feedback-header span {
    color: #ff4a39;
}
.feedback-content {
    font-size: 16px;
    font-weight: bold;
    color: #777777;
    margin: 0 0 35px;
}
.feedback-content span {
    font-size: 24px;
    color: #fa7c1f;
}
.feedback-cta-wr {
    margin: 0 0 20px;
}
.feedback-image-wr {
    height: 327px;
    overflow: hidden;
}

.students-wr {
    background: transparent url("../images/l6/ip.png") no-repeat 0 0;
    width: 435px;
    float: left;
    margin-left: 140px;
}
.carousel-student {
    padding: 68px 20px 10px;
    width: 362px;
}
.student {
    width: 324px;
}

.carousel-teacher-control {
    display: block;
    width: 61px;
    height: 61px;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    background: #f24445;
    margin-top: -41px;
}
.carousel-teacher-control.left {
    background: #f24445 url("../images/l3/b1.png") no-repeat center center;
    left: -25px;
}
.carousel-teacher-control.right {
    background: #f24445 url("../images/l3/b2.png") no-repeat center center;
    right: -25px;
}

@media only screen and (max-width : 1300px) {

}
@media only screen and (max-width : 1023px) {

}
@media only screen and (max-width : 991px) {

}
@media only screen and (max-width : 767px) {

}
@media only screen and (max-width : 639px) {

}
@media only screen and (max-width : 479px) {
    .feedback-layout {
        background: transparent url("../images/l6/bgm.png") no-repeat center 0;
        padding: 20px 0 0;
        position: relative;
        z-index: 1;
        margin-top: -5px;
        overflow: hidden;
    }
    .feedback-info-wr {
        float: none;
        padding: 0;
    }
    .feedback-header {
        font-size: 16px;
        margin-bottom: 13px;
    }
    .feedback-content {
        font-size: 10px;
        margin-bottom: 13px;
    }
    .feedback-content span {
        font-size: 11px;
    }
    .cta.cta-red.cta-feedback {
        font-size: 12px;
        border-color: #fbaf5d;
    }
    .feedback-image-wr {
        width: 240px;
        margin: 0 auto;
        height: 133px;
    }
    .students-wr {
        background: transparent url("../images/l6/ipm.png") no-repeat 0 0;
        margin-left: 8px;
        width: 312px;
        float: none;
    }
    .carousel-student {
        width: 261px;
        padding: 47px 14px 130px 14px;
    }
    .student {
        width: 100%;
        height: 400px;
    }
    .carousel-teacher-control {
        width: 42px;
        height: 42px;
    }
    .carousel-teacher-control.left {
        background: #f24445 url("../images/l3/b1m.png") no-repeat center center;
        left: -25px;
    }
    .carousel-teacher-control.right {
        background: #f24445 url("../images/l3/b2m.png") no-repeat center center;
        right: -25px;
    }
}