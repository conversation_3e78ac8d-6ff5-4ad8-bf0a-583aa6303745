.features {
    background: #def8ff url("../images/l2/bg_3.png") no-repeat top center;
    padding-top: 50px;
}
.qoute {
    text-align: center;
    width: 910px;
    margin: 0 auto;
    position: relative;
}
.qoute:before, .qoute:after {
    content: "";
    width: 43px;
    height: 33px;
    position: absolute;
}
.qoute::before {
    background: transparent url("../images/l2/q1.png") no-repeat;
    top: 0;
    left: 0;
}
.qoute:after {
    background: transparent url("../images/l2/q2.png") no-repeat;
    bottom: 0;
    right: 0;
}
.qoute-title {
    font-size: 48px;
    margin: 0 0 10px;
}
.qoute-list {
    font-size: 18px;
    padding: 0 50px;
}
.necessary {
    margin: 0 0 33px;
}
.necessary-title {
    text-align: center;
    color: #f0663f;
    font-size: 30px;
    margin: 20px 0 30px;
}
.necessary-items {
    margin: 0;
    padding: 0 95px;
}
.necessary-items li {
    float: left;
    width: 233px;
    margin-right: 60px;
    box-shadow: 0px 5px 8.58px 4.42px rgba(0, 0, 0, 0.1);
    padding: 25px 15px;
    text-align: center;
    border-radius: 10px;
    min-height: 300px;
    position: relative;
    background: #fff;
}
.necessary-items li::before {
    display: block;
    position: absolute;
    top: 50%;
    right: -45px;
    font-size: 48px;
    font-weight: bold;
    color: #58478e;
    height: 26px;
    line-height: 3px;
    margin-top: -13px;
}
.af-plus::before {
    content: "+";
}
.af-equal::before {
    content: "=";
}
.necessary-items li:nth-child(1) {
    padding-top: 70px;
}
.necessary-items li:nth-child(3) {
    padding-top: 50px;
}
.necessary-items li:last-child {
    margin-right: 0;
    box-shadow: none;
    background: #58478e;
}
.ni-image {
    margin-bottom: 15px;
}
.ni-content {
    font-size: 20px;
}
.ni-title {
    font-size: 24px;
    color: #ffff62;
    font-weight: bold;
    text-transform: uppercase;
    margin: 40px 0 25px;
}
.ni-stitle {
    font-size: 18px;
    font-weight: bold;
    color: #fff;
}
.cta-red.cta-feature {
    border: 4px solid #f68e56;
    font-size: 26px;
    text-transform: uppercase;
}
@media only screen and (max-width : 1300px) {

}
@media only screen and (max-width : 1023px) {

}
@media only screen and (max-width : 991px) {

}
@media only screen and (max-width : 767px) {

}
@media only screen and (max-width : 639px) {

}
@media only screen and (max-width : 479px) {
    .qoute {
        width: 100%;
        padding: 45px 0;
    }
    .qoute::before, .qoute::after {
        left: 50%;
        margin-left: -31px;
    }
    .qoute-title {
        font-size: 36px;
    }
    .qoute-list {
        font-size: 13px;
        text-align: left;
        padding-left: 30px;
        padding-right: 0;
    }
    .qoute-list li {
        position: relative;
    }
    .qoute-list li::before {
        content: "";
        display: block;
        background: #f24445;
        width: 4px;
        height: 4px;
        position: absolute;
        left: -12px;
        top: 9px;
    }
    .necessary-title {
        font-size: 24px;
    }
    .necessary-items {
        padding: 0 30px;
    }
    .necessary-items li {
        width: 100%;
        float: none;
        margin: 0 0 45px;
        min-height: 108px;
        padding: 25px 30px;
    }
    .necessary-items li:nth-child(1) {
        padding: 30px 30px 15px;
    }
    .necessary-items li:nth-child(3) {
        padding: 25px 30px 15px;
    }
    .ni-image {
        display: none;
    }
    .ni-content {
        font-size: 14px;
    }
    .necessary-items li::before {
        top: auto;
        left: 50%;
        right: auto;
        margin-left: -14px;
        bottom: -35px;
    }
    .ni-title {
        margin: 0 0 20px;
    }
    .necessary {
        margin: 0;
    }
    .cta.cta-red.cta-feature {
        font-size: 22px;
        margin-bottom: 35px;
    }
}