(function ($) {
    $(document).ready(function () {
        /*--------------------- PHONE CONFIRM FORM -------------------*/
        $('[data-toggle="confirmPhoneForm"]').validate({
            rules: {
                name: {
                    required: true,
                },
                phone: {
                    required: true,
                    phone: true
                },
                email:{
                    email: true
                },
                class_level: {
                    required: true,
                    class_level: true
                },
                position: {
                    required: true,
                    position: true,
                },
            },
            messages: {
                name: {
                    required: '* Bạn chưa nhập họ tên',
                },
                phone: {
                    required: '* Bạn chưa nhập số điện thoại',
                    phone: '* Số điện thoại không hợp lệ'
                },
                email:{
                    email: "* Không đúng định dạng email"
                },
                class_level: {
                    required: '* Bạn chưa chọn lớp',
                    class_level: '* <PERSON><PERSON><PERSON> không đúng định dạng'
                },
                position: {
                    required: '* <PERSON>ạn chưa chọn định danh',
                    class_level: '* <PERSON><PERSON><PERSON> danh không đúng định dạng'
                },
            },
            errorElement: 'div',
            errorPlacement: function (place, element) {
                place.addClass('error-message').appendTo(element.closest('div'));
            },
            highlight: function (element, errorClass, validClass) {
                if (element.type === "radio") {
                    this.findByName(element.name).addClass(errorClass).removeClass(validClass);
                } else if (element.type === "select-one" || element.type === "select-multiple") {
                    var $element = $(element);
                    $element.addClass(errorClass).removeClass(validClass);
                    var $next = $element.next();
                    if ($next.length > 0 && $next.hasClass('select2')) {
                        $next.addClass(errorClass).removeClass(validClass);
                    }
                } else {
                    $(element).addClass(errorClass).removeClass(validClass);
                }
            },
            unhighlight: function (element, errorClass, validClass) {
                if (element.type === "radio") {
                    this.findByName(element.name).addClass(validClass).removeClass(errorClass);
                } else if (element.type === "select-one" || element.type === "select-multiple") {
                    var $element = $(element);
                    $element.addClass(validClass).removeClass(errorClass);
                    var $next = $element.next();
                    if ($next.length > 0 && $next.hasClass('select2')) {
                        $next.addClass(validClass).removeClass(errorClass);
                    }
                } else {
                    $(element).addClass(validClass).removeClass(errorClass);
                }
            },
            submitHandler: function(form) {
                var $thisForm = $(form);

                return $thisForm.valid();
            }
        });
        $('[data-toggle="confirmPhoneForm2"]').validate({
            rules: {
                name: {
                    required: true,
                },
                phone: {
                    required: true,
                    phone: true
                },
                email:{
                    email: true
                },
                class_level: {
                    required: true,
                    class_level: true
                },
                position: {
                    required: true,
                    position: true,
                },
            },
            messages: {
                name: {
                    required: '* Bạn chưa nhập họ tên',
                },
                phone: {
                    required: '* Bạn chưa nhập số điện thoại',
                    phone: '* Số điện thoại không hợp lệ'
                },
                email:{
                    email: "* Không đúng định dạng email"
                },
                class_level: {
                    required: '* Bạn chưa chọn lớp',
                    class_level: '* Lớp không đúng định dạng'
                },
                position: {
                    required: '* Bạn chưa chọn định danh',
                    class_level: '* Định danh không đúng định dạng'
                },
            },
            errorElement: 'div',
            errorPlacement: function (place, element) {
                place.addClass('error-message').appendTo(element.closest('div'));
            },
            highlight: function (element, errorClass, validClass) {
                if (element.type === "radio") {
                    this.findByName(element.name).addClass(errorClass).removeClass(validClass);
                } else if (element.type === "select-one" || element.type === "select-multiple") {
                    var $element = $(element);
                    $element.addClass(errorClass).removeClass(validClass);
                    var $next = $element.next();
                    if ($next.length > 0 && $next.hasClass('select2')) {
                        $next.addClass(errorClass).removeClass(validClass);
                    }
                } else {
                    $(element).addClass(errorClass).removeClass(validClass);
                }
            },
            unhighlight: function (element, errorClass, validClass) {
                if (element.type === "radio") {
                    this.findByName(element.name).addClass(validClass).removeClass(errorClass);
                } else if (element.type === "select-one" || element.type === "select-multiple") {
                    var $element = $(element);
                    $element.addClass(validClass).removeClass(errorClass);
                    var $next = $element.next();
                    if ($next.length > 0 && $next.hasClass('select2')) {
                        $next.addClass(validClass).removeClass(errorClass);
                    }
                } else {
                    $(element).addClass(validClass).removeClass(errorClass);
                }
            },
            submitHandler: function(form) {
                var $thisForm = $(form);

                return $thisForm.valid();
            }
        });
        /*--------------------- PHONE CONFIRM FORM -------------------*/

        /*--------------------- Scroll to -------------------*/
        $(document).on('click', '[data-toggle="scroll"]', function (e) {
            if (typeof e != 'undefined' && typeof e.preventDefault == 'function') {
                e.preventDefault();
            }

            var $this = $(this),
                $target = $($this.data('target'));
            if ($target.length > 0) {
                var $point = $target.offset().top,
                    $duration = 800;
                if ($this.data('duration')) {
                    $duration = $this.data('duration');
                }
                scrollTo($point, $duration);
            }
        });
        /*--------------------- Scroll to -------------------*/

        $('#js-hidden-msg').on('click', function() {
            $('.msg-text-wrap').hide();
        })

        $('#js-hidden-msg').on('click', function() {
            $('.msg-text-wrap').hide();
        })

        $('.msg-icon').on('click', function() {
            $('.msg-text-wrap').toggle();
        })
    });

})(jQuery);

