.msg-icon {
    height: 50px;
    width: 50px;
    top: auto;
    left: auto;
    bottom: 30px;
    right: 15px;
    cursor: pointer;
    position: fixed;
    z-index: 9999;
}

.msg-image-background {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    top: 0px;
    left: 0px;
    width: 60px;
    height: 60px;
    background-image: url(../../assets/images/icon-messenger.png);
    background-repeat: no-repeat;
    background-position: left top;
    background-size: cover;
    background-attachment: scroll;
    background-origin: content-box;
    position: absolute;
    margin: 0 auto;
}

.msg-text-wrap {
    top: auto;
    left: auto;
    bottom: 30px;
    right: 70px;
    width: 290px;
    height: 117px;
    position: fixed;
    z-index: 9999;
    box-shadow: 18px 11px 20px -20px #000;
    -webkit-box-shadow: 18px 11px 20px -20px #000;
    opacity: 0.95;
    background-color: rgb(255, 255, 255);
    border-color: rgb(132, 132, 132);
    border-width: 1px;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    border-bottom-left-radius: 20px;
    padding: 10px;
}

.msg-text p {
    color: rgb(0, 0, 0);
    font-size: 13px;
    line-height: 1.6;
    margin-bottom: 0;
}

.msg-text span {
    font-weight: bold;
}

.msg-dot {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    display: inline-block;
    background-color: rgb(48, 232, 73);
}

ol, ul {
    list-style: none;
}

.grid {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-flow: row wrap;
    flex-flow: row wrap;
}

textarea:hover,
input:hover,
textarea:active,
input:active,
textarea:focus,
input:focus,
button:focus,
button:active,
button:hover,
label:focus,
.btn:active,
.btn.active
{
    outline:0px !important;
    -webkit-appearance:none;
    box-shadow: none !important;
}

select:focus {
   outline-color: transparent;
}

@media only screen and (max-width : 479px) {
    .msg-text-wrap {
        width: 250px;
        height: 110px;
    }

    .msg-text p {
        font-size: 11px;
    }
}

.fb-comments_area {
    border: 1px solid #eee;
    box-shadow: 0 2px 2px rgba(0,0,0,.15);
    margin-top: 50px;
    background: #fff;
    padding: 10px;
    border-radius: 5px;
}
