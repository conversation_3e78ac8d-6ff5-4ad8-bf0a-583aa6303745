# Dự án kh<PERSON><PERSON> học Vietjack

## Dự án sử dụng:

- Larravel 5.5.*
- Bootstrap 3 cho web và Bootstrap 4 cho bên quản trị admin
- Sử dụng một số component bằng react bên admin
- Hể quản trị CSDL MySQL
- Và nhiều package hỗ trợ khác ...

## Cài đặt dự án:

### Không sử dụng docker:

- Yêu cầu php phiên bản dưới v7.2.5 rồi setup môi trường trên local như bình thường:
+ cp .env.example .env
+ Thay đổi 1 số thông tin config trong file .env
+ export COMPOSER_MEMORY_LIMIT=-1
+ composer install
+ npm i
+ php artisan key:generate
+ php artisan migrate --seed
+ npm run dev
+ php artisan serve

### Sử dụng docker:

- Docker đ<PERSON> cài sẵn php v7.2.5:
+ <PERSON><PERSON><PERSON> đặt docker, docker-compose
+ cp .env.example .env
+ Thay đổi 1 số thông tin config trong file .env
+ docker-compose up -d
+ Truy cập vào container: docker exec -it [container name] bash
+ composer install
+ npm i
+ php artisan key:generate
+ php artisan migrate --seed
+ npm run dev

### Download DB test:

- database/database_local.zip

## Note:

- Dự án sử dụng 2 server, một server dùng để xử lý upload video, một server chạy web. Hai server gọi nhau thông qua api
- Xử lý video sử dụng: [nginx-vod-module](https://github.com/kaltura/nginx-vod-module)
- Iphone not play video mp4 is Interlaced Scan type
- Cách cài đặt server mail để check mail là hợp lệ trước khi gửi mail bằng aws tham khảo [ở đây](https://www.tecmint.com/install-postfix-mail-server-with-webmail-in-debian/)

### Cổng thanh toán ONEPAY
- [Tài liệu pdf](https://mtf.onepay.vn/developer/resource/documents/docx/quy_trinh_tich_hop-quocte.pdf)
- [Tài liệu developer](https://mtf.onepay.vn/developer/)
