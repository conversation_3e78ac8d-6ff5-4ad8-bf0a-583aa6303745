html {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
}

body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
}

::-moz-selection {
    background: #b3d4fc;
    text-shadow: none;
}

::selection {
    background: #b3d4fc;
    text-shadow: none;
}

a {
    color: #4384F5;
    text-decoration: none;

    &:hover {
        opacity: 0.8;
    }
}

svg {
    vertical-align: middle;
}

.container {
    display: table;
    font-family: 'Open Sans', sans-serif;
    height: 100%;
    width: 100%;
    background: #ffffff;
}

.content {
    display: table-cell;
    font-size: 22px;
    text-align: center;
    vertical-align: middle;
    padding: 40px 30px;
}

.inner-wrapper {
    display: inline-block;
}

.top-title {
    color: #4384F5;
    font-size: 35px;
    font-weight: 700;
    margin-bottom: 25px;
}

.main-title {
    line-height: 0;
    font-size: 90px;
    font-weight: 800;
    color: #4384F5;
}

.svg-wrap {
    display: inline-block;
    font-size: 0;
    vertical-align: super;
}

#lego {
    padding: 5px;
}

.blurb {
    margin-top: 30px;
    color: #777;
}

.lego-btn {
    background: #ff9130;
    border-radius: 4px;
    color: #ffffff;
    display: inline-block;
    margin-top: 30px;
    padding: 7px 20px;
    font-size: 16px;

    &:hover {
        color: #ffffff;
    }
}

@media only screen and (max-width: 440px) {
    .main-title {
        font-size: 60px;
    }

    #lego {
        width: 50px;
        height: 55px;
        padding: 10px 5px;
    }

    .blurb {
        font-size: 20px;
    }
}
