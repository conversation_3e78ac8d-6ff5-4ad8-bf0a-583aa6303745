// footer
.social-icons li {
    float: left;
    width: 30px;
    height: 30px;
    margin: 0 2px;
    -moz-transition: all .3s ease;
    -ms-transition: all .3s ease;
    -o-transition: all .3s ease;
    -webkit-transition: all .3s ease;
    transition: all .3s ease;

    a {
        display: block;

        i {
            line-height: 30px;
            text-align: center;
            color: #333;
            background-color: #ffffff;
            width: 100%;
            @include border-radius(50%);
        }
    }

    &.facebook {

        &:hover,
        &:focus,
        &.active {
            a i {
                background-color: #3a5897;
                color: #ffffff;
            }
        }
    }

    &.twitter {

        &:hover,
        &:focus,
        &.active {
            a i {
                background-color: #00bdec;
                color: #ffffff;
            }
        }
    }

    &.googleplus {

        &:hover,
        &:focus,
        &.active {
            a i {
                background-color: #d94a3a;
                color: #ffffff;
            }
        }
    }

    &.linkedin {

        &:hover,
        &:focus,
        &.active {
            a i {
                background-color: #1a92bd;
                color: #ffffff;
            }
        }
    }
}

.footer-area {
    position: relative;
    z-index: 997;
}

footer {
    .social-icons-top {
        .social-icons {
            li {
                text-indent: inherit;
                margin: 0 10px 0 0;
                width: 36px;
                height: 36px;

                a {
                    i {
                        line-height: 36px;
                        font-size: 18px;
                    }
                }
            }
        }
    }

    .footer {
        padding: 40px 0 0 0;
        background: #272727;
        color: #ffffff;

        a {
            color: #ffffff;
            font-weight: 500;

            &:hover,
            &:focus,
            &.active {
                color: #f7941d;
            }
        }

        .headline {
            margin: 0;
            border: none;

            h2 {
                font-size: 20px;
                margin-bottom: 35px;
                color: #ffffff;
                font-weight: normal;
            }
        }
    }

    .row {
        margin-left: -15px;
        margin-right: -15px;

        .col-xs-2 {
            margin: 5px 0;
        }
    }

    .copyright {
        background: #222;
        border-top: none;
        padding: 10px 0 5px;
        margin-top: 25px;

        p {
            float: left;
            line-height: 30px;
            margin: 0;
        }

        .txt-footer-1 {
            margin-left: 30px;

            a {
                font-weight: normal;
            }
        }
    }

    .text-justify {
        text-align: justify;
        margin-top: 10px;
    }

    .footer-logo {
        margin-bottom: 20px;
    }

    p {
        line-height: 1.4;
    }

    .address,
    .phone,
    .email {
        line-height: 20px;
        display: block;
        clear: both;
        overflow: hidden;

        i {
            line-height: 20px;
            padding-right: 20px;
            float: left;
            display: inline-block;

            &.fa-map-marker {
                font-size: 20px;
            }

            &.fa-mobile {
                font-size: 23px;
            }

            &.fa-envelope {
                padding-right: 15px;
            }
        }
    }

    .list-unstyled li {
        width: 100%;
        display: block;
        margin: 0 0 15px 0;
    }
}

.footer-wrap {
    background: #272727;
    color: #ffffff;
    padding: 30px 0 0;

    h4 {
        font-size: 16px;
        margin: 0;
        padding: 0 0 15px;
        text-transform: uppercase;

        i { display: none; }
    }

    a {
        color: #ffffff;

        &:hover {
            text-decoration: underline;
        }
    }

    .container {
        &>.row {
            &>[class*="col-"] {
                margin-bottom: 15px;
            }
        }
    }

    ul {
        li {
            margin-bottom: 10px;

            img {
                width: 122px;
                height: 39px;
            }
        }

        &.bottom {
            margin: 20px 0 0;

            li {
                .txt {
                    color: #999;
                    font-size: 15px;
                    font-weight: normal;
                    margin: 0;
                }

                .node {
                    font-size: 16px;
                }
            }
        }
    }
}

.copyright {
    background: #222;
    padding: 15px 0;
    color: #ffffff;
    font-size: 12px;
}

@media only screen and (max-width: 767px) {
    .footer-wrap {
        padding: 15px 0 0;

        .logo {
            margin-bottom: 10px !important;
        }

        .bottom {
            margin: 5px 0 12px !important;

            li {
                margin-bottom: 5px !important;
            }
        }

        h4 {
            font-size: 14px !important;
            padding: 5px 0 15px;

            i { display: inline-block; }
        }

        [class*="col-"] {
            margin-bottom: 0px !important;
        }
    }

    .copyright {
        font-size: 10px;
    }
}
