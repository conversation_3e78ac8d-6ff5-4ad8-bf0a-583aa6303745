$spacer: 0.25 !default;
$spacers: 5, 10, 15, 20, 30 !default;

@function space($index) {
  @return $spacer * $index * 1rem;
}

@for $i from 0 through 4 {
  @each $dir, $abbr in (left: l, right: r, top: t, bottom: b) {
    .m#{$abbr}-#{$i} {
      margin-#{$dir}: space($i) !important;
    }
  }
}

@for $i from 0 through 4 {
  @each $dir, $abbr in (left: l, right: r, top: t, bottom: b) {
    .p#{$abbr}-#{$i} {
      padding-#{$dir}: space($i) !important;
    }
  }
}

@for $i from 1 through 8 {
  .gap-#{$i} {
    gap: space($i) !important;
  }
}

@each $size in $spacers {
  .m-#{$size} {
    margin: #{$size}px !important;
  }

  @each $dir, $abbr in (top: t, right: r, bottom: b, left: l) {
    .m#{$abbr}-#{$size} {
      margin-#{$dir}: #{$size}px !important;
    }
  }
}

@each $size in $spacers {
  .p-#{$size} {
    padding: #{$size}px !important;
  }

  @each $dir, $abbr in (top: t, right: r, bottom: b, left: l) {
    .p#{$abbr}-#{$size} {
      padding-#{$dir}: #{$size}px !important;
    }
  }
}

.m-0 {
    margin: 0 !important;
}

.p-0 {
    padding: 0 !important;
}

.bg-white {
    background-color: #ffffff !important;
}

.bg-transparent {
    background-color: transparent !important;
}

.bg-orange {
    background-color: #ff9e4a !important;
    background-image: url(/images/bg_tab.png);
    background-position: 50%;
    color: #ffffff;
}

.d-flex {
    display: flex;
}

.flex-wrap {
    flex-wrap: wrap;
}

.f-right {
    float: right;
}

.error-message {
    position: absolute;
    font-size: 13px;
    margin-top: 2px;
    color: red;
}

.cursor-no-drop {
    cursor: not-allowed !important;
    background: #f6f6f6 !important;
    color: #000 !important;
    border-bottom: 1px solid #ffffff !important;

    .icon-default {
        color: #000 !important;
    }

    a {
        cursor: not-allowed !important;
    }

    &.disabled {
        background: #ff00000d !important;
    }
}

// sticky of col row
@media all and (min-width: 676px) {
    .flexbox.row {
        display: table !important;

        [class*="col-"] {
            float: none !important;
            display: table-cell !important;
            vertical-align: top !important;
        }
    }
}

.bottom-sticky-position {
    position: sticky;
    bottom: 50px;
    align-self: flex-end;
    z-index: 2;
}

.top-sticky-position {
    position: sticky;
    top: 50px;
    align-self: flex-start;
    z-index: 2;
}

@media all and (max-width: 767px) {
    .bottom-sticky-position, .top-sticky-position {
        position: static;
    }
}
// end

.fixed-top {
    position: fixed;
    z-index: 998;
    width: 100%;
    right: 0;
    left: 0;
    top: 0;
}

.center-block {
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.overflow-x-el {
    overflow-x: auto;

    &::-webkit-scrollbar {
        height: 4px;
        width: 4px;
    }

    &::-webkit-scrollbar-thumb {
        border-radius: 1px;
        background: #ff940e;
    }
}

.overflow-y-el {
    overflow-x: auto;

    &::-webkit-scrollbar {
        height: 4px;
        width: 4px;
    }

    &::-webkit-scrollbar-thumb {
        border-radius: 1px;
        background: #ff940e;
    }
}

iframe {
    max-width: 100%;
}

.btn-danger-2 {
    color: #ffffff !important;
    background-color: #e91e30;
    border-color: #e91e30;
}

.w-100 {
    width: 100% !important;
}

.h-100 {
    height: 100% !important;
}

.fontB{font-weight: 700!important}
.fontM{font-weight: 500!important}
.fontR{font-weight: 400!important}
.fontL{font-weight: 300!important}

@for $i from 12 through 20 {
  .fs-#{$i} {
    font-size: #{$i}px !important;
    line-height: 1.4em;
  }
}

img {
  max-width: 100%;
  height: auto;
}

.overflow {
  overflow: hidden;
}

.clearfix {
  clear: both;
  overflow: hidden;
}

.d-none {
    display: none !important;
}

.d-block {
    display: block !important;
}

@media (min-width: 768px) {
    .d-md-block {
        display: block !important;
    }

    .d-md-none {
        display: none !important;
    }
}

@media only screen and (max-width: 500px) {
    .p-sm-0 {
        padding: 0 5px !important;
    }

    .m-sm-0 {
        margin: 0 5px !important;
    }
}

.border-0, .b-0 {
    border: none !important;
}

.bg-light {
    background-color: #f8f9fa !important;
}

ul.list-initial li {
    list-style: initial;
    margin-left: 30px;
}

ul.list-circle li {
    list-style: circle;
    margin-left: 30px;
}

.text-underline:hover {
    text-decoration: underline !important;
}

.d-inline-block {
    display: inline-block !important;
}

.cursor-pointer {
    cursor: pointer;
}

.bb-1 {
    border-bottom: 1px solid #dedede;
}

.bt-1 {
    border-top: 1px solid #e8eaed;
}
