.table {
    width: 100%;
    max-width: 100%;
    margin-bottom: 2rem;
    background-color: #ffffff;
    border: 1px solid #CFD8DC;
}

.table>thead>tr,
.table>tbody>tr,
.table>tfoot>tr {
    -webkit-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease;
}

.table>thead>tr>th,
.table>thead>tr>td,
.table>tbody>tr>th,
.table>tbody>tr>td,
.table>tfoot>tr>th,
.table>tfoot>tr>td {
    text-align: left;
    padding: 1.6rem !important;
    vertical-align: top;
    -webkit-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease;
}

.table>thead>tr>th {
    font-weight: 400;
    color: #757575;
    vertical-align: middle;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.table>caption+thead>tr:first-child>th,
.table>caption+thead>tr:first-child>td,
.table>colgroup+thead>tr:first-child>th,
.table>colgroup+thead>tr:first-child>td,
.table>thead:first-child>tr:first-child>th,
.table>thead:first-child>tr:first-child>td {
    border-top: 0;
}

.table>tbody+tbody {
    border-top: 1px solid rgba(0, 0, 0, 0.12);
}

.table .table {
    background-color: #ffffff;
}

.table .no-border {
    border: 0;
}

.table-bordered {
    border: 0;
}

.table-bordered>thead>tr>th,
.table-bordered>thead>tr>td,
.table-bordered>tbody>tr>th,
.table-bordered>tbody>tr>td,
.table-bordered>tfoot>tr>th,
.table-bordered>tfoot>tr>td {
    border: 0;
    border-bottom: 1px solid #e0e0e0;
}

.table-bordered>thead>tr>th,
.table-bordered>thead>tr>td {
    border-bottom-width: 2px;
}

.table-striped>tbody>tr:nth-child(odd)>td,
.table-striped>tbody>tr:nth-child(odd)>th {
    background-color: #f5f5f5;
}

.table-hover>tbody>tr:hover>td,
.table-hover>tbody>tr:hover>th {
    background-color: rgba(0, 0, 0, 0.12);
}

@media screen and (max-width: 768px) {
    .table-responsive-vertical>.table {
        margin-bottom: 0;
        background-color: transparent;
    }

    .table-responsive-vertical>.table>thead,
    .table-responsive-vertical>.table>tfoot {
        display: none;
    }

    .table-responsive-vertical>.table>tbody {
        display: block;
    }

    .table-responsive-vertical>.table>tbody>tr {
        display: block;
        border: 1px solid #e0e0e0;
        border-radius: 2px;
        margin-bottom: 1.6rem;
    }

    .table-responsive-vertical>.table>tbody>tr>td {
        background-color: #ffffff;
        display: block;
        vertical-align: middle;
        text-align: right;
    }

    .table-responsive-vertical>.table>tbody>tr>td[data-title]:before {
        content: attr(data-title);
        float: left;
        font-size: inherit;
        font-weight: 400;
        color: #757575;
    }

    .table-responsive-vertical.shadow-z-1 {
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        box-shadow: none;
    }

    .table-responsive-vertical.shadow-z-1>.table>tbody>tr {
        border: none;
    }

    .table-responsive-vertical>.table-bordered {
        border: 0;
    }

    .table-responsive-vertical>.table-bordered>tbody>tr>td {
        border: 0;
        border-bottom: 1px solid #e0e0e0;
    }

    .table-responsive-vertical>.table-bordered>tbody>tr>td:last-child {
        border-bottom: 0;
    }

    .table-responsive-vertical>.table-striped>tbody>tr>td,
    .table-responsive-vertical>.table-striped>tbody>tr:nth-child(odd) {
        background-color: #ffffff;
    }

    .table-responsive-vertical>.table-striped>tbody>tr>td:nth-child(odd) {
        background-color: #f5f5f5;
    }

    .table-responsive-vertical>.table-hover>tbody>tr:hover>td,
    .table-responsive-vertical>.table-hover>tbody>tr:hover {
        background-color: #ffffff;
    }

    .table-responsive-vertical>.table-hover>tbody>tr>td:hover {
        background-color: rgba(0, 0, 0, 0.12);
    }
}

.table-striped.table-mc-light-blue>tbody>tr:nth-child(odd)>td,
.table-striped.table-mc-light-blue>tbody>tr:nth-child(odd)>th {
    background-color: #e1f5fe;
}

.table-hover.table-mc-light-blue>tbody>tr:hover>td,
.table-hover.table-mc-light-blue>tbody>tr:hover>th {
    background-color: #b3e5fc45;
}

@media screen and (max-width: 767px) {
    .table-responsive-vertical .table-striped.table-mc-light-blue>tbody>tr>td,
    .table-responsive-vertical .table-striped.table-mc-light-blue>tbody>tr:nth-child(odd) {
        background-color: #ffffff;
    }

    .table-responsive-vertical .table-striped.table-mc-light-blue>tbody>tr>td:nth-child(odd) {
        background-color: #e1f5fe;
    }

    .table-responsive-vertical .table-hover.table-mc-light-blue>tbody>tr:hover>td,
    .table-responsive-vertical .table-hover.table-mc-light-blue>tbody>tr:hover {
        background-color: #ffffff;
    }

    .table-responsive-vertical .table-hover.table-mc-light-blue>tbody>tr>td:hover {
        background-color: #b3e5fc45;
    }
}
