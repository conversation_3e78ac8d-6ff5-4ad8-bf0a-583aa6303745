.box-panel-index {
    border: unset;

    .panel-heading {
        background: #ff9700;

        .panel-title {
            color: #ffffff;
        }
    }

    .panel-body {
        padding-top: 0;
        margin-top: 10px;

        .list-group {
            border: 1px dashed #9b9292;
            padding: 10px 10px;
            margin-bottom: 0;

            .list-group-item {
                border: unset;
                text-transform: uppercase;
            }
        }
    }
}

span.icon-edu {
    width: 100%;
    background: rgba(255, 193, 7, .231);
    color: #475fe6;
    display: flex;
    align-items: center;
    font-size: 18px;
    line-height: 1.5;
    text-transform: uppercase;

    > span {
        background: rgb(253 144 15);
        color: #ffffff;
    }

    > * {
        padding: 10px;
    }
}

.post-header {
    display: flex;
    align-items: center;
    gap: 15px;
}

.post-thumbnail {
    background-color: #fff;
    border: 1px solid #fff;
    height: auto;
    width: 70px;
}

.post-title {
    font-size: 25px;
    color: #000;
    line-height: 1.4;
}

.post-des {
    text-align: justify;
    line-height: 26px;
    font-size: 18px;
}

.post-main {
    padding: 0 10px;

    &  ~ .rsidebar {
        min-width: 0;

        &:has(.toc-nav-list li) {
            min-width: 270px;
        }
    }

    .post-content {
        font-size: 18px;
        line-height: 24px;
        overflow-x: auto;

        table {
            margin: 20px auto;

            th, td {
                padding: 10px !important;
            }
        }

        *:not([class*='MJXc-TeX-']) {
            font-family: "Open Sans", Arial, sans-serif !important;
        }

        p,
        span {
            text-indent: 0 !important;
        }

        .mjx-chtml {
            line-height: 0 !important;
        }

        p,
        p>*,
        ul,
        li {
            line-height: 26px !important;
            font-size: 18px;
        }

        ul li {
            list-style: initial;
        }

        img {
            margin: 20px auto;
        }
    }

    .pathway {
        height: 46px;
        padding: 4px 10px 0;
        border-bottom: 1px dotted #9f9fa0;
        margin-bottom: 10px;

        h1 {
            float: left;
            background: #ffffff;
            padding-right: 15px;
            color: #036;
            font-size: 18px;
            text-transform: uppercase;
            font-weight: 700;
        }

        .pattern {
            margin-top: 15px;
        }
    }

    .title-pop {
        background: #0ba6b7;

        h2 {
            font-size: 16px;
            line-height: 20px;
            padding: 6px;
            margin: 0;
        }
    }

    .box-list {
        background: #fcf9eb;
        border-radius: 0 0 3px 3px;
        border: 1px solid #f7f4e5;
        margin: 0 10px 15px;
    }

    .box_newss {
        padding: 8px 2px 0;

        ul.list li {
            border-bottom: none !important;

            .img_130 {
                width: 192px;
                height: 140px;
                margin-right: 15px;
            }

            h3 {
                margin-bottom: 5px;
            }
        }
    }

    .box-cont {
        padding: 5px 10px;

        ul.list li {
            border-bottom: 1px dotted #9f9fa0;
            margin: 0;
            padding-top: 20px;
            width: 100%;

            a {
                img {
                    margin: 0 12px 30px 0;
                }
            }
        }
    }

    ul.list li {
        float: left;
        padding: 8px 0;

        h3 {
            overflow: hidden;
            margin-top: 0;
            margin-bottom: 5px;

            a {
                color: #333;
                font-size: 18px;
                text-decoration: none;
            }
        }
    }

    .des_news {
        color: #666;
        margin: 10px 0;
        overflow: hidden;
        line-height: 1.4;
    }

    .img-132 {
        max-width: 132px;
        max-height: 96px;
        float: left;
    }

    .img-132,
    .img_130 {
        background: #e3e3e3;
        border: 1px solid #ffffff;
        box-shadow: 0 0 5px 0 #ccc;
        height: 95px;
        margin-bottom: 6px;
        margin-right: 8px;
        padding: 1px;
        width: 130px;
        object-fit: contain;
    }

    .title-style {
        height: 36px;
        margin: 0 0 10px;
        border-bottom: 3px solid #ff9e2c;
        color: #000;
        font-size: 18px;
        text-transform: uppercase;
    }

    ul.list-col {
        li {
            padding: 4px 0;
            width: 100%;

            a {
                background: url('/images/icons/dot.png') 0 12px no-repeat;
                max-height: 50px;
                min-height: 40px;
                overflow: hidden;
                line-height: 24px;
                color: #333;
                display: block;
                padding: 3px 0 5px 12px;

                &:hover {
                    color: #ff5722;
                }
            }
        }

        li.last {
            border-bottom: none;
        }
    }

    @media screen and (max-width: 768px) {
        .img_100 {
            max-width: 100px;
            margin: 0 10px 8px 0;

            img {
                width: 100%;
                max-height: 65px;
            }
        }
    }
}

.toolbox {
    margin: 20px 0;
}

.view-all-school {
    text-align: center;
    margin: 0 auto;

    .btn-view-all {
        letter-spacing: 1.25px;
        transition: all 0.3s ease 0s;
        font-size: 14px;
        line-height: 1.4;
        border: 1px solid #2f80ed;
        border-radius: 5px;
        color: #2f80ed;
        display: block;
        margin: auto;
        padding: 8px 5px;
        text-align: center;
        font-weight: 600;
        max-width: 300px;

        &:after {
            content: " »";
        }

        &:hover {
            color: #ff7e32;
            border: 1px solid #ff7e32;
            text-decoration: none;
        }
    }

    .btn-view-all::after {
        content: " »";
    }
}

.related-school {
    .text-related {
        font-weight: 600;
    }

    .list-enroll {
        .owl-stage-outer {
            display: flex;
            width: 100%;
            overflow-x: auto;
            padding-bottom: 5px;

            .box-product {
                color: #666666;

                &:hover {
                    text-decoration: none;

                    h3 {
                        color: #00206B;
                    }
                }
            }

            .item-img {
                img {
                    width: 100%;
                    border-radius: 5px 5px 0 0;
                    aspect-ratio: 4/3;

                    &:hover {
                        opacity: 0.8;
                    }
                }
            }

            h3 {
                font-size: 17px;
                line-height: 24px;
                color: #333333;
                padding: 10px;
                margin: 5px 0;
                font-weight: 500;
                text-align: center;
            }

            &::-webkit-scrollbar {
                width: 7px;
                height: 7px;
            }

            &::-webkit-scrollbar-thumb {
                border-radius: 3px;
                background: #e5e5e5;
            }

            &:hover {
                &::-webkit-scrollbar-thumb {
                    background: #dee0e1;
                }
            }
        }

        .owl-item {
            border: 1px solid #f1f1f1;
            padding: 0;
            width: 25%;
            min-width: 220px;
            border-radius: 5px;

            &:hover {
                box-shadow: 0 0 6px 0 #e5e5e5;
            }
        }
    }
}

@media only screen and (min-width: 1600px) {
    .related-school {
        .list-enroll {
            .owl-item {
                width: 23%;
            }
        }
    }
}

.year {
    min-height: 50px;

    form {
        display: inline-flex;
        float: right;

        strong {
            padding-right: 10px;
        }
    }

    .select-year {
        width: 85px;
        background: #e9e9e9;
        font-style: italic;
        padding: 0;
        height: auto;
    }
}

.detail-action {
    display: flex;
    justify-content: space-between;

    .next-btn, .prev-btn {
        border-radius: 40px;
        display: inline-table;
        font-size: 14px;
        font-weight: 400;
        height: 35px;
        line-height: 32px;
        padding: 0 15px;
        text-align: center;
        width: 130px;
        display: none;

        &:hover {
            color: #ffffff !important;
        }
    }

    .mr-2, .mx-2 {
        margin-right: 0.5rem!important;
    }
}

.fb-action {
    .fb_iframe_widget span {
        height: 20px !important;
    }
}

.pattern {
    background: url('/images/icons/pattern.png') repeat-x;
    display: block;
    margin-top: 20px;
}

.text-limit-2-row {
    -webkit-line-clamp: 2 !important;
    -webkit-box-orient: vertical;
    display: -webkit-box !important;
    overflow: hidden !important;
}

.dsearch-area {
    color: #ffffff;
    background: #a0cfff;
    padding: 15px 0;
    position: relative;
    height: 200px;
    width: 100%;

    .dsearch-banner {
        object-fit: cover;
        position: absolute;
        top: 0;
        width: 100%;
        height: 100%;
    }

    .dsearch-overlay {
        background-color: rgba(0, 0, 0, .8);
        bottom: 0;
        display: block;
        left: 0;
        position: absolute;
        right: 0;
        top: 0;
        z-index: 1;
        opacity: 0.3;
        height: 100%;
        width: 100%;
    }
}

.dsearch-box {
    height: 100%;
    position: relative;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;

    form {
        width: 100%;
        // max-width: 850px;
        margin-bottom: 0;
        position: relative;

        .dsearch-results {
            background-color: #ffffff;
            border: 1px solid #ccc;
            border-radius: 3px;
            border-top: none;
            box-shadow: rgba(0, 0, 0, 0.25) 0px 25px 50px -12px;
            display: none;
            margin-top: 5px;
            padding: 10px 0;
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            z-index: 301;
        }

        .dsearch-form {
            background: #fff;
            display: -ms-flexbox;
            display: flex;
            width: 100%;
            -ms-flex-pack: justify;
            justify-content: space-between;
            -ms-flex-align: center;
            align-items: center;
            box-shadow: rgba(0, 0, 0, 0.05) 0px 0px 0px 1px;
            border-radius: 3px;

            .dsearch-select {
                background: transparent;
                border-radius: 0;
                border: 0;
                height: 100%;
                color: #fff;
                display: -ms-flexbox;
                display: flex;
                -ms-flex-align: center;
                align-items: center;
                padding: 10px 0;

                select {
                    position: relative;
                    border: none;
                    outline: none;
                    font-size: 16px;
                    font-weight: 500;
                    color: #000;
                    padding: 20px 15px;
                    appearance: none;
                    width: 100%;
                    background: transparent;
                }
            }

            .dsearch-field {
                height: 55px;

                input {
                    height: 100%;
                    background: transparent;
                    border: 0;
                    display: block;
                    width: 100%;
                    padding: 10px 30px;
                    font-size: 16px;
                    color: #555;

                    &.placeholder,
                    &:-moz-placeholder,
                    &::-webkit-input-placeholder {
                        color: #888;
                        font-size: 16px;
                    }

                    &:hover,
                    &:focus {
                        box-shadow: none;
                        outline: 0;
                        border-color: #fff;
                    }
                }

                &.first-wrap {
                    width: 200px;
                    border-right: 1px solid rgba(0, 0, 0, 0.1);
                }

                &.second-wrap {
                    -ms-flex-positive: 1;
                    flex-grow: 1;
                }

                &.third-wrap {
                    width: 74px;
                    padding: 5px;

                    .btn-dsearch {
                        height: 100%;
                        width: 100%;
                        white-space: nowrap;
                        color: #fff;
                        border: 0;
                        cursor: pointer;
                        background: #607d8b;
                        transition: all .2s ease-out, color .2s ease-out;
                        font-size: 20px;
                        border-radius: 3px;

                        &:hover {
                            background: #376379;
                        }

                        &:focus {
                            outline: 0;
                            box-shadow: none;
                        }
                    }
                }
            }

            // Media queries bên trong để dễ quản lý phạm vi ảnh hưởng
            @media screen and (max-width: 992px) {
                .dsearch-field {
                    height: 40px;
                }
            }

            @media screen and (max-width: 767px) {
                -ms-flex-wrap: wrap;
                flex-wrap: wrap;
                padding: 4px;
                box-shadow: none;

                .dsearch-field {
                    margin-bottom: 5px;
                    border-bottom: 1px solid rgba(0, 0, 0, 0.1);

                    input {
                        padding: 5px 10px;
                    }

                    .dsearch-select select {
                        padding: 5px 10px;
                    }

                    &.first-wrap {
                        width: 100%;
                        border-right: 0;
                    }

                    &.second-wrap {
                        width: 100%;
                        margin-bottom: 15px;

                        input {
                            border: 1px solid rgba(255, 255, 255, 0.3);
                        }
                    }

                    &.third-wrap {
                        width: 100%;
                        margin-bottom: 0;
                        padding: 0;
                    }
                }
            }
        }
    }
}
