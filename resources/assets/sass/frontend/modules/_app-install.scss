.app-install {
    height: 55px;
    text-align: center;
    background: rgba(0, 176, 255, 0.1);
    border-bottom: 1px solid #eee;

    .app-install-box {
        display: inline-flex;
        padding: 5px 0;
    }

    .btn-close {
        font-size: 12px;
        cursor: pointer;
        padding: 10px;
        line-height: 26px;
        color: #999;
    }

    .app-avatar {
        max-width: 50px;
        height: auto;
        margin: 3px;

        img {
        	max-height: 40px;
    		width: 50px;
            border-radius: 5px;
        }
    }

    .app-title {
        padding: 0 5px;

        h6 {
            color: #000;
            margin: 0;
            text-transform: capitalize;
            line-height: 1.4;
            font-size: 10px;
            font-weight: 400;
            text-align: left;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            height: 30px;
        }

        .stars-box {
            display: flex;
            font-size: 11px;

            .stars {
                display: flex;
                color: #ff9700;
            }

            .number-install {
                font-size: 10px;
                font-weight: 400;
            }
        }
    }

    .app-btn {
        a {
            color: #ffffff;
            background: red;
            background-image: none;
            border: none;
            position: relative;
            top: 20%;
            padding: 5px 10px;
            margin: 0 10px;
            font-size: 12px;
            font-weight: 900;
        }
    }
}
