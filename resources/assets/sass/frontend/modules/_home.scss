.box-home {
    background: #b8dbe6 0 0 repeat;
    position: relative;
    overflow: hidden;
    min-height: 450px;
    background-image: url('/images/home-bg.png'), repeating-linear-gradient(180deg, #b8dbe6 0, #c2decd 2500px, #b8dbe6 5000px);
    padding: 40px 0 0;
}

.hometop {
    position: relative;
    height: 250px;
    max-height: 250px;
    min-height: 250px;
    width: 100%;
}

.hometop-banner {
    object-fit: cover;
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
}

.bg-overlay {
    background-color: rgba(0, 0, 0, .8);
    bottom: 0;
    display: block;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 1;
    opacity: 0.3;
    height: 100%;
    width: 100%;
}

.hometop-box {
    height: 100%;
    position: relative;
    z-index: 2;
    padding: 20px;
}

.hometop-title {
    font-size: 28px;
    text-transform: capitalize;
    color: red;
    text-align: center;
    font-weight: 700;
    padding: 0 20px;
    text-shadow: 3px -2px 0 #ffffff, -4px -1px 0 #ffffff, -4px 0 0 #ffffff, -4px 1px 0 #ffffff, -4px 2px 0 #ffffff, -3px -3px 0 #ffffff, -3px -2px 0 #ffffff, -3px -1px 0 #ffffff, -3px 0 0 #ffffff, -3px 1px 0 #ffffff, -3px 2px 0 #ffffff, -3px 3px 0 #ffffff, -2px -4px 0 #ffffff, -2px -3px 0 #ffffff, -2px -2px 0 #ffffff, -2px -1px 0 #ffffff, -2px 0 0 #ffffff, -2px 1px 0 #ffffff, -2px 2px 0 #ffffff, -2px 3px 0 #ffffff, -2px 4px 0 #ffffff, -1px -4px 0 #ffffff, -1px -3px 0 #ffffff, -1px -2px 0 #ffffff, -1px -1px 0 #ffffff, -1px 0 0 #ffffff, -1px 1px 0 #ffffff, -1px 2px 0 #ffffff, -1px 3px 0 #ffffff, -1px 4px 0 #ffffff, 0 -4px 0 #ffffff, 0 -3px 0 #ffffff, 0 -2px 0 #ffffff, 0 -1px 0 #ffffff, 0 0 0 #ffffff, 0 1px 0 #ffffff, 0 2px 0 #ffffff, 0 3px 0 #ffffff, 0 4px 0 #ffffff, 1px -4px 0 #ffffff, 1px -3px 0 #ffffff, 1px -2px 0 #ffffff, 1px -1px 0 #ffffff, 1px 0 0 #ffffff, 1px 1px 0 #ffffff, 1px 2px 0 #ffffff, 1px 3px 0 #ffffff, 1px 4px 0 #ffffff, 2px -4px 0 #ffffff, 2px -3px 0 #ffffff, 2px -2px 0 #ffffff, 2px -1px 0 #ffffff, 2px 0 0 #ffffff, 2px 1px 0 #ffffff, 2px 2px 0 #ffffff, 2px 3px 0 #ffffff, 2px 4px 0 #ffffff, 3px -3px 0 #ffffff, 3px -2px 0 #ffffff, 3px -1px 0 #ffffff, 3px 0 0 #ffffff, 3px 1px 0 #ffffff, 3px 2px 0 #ffffff, 3px 3px 0 #ffffff, 4px -2px 0 #ffffff, 4px -1px 0 #ffffff, 4px 0 0 #ffffff, 4px 1px 0 #ffffff, 4px 2px 0 #ffffff;

    span {
        text-shadow: none;
        color: #9C27B0;
        font-size: 22px;
        font-weight: 600;
        letter-spacing: normal;
        position: relative;
    }
}

.hometop-btn {
    text-decoration: none;
    background-color: #52b700;
    border-radius: 20px;
    color: #ffffff !important;
    cursor: pointer;
    font-size: 16px;
    line-height: 20px;
    box-shadow: 0 0 0 1px rgb(0 0 0 / 15%);
    align-items: center;
    display: inline-flex;
    padding: 10px 20px;
    gap: 10px;
    text-transform: capitalize;
    font-weight: 500;

    &:hover {
        background-color: #69c130;
        color: #ffffff;
    }
}

.hometop-action {
    max-width: 650px;
    width: 100%;
    margin: auto;

    .form-control {
        font-size: 18px;
        border: 2px solid #FF9800;
        height: 50px;
        display: flex;
        align-items: center;
    }

    .btn-activevip {
        border: none;
        height: 50px;
        font-size: 18px;
        background: #ff5722;
        color: #ffffff;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        &:hover, &:active, &:focus {
            background: #ff5722;
            color: #ffffff;
            border-color: transparent;
            outline: none;
        }
    }
}

.gradebox {
    border: 1px solid #adc8d5;
    border-radius: 5px;

    a {
        display: flex !important;
        align-items: center;
        gap: 15px;
        padding: 10px;
        height: 100%;
    }

    .gradebox-icon {
        flex-shrink: 0;
        max-width: 50px;

        img {
            width: 35px;
            height: auto;
        }
    }

    .gradebox-des {
        width: 100%;

        h3 {
            font-size: 16px;
            font-weight: 400;
            line-height: 1.3;
            margin-top: 0;
            color: #333;
            white-space: break-spaces;
        }

        .gradebox-info {
            // background: #eafffd;
            // padding: 7px 10px;
            // border-radius: 5px;

            p {
                color: #607D8B;
                font-size: 14px;
                line-height: 1.3;
                margin: 0;
                white-space: break-spaces;
            }
        }
    }

    &:hover {
        background: linear-gradient(135deg, #fffaf2, #fff2d9);

        .gradebox-des h3 {
            color: #ff5722;
        }
    }
}

.htitle {
    border-left: 5px solid #FF5722;
    padding-left: 15px;
    font-size: 20px;
    line-height: 1.3;
    font-weight: 500;
    margin-bottom: 20px;
    margin-top: 0;
}

.htitle2 {
    font-size: 20px;
    line-height: 1.3;
    font-weight: 500;
    margin-bottom: 20px;
    margin-top: 0;
    position: relative;

    &:before {
        background-color: #FF5722;
        content: "";
        height: 2px;
        width: 50px;
        position: absolute;
        left: 50%;
        bottom: -7px;
        transform: translateX(-50%);
    }
}

.row-flex {
    display: flex;
    flex-wrap: wrap;

    > [class^="col-"] {
        margin-bottom: 2.5rem;
        display: flex;
        align-items: stretch;

        @media only screen and (max-width: 500px) {
            margin-bottom: 1.5rem;
        }
    }
}

@media (max-width: 991px) {
  .row-md-swap {
    display: flex;
    flex-direction: column;
    flex-direction: column-reverse;
  }
}

.exambox {
    background: #ffffff;
    border: 1px solid #ffdc99;
    border-radius: 3px;
    display: flex;
    gap: 15px;
    padding: 10px 20px;
    position: relative;
    align-items: center;

    h3 {
        color: rgba(0, 0, 0, .88);
        margin: 10px auto;
        font-size: 18px;
        font-weight: 400;
        line-height: 26px;
        padding-right: 15px;
    }
}

.exambox-info {
    span {
        color: #607d8b;
        padding: 5px 10px;
        font-size: 14px;
    }
}

.exambox-icon {
    width: 90px;
}

.exambox-icontop {
    align-items: center;
    background: #ffffff;
    border-radius: 50%;
    display: grid;
    height: 32px;
    padding: 8px;
    width: 32px;
    color: #ffa800;
    position: absolute;
    right: 8px;
    top: 8px;
}

@media (min-width: 1024px) {
    .hometop-title {
        font-size: 34px;

        span {
            font-size: 28px;
        }
    }
}

.p-info {
    font-size: 12px;
    color: #777;
    margin: 5px 0 15px;

    span, small {
        display: inline-block;
    }
}

.meta-title {
    color: #586380;
    font-size: 17px;
    font-weight: 500;
    margin: 10px;
}

.p-meta {
    background-color: #edefff;
    color: #2e3856;
    padding: 3px 10px;
    display: inline-flex;
    align-items: center;
    border-radius: 12.5rem;
    gap: 7px;
    font-size: 13px;
    line-height: 1.2;

    i {
        color: #f6406c;
    }
}

.p-item {
    display: flex;
    align-items: baseline;
    gap: 10px;

    &.active {
        background: #79dd052e;
    }

    > i {
        color: #ff9700;
    }

    p {
        font-size: 14px;
        color: #444;
        line-height: 1.5;
        margin-bottom: 0;
    }

    .old-price {
        margin-left: 5px;
        text-decoration: line-through;
        font-size: 13px;
        font-weight: 500;
        color: #666;
    }

    .price {
        color: red;
        font-size: 14px;
        font-weight: 700;
    }
}

.l-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: .5rem;
    background: #ffffff;

    &:hover {
        background: #f0faff;
    }

    .l-icon {
        height: 40px;
        width: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: .5rem;
        border-radius: .5rem;
        background: #e6fcf4;
        color: #18ae79;
        flex-shrink: 0;
    }

    .l-content {
        flex: 1 1 auto;
        overflow: hidden;
    }

    .l-title {
        font-weight: 500;
        font-size: 16px;
        overflow: hidden;
        color: #282e3e;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-bottom: 5px;
    }

    .l-subtitle {
        font-weight: 400;
        font-size: 15px;
        color: #262626;
        margin-bottom: 5px;
    }

    .l-meta {
        display: flex;
        align-items: center;
        gap: 15px;
        color: #586380;
        font-size: 13px;

        @media (max-width: 500px) {
            gap: 8px;

            &.l-smeta {
                display: grid;
                gap: 2px;
            }
        }
    }
}

.home-cat {
    // @media (min-width: 1024px) {
    //     .main-subject {
    //         .subject-item {
    //             font-size: 16px;
    //             width: 170px;
    //             color: #555;

    //             .subject-item__image {
    //                 height: 45px;
    //             }
    //         }

    //         .active {
    //             .subject-item {
    //                 background: #fff59d;
    //                 border-top: 2px solid #ff5722;
    //             }
    //         }
    //     }
    // }

    .cat-icon {
        background: linear-gradient(135deg, #fffaf2, #fff2d9);
        border: 1px solid #09a3ab;
        border-radius: 5px;
        padding: 5px;
        width: 50px;
        height: 50px;
        flex-shrink: 0;

        img {
            height: auto;
            width: 100%;
        }
    }

    .cat-mega {
        background: #ffffff;
        border-radius: 5px;
        border: 1px solid #e5e5e5;
        padding: 20px 30px;
    }

    .cat-panel {
        margin: 0px;
        width: 100%;

        .panel-default {
            padding: 0px 0px 0px 0px;
            margin-bottom: 15px;

            & > .panel-heading {
                padding: 0px;
                background-color: transparent;
                position: relative;
            }
        }

        .panel-title > a {
            padding: 10px 40px 10px 20px;
            text-transform: uppercase;
            position: relative;
            box-shadow: 1px 5px 9px rgba(0, 0, 0, 0.1);
            border-top: 1px solid #ececec;
            display: flex;
            align-items: center;
            gap: 10px;

            h3 {
                font-size: 18px;
                font-weight: 500;
                line-height: 24px;
                color: #000;
                margin: 0;
            }
        }

        .panel-body {
            padding: 30px 30px 15px;
        }

        .panel-heading + .panel-collapse > .panel-body { border-top: none; }

        .panel-title > a {
            &:before {
                content: "\f078";
                font-family: "Font Awesome 5 Free";
                font-weight: 900;
                position: absolute;
                top: 50%;
                right: 20px;
                transform: translateY(-50%);
            }

            &[aria-expanded="true"]:before { content: "\f077"; }
        }

        .collapse.in {
            .htitle {
                position: sticky;
                top: 60px;
                background: #ffffff;
                padding-left: 15px;
                font-size: 20px;
                line-height: 1.3;
                font-weight: 500;
                margin-bottom: 20px;
                margin-top: 0;
                z-index: 10;
            }
        }
    }
}

.hsearch-area {
    // background: linear-gradient(89.9deg, #c9fae3 -137.66%, #e7f4d3 95.65%);
    background: linear-gradient(90deg, #e6f5ff, #e9fbf6);
    padding: 15px 0;
}

.hhistory-area {
    background: #ffffff;
    border-top: 1px solid #efefef;
    border-bottom: 1px solid #efefef;

    .nav-history {
        display: flex;
        justify-content: space-between;

        .nav-history_item {
            position: relative;
            z-index: 1;
            padding: 5px 10px;
            color: #00AEEF;
            font-size: 15px;

            .nav-history_link {
                position: relative;
                z-index: 2;
                display: inline-block;
                margin: 0;
            }

            &.active {
                color: #fff;
                background: #60c753;

                // &:after {
                //     background: no-repeat top / 100% 40px url('/images/tab-selected-bg.svg');
                //     content: " " / "";
                //     height: 40px;
                //     left: 0;
                //     position: absolute;
                //     top: 1px;
                //     width: 100%;
                //     z-index: 0;
                // }
            }
        }
    }

    @media only screen and (max-width: 500px) {
        >.container {
            padding: 0;
        }

        .nav-history {
            .nav-history_item {
                padding: 4px 10px;
                font-size: 14px;
            }
        }
    }
}

.list-history {
    background: #fff;
    padding-top: 2rem;

    .l-item {
        background: linear-gradient(135deg, #e8f2fc, #f6f9fc);

        &:hover {
            background: linear-gradient(135deg, #badaf7, #e8f2fc);
        }
    }
}

.contact-area {
    background: #ffffff;
    padding: 20px 0;
    margin-bottom: 20px;

    .contact-item {
        display: block;
        padding: 10px;
        background-color: #ffffff;
        border-radius: 5px;
    }
}

.pvip-section {
    background: linear-gradient(90deg, #e6f5ff, #e9fbf6);
    padding: 10px 5px;
}

.pvip-content {
    max-width: 1200px;
    margin: 0 auto;
}

.stats-list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    height: 100%;
    margin: 0 auto;
    position: unset;
    transition: unset;
    width: unset;
    overflow-x: hidden;
}

.stat-card {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: center;
    min-width: 300px;
    color: #0BC1B6;
    padding: 0;
}

@media (max-width: 500px) {
    .stat-card {
        min-width: 250px;
    }
}

.stat-card__thumbnail {
    height: 43px;
    width: 40px;
    flex: 0 0 auto;
    margin-right: 15px;
}

.stat-card__text {
    display: inline-block;
    flex: 0 1 auto;
    font-size: 16px;
    max-width: 185px;
    text-align: left;
    font-weight: 400;
}

.stat-card__strong {
    display: block;
    font-size: 22px;
    font-weight: 900;
    margin-bottom: 5px;
}

.text-color-1 {
    color: #0BC1B6;
}

.text-color-2 {
    color: #7A67D7;
}

.text-color-3 {
    color: #00AEEF;
}

.text-color-4 {
    color: #FF5722;
}
