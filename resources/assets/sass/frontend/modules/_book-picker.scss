.modal-book-picker {
    .modal-header {
        border-bottom: none;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
        background: #52b700;
        // background: linear-gradient(to right, rgb(68, 165, 1) 20%, rgb(52, 163, 212) 100%);
    }

    .modal-title {
        color: #ffffff;
        font-weight: 700;
        line-height: 20px;
        font-size: 18px;
    }

    .modal-body {
        padding: 0;

        .list-group {
            margin-bottom: 0;

            .list-group-item {
                border: 1px solid #eee;
            }
        }
    }

    .modal-footer {
        padding: 10px;
        border-top: none;

        p {
            color: #FF5722;
            font-size: 16px;
            font-weight: 700;
            margin: 0;

            i {
                color: #ff922e;
            }
        }
    }
}

.pick-book-box {
    padding: 12px;
    display: inline-block;
    border: 1px dashed #607D8B;
    background: #ffffff;
}

.pick-book-btn {
    padding: 0px 15px;
    background: #f2852a;
    border-radius: 20px;
    border: 1px solid #6dae40;
    margin-left: 10px;
    font-size: 15px;
    font-weight: 700;
    color: #ffffff !important;
    cursor: pointer;
    display: inline-block;
}
