@mixin toc-responsive {
  width: 100% !important;
  padding-bottom: 5px;

  .btn-show-toc,
  .btn-hide-toc {
    display: block;
  }

  .toc-nav-list {
    max-height: initial;

    li:nth-child(n+7) {
      display: none;
    }
  }

  .sidebar-toc {
    border: 1px solid #4285f461;
  }
}

.sidenav-toc {
    width: 0;
    padding-bottom: 100px;
    display: none;

    &:has(.toc-nav-list li) {
        display: block;
        width: 270px;
    }

    .sidebar-toc {
        border-left: 2px solid #4285f4;
    }

    .title {
        font-size: 15px !important;
        color: #073042;
        font-weight: 400;
        background: #4285f430;
        padding: 4px 10px;
        margin: 0 !important;
    }

    .btn-show-toc,
    .btn-hide-toc {
        font-size: 13px;
        color: #ff5722;
        text-align: center;
        margin: 0 auto;
        display: none;
        padding-bottom: 7px;

        i {
            font-size: 10px;
        }
    }

    .toc-nav-list {
        padding: 10px 10px 0;
        max-height: calc(100vh - 15rem);
        overflow-y: auto;
        overflow-x: hidden;
        list-style: none;
        margin: 0;

        &::-webkit-scrollbar {
            width: 7px;
        }

        &::-webkit-scrollbar-track {
            background: #ffffff;
        }

        &::-webkit-scrollbar-thumb {
            background: #dee0e1;
            border-radius: 5px;
        }

        .toc-nav-item {
            font-size: 15px !important;
            line-height: 24px;
            margin-bottom: 0.4rem;

            a {
                color: #073042;

                &.active {
                    color: #1769e0;
                    font-weight: 600;
                }

                &:hover {
                    color: #ff7c19;
                }
            }

            &.lv-item-1 {
                padding-left: 0;
            }

            &.lv-item-2 {
                padding-left: 0.75rem;
            }

            &.lv-item-3 {
                padding-left: 1.5rem;
            }
        }
    }

    &.toc-mobi {
        @include toc-responsive;
    }

    @media only screen and (max-width: 1250px) {
        @include toc-responsive;
    }
}

.toc-heading {
    background: #ffffff;

    &.highlight-target {
        border-radius: 5px;
        animation: highlight-text 1200ms normal ease-out;
    }
}

@keyframes highlight-text {
    0% {
        color: inherit;
        text-shadow: 0 0 10px #ffffff,
            0 0 20px #ffffff,
            0 0 40px #ffffff;
    }

    100% {
        color: #000;
        text-shadow: 0 0 10px #ff5722,
            0 0 20px #ff5722,
            0 0 40px #ff5722;
    }
}

.vj-template-markup {
    padding: 1rem 1rem 1rem 4rem;
    margin: 1rem 0;

    .vj-markup-text {
        margin: 0;
        font-size: 14px !important;
    }

    a {
        color: inherit;
        text-decoration: underline;
        font-size: 14px !important;
    }

    &::before {
        content: "\f111";
        float: left;
        margin-left: -20px;
        top: 10px;
        font-style: normal;
        font-variant: normal;
        font-weight: normal;
        font-size: 16px;
        font-family: FontAwesome;
    }

    &.keypoint {
        background-color: #e8eaf6;
        color: #3f51b5;

        &::before {
            content: "\f0eb";
        }
    }

    &.note {
        background-color: #e1f5fe;
        color: #01579b;

        &::before {
            content: "\f005";
        }
    }

    &.warning {
        background-color: #fce8e6;
        color: #d50000;

        &::before {
            content: "\f071";
        }
    }
}

//answer template
.vj-template-answer {
    margin: 5px 0 15px;
    position: relative;
    clear: both;

    >label {
        -moz-transition: all .15s ease-out;
        -o-transition: all .15s ease-out;
        -webkit-transition: all .15s ease-out;
        background: #f4f4f4;
        // border-left: 3px solid #CCC;
        // color: #000;
        color: #936927;
        display: block;
        font-size: 18px;
        min-height: 20px;
        padding: 12px 20px 12px 10px;
        position: relative;
        cursor: pointer;
        font-weight: 400;
        transition: all .15s ease-out;
        max-width: 100%;
        //
        border-left: none;
        background: #ddddff;
        border-radius: 3px;
        margin: 0;

        span,
        i {
            font-size: .7em;
            margin-right: 8px;
            position: relative;
            top: -1px;

            &.fa-plus {
                display: inline;
            }

            &.fa-minus {
                display: none;
            }
        }

        &::before {
            position: absolute;
            content: '';
            top: 50%;
            border-top-color: #6161e7 !important;
            border-left-color: transparent;
            border: 8px solid transparent;
            right: 15px;
            margin-top: -2px;
        }
    }

    .vj-answer-content {
        display: none;
        padding: 10px 20px;
        border: 1px solid #c2c2f3;
        border-top: none;
        border-radius: 0 0 3px 3px;

        .vj-answer-text {
            margin: 0 0.2em 0;
            padding: 0;
            text-align: justify;
            color: #000;

            &:not(:first-child) {
                margin-top: 1em;
            }
        }
    }

    &.active {
        >label {
            border-color: transparent;
            background: #c2c2f3;
            border-radius: 3px 3px 0 0;

            &::before {
                border: 8px solid transparent;
                margin-top: -12px;
                right: 15px;
                border-top-color: transparent !important;
                border-bottom-color: #6161e7 !important;
            }

            i,
            span {
                &.fa-plus {
                    display: none;
                }

                &.fa-minus {
                    display: inline;
                }
            }
        }

        .vj-answer-content {
            display: block;
        }
    }
}

@media only screen and (max-width: 500px) {
    .vj-template-answer {
        >label {
            font-size: 16px;
        }

        .vj-answer-content {
            .vj-answer-text {
                margin: 0 0.1em 0em;
                font-size: 14px !important;
            }
        }
    }

    .vj-template-markup {
        padding: .5rem 1rem .5rem 2rem;

        &::before {
            line-height: 1.4;
        }

        .vj-markup-text {
            line-height: 1.5;
        }
    }
}

.template-section {
    padding-bottom: 20px;

    table {
        max-width: 100% !important;

        td p {
            word-break: break-word;
        }
    }

    .section-content {
        //line-height: 1em;
        padding-top: 10px;
    }

    iframe {
        max-width: 100%;
    }

    .short-text {
        max-height: 25em;
        -webkit-box-orient: vertical;
        display: -webkit-box!important;
        overflow: hidden!important;
        -webkit-line-clamp: 10!important;
    }

    //.short-text.section-content-2 {
    //    max-height: 45em !important;
    //}
    //
    //.short-text.section-content-3 {
    //    max-height: 45em !important;
    //}

    .short-text.section-content-img {
        max-height: 45em !important;
    }

    .full-text {
        height: auto;
    }

    h2 {
        font-size: 2.2rem;
        font-weight: 700;
        line-height: 1.25;
        //color: #1A1D26;
        color: #1350a3;
        background: url('/images/icons/readbook_icon.png') left no-repeat;
        padding: 10px 5px 10px 50px;

        &:hover {
            color: #00206b;
        }

        span.toc-heading {
            background: unset;
        }
    }

    h3 {
        font-size: 1.25rem;
        font-weight: 700;
        //color: #ff6600;
    }

    ul {
        margin-left: 25px;

        li {
            span {
                line-height: 36px;
            }
        }
    }

    .show-more {
        text-align: center;
        max-width: 300px;
        margin: 0 auto;

        a {
            letter-spacing: 1.25px;
            transition: all 0.3s ease 0s;
            font-size: 14px;
            line-height: 1.4;
            border: 1px solid #2f80ed;
            border-radius: 5px;
            color: #2f80ed;
            display: block;
            margin: 10px auto;
            padding: 10px 5px;
            text-align: center;
            font-weight: 600;

            &:after {
                content: " »";
            }

            &:hover {
                color: #ff7e32;
                border: 1px solid #ff7e32;
            }
        }

        .btn-show-more::after {
            content: " »";
        }
    }
}
