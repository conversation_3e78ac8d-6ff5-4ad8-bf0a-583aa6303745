.box-title {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    position: relative;
    margin-top: 20px;
    margin-bottom: 15px;
    padding: 0 10px;

    .txt-title {
        font-weight: 700;
        color: #ff3860;
        font-size: 20px;
        text-transform: uppercase;
        margin: 0;
        line-height: 25px;

        @media (max-width: 600px) {
            font-size: 17px;
        }
    }

    .view-more {
        line-height: 25px;
        font-size: 14px;

        &:hover {
            color: #ff5722;
        }
    }
}

.scoure-item {
    background-color: #ffffff;
    border: 1px solid #B0BEC5;
    border-radius: 3px;
    padding: 20px 10px;
    margin-bottom: 30px;
    display: flex;
    gap: 20px;

    .scoure-img {
        display: block;
        overflow: hidden;
        max-width: 250px;
        margin-top: 10px;

        img {
            border-radius: 3px;
            width: 100%;
        }
    }

    .scoure-info {
        color: #111;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%; 
        gap: 50px;

        @media (max-width: 1200px) {
            flex-direction: column;
            align-items: baseline;
            gap: 10px;
        }

        .scoure-title {
            font-size: 20px;
            font-weight: 500;
            line-height: 1.3;
            margin-top: 0;
        }

        .scoure-meta {
            color: #607D8B;
            font-size: 14px;

            >*{
                margin-right: 20px;
                display: inline-block;
            }
        }

        .price {
            margin-left: auto;
        }
    }

    &.scoure-small {
        padding: 10px;
        margin-bottom: 20px;
        gap: 15px;

        .scoure-img {
            max-width: 130px;
        }

        .scoure-info {
            font-size: 14px;

            .scoure-title {
                font-size: 16px;
            }
        }
    }

    @media (max-width: 600px) {
        flex-direction: column;
        align-items: baseline;

        .scoure-img {
            max-width: inherit!important;
            width: 100%!important;
        }
    }
}

.course-item {
    width: 100%;
    padding: 7px;

    img.course_banner {
        display: none;
    }

    .course-inner {
        width: 100%;
        height: 100%;
        background-color: #ffffff;
        @include border-radius(3px);
    }

    .course-img {
        min-height: 120px;
        overflow: hidden;
        height: 100%;
        width: 100%;
        color: white;
        background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=) #eee no-repeat (center / cover);
        position: relative;
    }

    .course-text {
        height: 170px;
        padding: 10px 15px;
        display: inline-block;
        width: 100%;

        h3 {
            display: -webkit-box;
            margin: 8px 0;
            height: 47px;
            overflow: hidden;
            font-size: 17px;
            font-weight: 400;
            font-style: normal;
            font-stretch: normal;
            letter-spacing: -.24px;
            color: #464646;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;

            a {
                line-height: 1.5;
                color: #464646;
                font-weight: 600;
            }
        }

        p {
            max-height: 4.5em;
            overflow: hidden;
        }
    }

    .course_teacher a {
        display: flex;

        img {
            width: 30px !important;
            height: 30px;
            margin-right: 10px;
            border-radius: 50%;
            display: inline-block;
            border: 1px solid #E0E0E0;
        }

        span {
            margin-top: 4px;
            font-size: 13px;
            color: #9b9b9b;
            display: inline-block;
            max-width: 136px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-weight: 400;
            font-stretch: normal;
            line-height: 1.38;
            letter-spacing: -.08px;
            transform: translateY(3px);
        }
    }
}

.box-course {
    .owl-stage-outer {
        z-index: 1;
    }

    .owl-nav {
        color: #333;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;

        button {
            background: #ffffff !important;
            width: 30px !important;
            height: 30px !important;
        }

        svg {
            color: currentColor;
        }

        .disabled {
            display: none;
        }
    }

    .owl-prev,
    .owl-next {
        top: 130px;
        z-index: 2;
        width: 40px;
        height: 40px;
        padding: 8px;
        margin-top: -20px;
        position: absolute;
        border-radius: 50%;
        background-color: #ffffff;
        box-shadow: 0 4px 4px rgba(0, 0, 0, 0.3), 0 0 4px rgba(0, 0, 0, 0.2);
    }

    .owl-prev {
        left: -10px;
    }

    .owl-next {
        right: -10px;
    }
}

.box-star {
    display: inline-block;
    font-size: 14px;

    i {
        color: #FF9800 !important;
        margin-right: 2px;
    }

    .overflow {
        padding: 3px 0;

        p {
            font-size: 12px;
            color: $color-666;
            line-height: 18px;
            margin: 0;

            span {
                color: $color-999;
            }
        }
    }
}

.price {
    font-weight: 500;
    text-transform: none;

    span {
        font-size: 14px;
        color: $color-999;
        padding-right: 10px;
        display: inline-block;

        &:last-child {
            padding-right: 0;
        }

        &.ori {
            font-size: 20px;
            font-weight: 700;
            color: #FF5722;
        }

        &.old {
            text-decoration: line-through;
            color: #888888;
        }

        &.news {
            font-size: 28px;
            font-weight: 700;
            color: #FF5722;
        }
    }

    .coupons {
        font-weight: 400;
        color: #3F51B5;
    }

    .sale {
        font-weight: 400;
        color: red;
    }
}

.action-image:hover {
    filter: grayscale(50%);
}

.mcourse-banner {
    position: relative;
    color: #ffffff;
    overflow: hidden;
    min-height: 250px;

    .mcourse-mask {
        position: absolute;
        top: 0;
        left: 0;
        background: rgba(0, 0, 0, .5);
        width: 100%;
        height: 100%;
        z-index: 1
    }

    .mcourse-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        filter: blur(10px);
        opacity: .8;
        background-size: cover;
        background-position: center center;
        background-attachment: fixed;
    }

    .mcourse-title {
        line-height: 1.5;
        text-transform: uppercase;
        font-size: 27px;

        @media (max-width: 768px) {
            font-size: 20px;
        }
    }

    .breadcrumb {
        background: none;
        color: #ffffff;
        padding-left: 0;

        &>.active {
            color: #c9c9c9;
        }
    }

    .mcourse-content {
        z-index: 2;
        position: relative;
        display: flex;
        height: 100%;
        flex-direction: column;
        justify-content: space-between;
        padding: 20px 10px;
    }
}

.mcourse-info {
    display: flex;
    gap: 30px;

    .mcourse-info_main {
        width: 150px;
        flex-shrink: 0;

        .img {
            width: 130px;
            height: 115px;
            line-height: 115px;
            display: inline-block;
            margin-bottom: 20px;
            border: 1px solid #CFD8DC;
        }
    }

    .number-review {
        font-size: 52px;
        line-height: 52px;
        color: #29303b;

        @media (max-width: 500px) {
            font-size: 36px;
            line-height: 36px;
        }
    }

    .mcourse-info_review {
        display: flex;
        align-items: center;
        flex-wrap: nowrap;
        gap: 20px;

        .chart-line {
            flex: 1 1 auto;
            margin: 0;
        }

        .box-star {
            width: 150px;
            flex-shrink: 0;
        }
    }

    .mcourse-info_meta {
        color: $color-666;

        i {
            color: $color-999;
            width: 28px;
            display: inline-block;

            &.fa-star {
                color: #f4c150;
            }
        }
    }

    @media (max-width: 480px) {
        flex-direction: column;
        gap: 10px;

        .mcourse-info_main {
            width: 100%;
            text-align: center;
        }

        .mcourse-info_meta {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 7px;
        }
    }
}

.list-check {
    li {
        position: relative;
        padding-left: 30px;
        color: $color-333;
        line-height: 1.4;
        margin: 0 0 10px 0;

        &:before {
            content: '\f058';
            position: absolute;
            top: 2px;
            left: 4px;
            color: #009688;
            font-family: Font Awesome\ 5 Free;
            -webkit-font-smoothing: antialiased;
            display: inline-block;
            font-style: normal;
            font-variant: normal;
            text-rendering: auto;
            line-height: 1;
            font-weight: 400;
            font-size: 16px;
        }

        a {
            color: $color-333;
        }

        &:last-child {
            margin: 0;
        }
    }
}

.lesson-group {
    .panel-heading {
        padding: 10px 0;

        .panel-title {
            &>a {
                display: flex;
                flex-wrap: nowrap;
                padding: 10px 15px;
                text-decoration: none !important;

                i {
                    flex: 0 0 25px;
                    color: #666;
                    font-weight: 400;
                }

                &[aria-expanded="false"] > .fa-minus-square:before {
                    content: "\f0fe";
                }

                .lesson-name {
                    flex: 1;
                }

                .lesson-time {
                    flex: 0 0 80px;
                    margin-left: auto;
                    text-align: right;
                }

                .lesson-total {
                    flex: 0 0 80px;
                    white-space: nowrap;
                    margin-left: auto;
                }
            }
        }
    }

    .panel-collapse {
        .panel-body {
            padding: 0;
        }
    }

    .list-group {
        margin: 0;

        .list-group-item {
            padding: 0;
            border: none;
            display: flex;
            flex-wrap: nowrap;
            align-items: center;
            cursor: pointer;

            &+.list-group-item {
                border-top: 1px solid $color-border;
            }

            .lesson-name {
                color: #000;
                flex: 1;
                padding: 15px 30px;

                @media (max-width: 768px) {
                    padding: 10px 15px;
                }
            }

            .lesson-item {
                font-size: 14px;
                color: #868686;
                padding: 5px 10px;

                @media (max-width: 768px) {
                    width: 100%;
                    display: flex;
                    flex-wrap: wrap;
                    align-items: center;
                    justify-content: space-between;

                    &:has(> :nth-child(2)) {
                      flex-direction: row-reverse;
                    }
                }
            }

            .lesson-publish,
            .lesson-time {
                color: #3F51B5;
            }

            .lesson-icon {
                color: #9C27B0;
            }

            .only-doc {
                background: #fffef2;

                .lesson-icon {
                    color: #F44336;
                }
            }

            .check-icon {
                color: #349023;
                padding-left: 10px;

                &.checked:before {
                    content: "\f058";
                    font-weight: 900;
                }
            }

            &:hover {
                background: #00afff30;
            }
        }
    }

    .lesson-free {
        color: red;
        margin-right: 20px;
        font-size: 14px;
        text-transform: capitalize;
    }
}

.pcourse-main {
    background: #ffffff;
    @include border-radius(5px);
    padding: 10px;
}

.pcourse-summary {
    background: #ffffff;
    @include border-radius(5px);
    padding: 10px 20px;
    margin-bottom: 30px;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;

    @media (min-width: 992px) {
        margin-top: -150px;
        position: relative;
        z-index: 2;
        box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
    }

    p {
        color: $color-666;
        line-height: 1.5;
        margin-top: 10px;
    }

    .pcourse-summary_meta {
        color: $color-666;
        padding-bottom: 10px;

        p {
            margin-bottom: 15px;
        }

        i {
            width: 20px;
            color: $color-666;
            display: inline-block;
        }

        .txt {
            width: 95px;
            display: inline-block;
        }
    }
}
