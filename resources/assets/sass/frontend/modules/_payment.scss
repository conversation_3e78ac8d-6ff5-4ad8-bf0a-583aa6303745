.page-pay {
    margin-top: 25px;
    margin-bottom: 25px;

    .btn-pay {
        margin-top: 25px;
        width: 100%;
        display: block;
        border-radius: 2px;
        text-align: center;
    }

    .summary-pay {
        border: 1px dashed #bcb9b9;
        border-radius: 2px;
        overflow: hidden;
        padding: 15px 10px;
        background: #ffffff;

        .name {
            color: #000;
            font-weight: 400;
            margin: 0;
            position: relative;

            i {
                padding-right: 5px;
            }

            .count-cart {
                position: absolute;
                top: -10px;
                left: 25px;
                font-size: 14px;
                font-weight: 900;
                color: red;
            }
        }

        .money-course {
            position: relative;
            margin-top: 20px;
            border-bottom: 1px dashed #d7d7d7;

            .left {
                width: 100%;
                padding-right: 90px;

                p {
                    color: $color-666;
                    min-height: 40px;
                }
            }

            .right {
                position: absolute;
                top: 0;
                right: 0;

                p {
                    color: $color-333;
                    font-weight: 900;
                }
            }
        }

        .money-total {
            margin-top: 20px;
            font-size: 18px;

            .left-old,
            .left-sale {
                p {
                    color: $color-333;
                    margin-bottom: 15px;
                }
            }

            .right-old,
            .right-sale {
                p {
                    color: $color-333;
                    margin-bottom: 15px;
                    font-weight: 500;
                    text-align: right;
                }
            }

            .left-total {
                p {
                    color: #FF5722;
                    margin: 0 0 20px 0;
                    font-weight: 700;
                }
            }

            .right-total {
                p {
                    color: #FF5722;
                    margin: 0 0 20px 0;
                    font-weight: 700;
                    text-align: right;
                }
            }

            .total {
                padding-top: 5px;
            }
        }
    }

    .txt-title {
        color: $color-333;
        font-size: 18px;
        font-weight: 500;
        margin: 5px 0 15px;
        text-transform: capitalize;
    }

    .form-info-user {
        padding: 10px;

        label {
            font-weight: normal;
            color: $color-333;
            margin-bottom: 15px;
            display: block;
        }

        .txt-form {
            width: 100%;
            height: 46px;
            line-height: 46px;
            padding: 0 10px;
            @include border-radius(2px);
            border: 1px solid #dfdfdf;
            color: $color-333;
            padding-right: 30px;

            &:disabled, &:read-only {
                border: 1px solid #eee;
                background-color: #eee;
            }
        }

        .checked {
            position: relative;

            .txt-form {
                border: 1px solid $color-yellow-1;
            }

            &:before {
                content: "\f058";
                font-family: Font Awesome\ 5 Free;
                -webkit-font-smoothing: antialiased;
                display: inline-block;
                color: #4bac4d;
                font-style: normal;
                font-variant: normal;
                text-rendering: auto;
                font-weight: 900;
                position: absolute;
                bottom: 13px;
                right: 12px;
            }
        }
    }

    .tab-pay {
        margin-bottom: 15px;

        .nav-tab-header {
            padding: 10px;

            .nav-tab-item {
                margin-bottom: 15px;
                background-color: #ffffff;
                border: 1px solid #d7d7d7;
                border-radius: 2px;
                padding: 10px;
                font-size: 16px;
                color: red;
                display: flex;
                align-items: center;
                gap: 10px;
                cursor: pointer;
                font-weight: 400;
                text-align: center;
                min-height: 70px;

                i {
                    color: #333;
                }

                &.active {
                    border: 1px solid red;
                    background: #cddc393b;
                }
           }
        }

        .tab-content {
            background: #ffffff;
            padding: 20px;
            border: 1px dashed #dedede;
            font-size: 18px;

            .tab-item {
                display: none;

                &.active {
                    display: block;
                }
            }
        }
    }
}
