.livestreams {
    .main-blogs {
        display: flex;
        align-items: center;
    }

    .main-blog__author {
        display: flex;
        align-items: center;
        padding-bottom: 10px;
    }

    .main-blog__author.tips {
        flex-direction: column-reverse;
        align-items: flex-start;
    }

    .main-blog__info {
        position: absolute;
        bottom: 0;
        width: 100%;
        padding: 30px;
    }

    .main-blog__title {
        font-size: 25px;
        font-weight: 600;
        letter-spacing: 1px;
        color: #ffffff;
        margin-bottom: 30px;
    }

    .main-blog-left {
        width: 65%;
        display: flex;
        flex-direction: column;
        align-self: stretch;
        overflow: hidden;
    }

    .main-blog {
        background-image: linear-gradient(#e66465, #9198e5);
        background-size: cover;
        background-position-x: center;
        background-position-y: center;
        background-repeat: no-repeat;
        // background-color: #ff9130;
        border-radius: 7px;
        position: relative;
        transition: background 0.3s;
        height: 385px;

        .author-img {
            border-color: rgba(255, 255, 255, 0.75);
        }
    }

    .main-blog-right {
        margin-left: 20px;
        width: 35%;
        background-image: none;
        background-color: #ffffff;
        background-position-x: 0;
        background-size: 139%;
        filter: saturate(1.4);
        padding: 0;
        display: flex;
        flex-direction: column;
        border-radius: 7px;
        align-self: stretch;
        overflow: hidden;
        position: relative;
        transition: background 0.3s;
        background-repeat: no-repeat;
        height: 385px;

        >div {
            padding: 5px 12px;
        }

        .body-list {
            height: 300px;
            overflow: auto;
            padding-right: 10px;
            margin-right: -12px;

            &::-webkit-scrollbar {
                width: 3px;
            }

            &::-webkit-scrollbar-thumb {
                border-radius: 1px;
                background: #ff940e;
            }

            ul {
                li {
                    list-style: none;
                    padding: 12px 0;
                    border-bottom: 1px dotted #e1e1e1;

                    .icon-list {
                        width: 48px;
                        float: left;
                        margin-right: 12px;
                    }

                    .title-item {
                        color: #818181;
                        display: table-row;

                        a {
                            display: inline-block;
                            color: #262626;
                            padding-bottom: 12px;
                        }
                    }

                    .info-item {
                        color: #818181;
                        font-size: 12px;
                        display: table-row;

                        >span {
                            padding: 4px 0;
                            padding-right: 12px;
                            display: inline-block;

                            span {
                                margin-bottom: 4px;
                                display: inline-block;
                            }
                        }

                        a {
                            padding: 5px 10px;
                            font-weight: 600;
                            border-radius: 5px;
                            border: 1px solid #e1e1e1;
                            color: #f2780c;
                            margin-right: 8px;
                            float: right;
                            display: inline-block;
                            margin-top: -5px;
                        }
                    }
                }
            }
        }

        .footer-list {
            height: 30px;
            z-index: 997;
            margin-top: -30px;
            width: 100%;
            background-image: -webkit-gradient(linear, left top, left bottom, from(#ffffff29), color-stop(30%, #ffffffd6));
            background-image: linear-gradient(#ffffff29, #ffffffd6 30%);
            position: relative;
        }

        .nav-tabs {
            display: -webkit-box;
            overflow-x: scroll;
            overflow-y: hidden;
            padding: 0 30px;

            &::-webkit-scrollbar {
                display: none;
            }

            >li {
                font-size: 13px;
                font-weight: 600;
                text-align: center;
                text-transform: uppercase;
                min-width: 100px;

                >a {
                    color: #31abbd;
                    padding: 7px 5px;
                }
            }
        }
    }

    .tab-content {
        background-image: url("/images/bg-title.png");
        background-color: #ffffff;
        background-size: 100%;
        background-repeat: no-repeat;
    }

    .main-blog__time {
        background: rgba(21, 13, 13, 0.44);
        color: #ffffff;
        padding: 3px 8px;
        font-size: 12px;
        font-weight: 600;
        border-radius: 4px;
        position: absolute;
        right: 20px;
        top: 20px;

        a {
            color: #ffffff;
        }
    }

    .main-blog__btn {
        text-transform: uppercase;
        font-size: 12px;
        font-weight: 600;
        position: absolute;
        left: 20px;
        top: 20px;
    }

    .author-img {
        width: 52px;
        height: 52px;
        border: 1px solid rgba(0, 0, 0, 0.5);
        padding: 4px;
        border-radius: 50%;
        object-fit: cover;
    }

    .author-img__wrapper {
        position: relative;
        display: flex;
        margin-right: 12px;

        svg {
            width: 16px;
            padding: 2px;
            background-color: #ffffff;
            color: #0daabc;
            border-radius: 50%;
            border: 2px solid #0daabc;
            position: absolute;
            bottom: 5px;
            left: 0;
        }
    }

    .author-name {
        font-size: 15px;
        color: #ffffff;
        font-weight: 500;
        margin-bottom: 8px;
    }

    .author-info {
        font-size: 13px;
        font-weight: 400;
        color: #ffffff;
    }

    .seperate {
        width: 3px;
        height: 3px;
        display: inline-block;
        vertical-align: middle;
        border-radius: 50%;
        background-color: #ffffff;
        margin: 0 3px;
    }

    .seperate.video-seperate {
        background-color: #808191;
    }

    .videos {
        // display: grid;
        // width: 100%;
        // grid-template-columns: repeat(4, 1fr);
        // grid-column-gap: 20px;
        // grid-row-gap: 20px;
        margin: 20px 0 0;
        color: #7d6c6c;

        a {
            color: #444;
        }
    }

    .livestream-slessons-slick {
        .slick-slide {
            margin: 0 7px;
        }

        .slick-list {
            margin: 0 -7px;
        }
    }

    .video {
        position: relative;
        background-color: #ffffff;
        border-radius: 5px;
        overflow: hidden;
        transition: 0.4s;
        padding: 10px 15px 7px
    }

    .video-wrapper {
        position: relative;
    }

    .video-info {
        height: 100px;
    }

    .video-name {
        color: #464646;
        font-size: 16px;
        line-height: 1.4em;
        padding: 10px 0 0;
        overflow: hidden;
        z-index: 9;
        position: relative;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        font-weight: 500;
    }

    .video-view {
        font-size: 12px;
        padding: 10px 0;
        position: relative;
    }

    .video-by {
        transition: 0.3s;
        padding: 15px 20px 0 5px;
        // display: inline-flex;
        position: relative;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        line-height: 30px;

        &:before {
            content: "";
            background-color: #22b07d;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            position: absolute;
            top: 26px;
            right: 5px;
        }
    }

    .video-by.offline {
        &:before {
            background-color: #ff7551;
        }
    }

    .video-time {
        position: absolute;
        background: rgba(21, 13, 13, 0.44);
        color: #ffffff;
        padding: 3px 8px;
        font-size: 12px;
        border-radius: 6px;
        top: 10px;
        z-index: 1;
        right: 8px;
    }

    .video-blade {
        position: absolute;
        background: transparent;
        color: #f00;
        font-size: 12px;
        font-weight: 500;
        border-radius: 2px;
        top: 5px;
        z-index: 1;
        right: 8px;
        border: 1px solid #f00;
        padding: 1px 4px;

        &.new {
            color: #999;
            background: #dedede;
            border: 1px solid #dedede;
        }
    }

    .video-author {
        svg {
            background-color: #0aa0f7;
            color: #ffffff;
            border-color: #ffffff;
        }
    }

    .test-btn {
        display: flex;
        justify-content: space-around;

        a {
            background-color: #ff9130;
            border: 1px solid #ffffff;
            color: #ffffff;
            font-size: 12px;
            font-weight: 900;
            text-transform: uppercase;
            display: flex;
            padding: 10px;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            transition: 0.3s;
            width: 100%;
        }
    }

    .anim {
        animation: bottom 0.8s 0s both;
    }

    .scroller {
        cursor: pointer;
        position: absolute;
        z-index: 1;
        background: #ff9130;
        padding: 7px 12px !important;
        color: #ffffff;
        font-size: 12px;
    }

    .scroller-right {
        right: 0;
    }

    .scroller-left {
        left: 0;
    }

    .video-list-row-title {
        display: block;
        font-weight: bold;
        font-size: 16px;
        color: #2e2e30;
        text-transform: uppercase;
        padding: 0;

        i {
            margin-left: 10px;
            font-size: 9px;
            line-height: 12px;
            vertical-align: text-top;
        }

        a {
            color: #3b3d40;
            text-decoration: none;

            &:hover {
                color: #dc102e;
            }
        }
    }

    .col-video {
        position: relative;

        .panel-video {
            margin-top: 15px;
            margin-bottom: 29px;
            text-align: left;
            -webkit-border-radius: 0;
            -moz-border-radius: 0;
            border-radius: 0;

            &:before {
                content: " ";
                display: table;
                content: " ";
                display: table;
            }

            &:after {
                content: " ";
                display: table;
                clear: both;
                content: " ";
                display: table;
                clear: both;
            }

            .label {
                font-weight: bold;
                font-size: 10px;
            }

            .panel-heading {
                -webkit-border-radius: 0;
                -moz-border-radius: 0;
                border-radius: 0;
                padding: 0;
                width: 100%;
                padding-bottom: 56.25%;
                position: relative;
                background-image: linear-gradient(#e66465, #9198e5);
                background-size: cover;
                background-position-x: center;
                background-position-y: center;
                background-color: #000;
                background-repeat: no-repeat;

                .video-add-to-playlist {
                    position: absolute;
                    top: 5px;
                    right: 5px;
                    z-index: 5;
                    display: none;

                    .video-add-to-playlist-button {
                        width: 20px;
                        height: 22px;
                        vertical-align: middle;
                        text-align: center;
                        background-color: white;
                        cursor: pointer;
                        border-radius: 4px;
                        opacity: 0.75;

                        &:hover {
                            opacity: 1;
                        }
                    }
                }

                .video-length {
                    display: none;
                    position: absolute;
                    bottom: 10px;
                    right: 10px;
                    z-index: 5;
                }

                .video-hd {
                    position: absolute;
                    bottom: 10px;
                    left: 10px;
                    z-index: 5;
                    background-color: #e91e30;
                    color: white !important;
                    font-weight: bold;
                    font-size: 13px;
                    padding: 3px 6px;
                    line-height: 13px;
                    border: 1px solid red;
                    border-radius: 2px;
                    text-transform: uppercase;
                }
            }

            .panel-body {
                -webkit-border-radius: 0;
                -moz-border-radius: 0;
                border-radius: 0;
                position: relative;
                padding: 10px 20px;

                .panel-video-text {
                    .panel-video-uploader {
                        display: inline-block;
                        margin-bottom: 7px;
                        word-wrap: break-word;
                        word-break: break-all;
                        white-space: normal;
                        text-align: left;
                        vertical-align: top;
                        padding: 4px 6px;
                        white-space: nowrap;
                        max-width: 100%;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        text-decoration: none;

                        &:hover {
                            text-decoration: none;
                        }
                    }

                    .panel-video-title {
                        display: block;
                        font-weight: bold;
                        font-size: 16px;
                        height: 44px;
                        word-wrap: break-word;
                        white-space: normal;
                        overflow: hidden;
                        margin-bottom: 0;
                        color: #3b3d40;
                        text-decoration: none;

                        a {
                            color: #3b3d40;
                            text-decoration: none;

                            &:hover {
                                color: #dc102e;
                            }
                        }

                        &:hover {
                            color: #dc102e;
                        }
                    }

                    .panel-video-upload {
                        display: none;
                        font-style: italic;
                        font-size: 12px;
                        color: #838d8f;

                        .panel-video-uploader {
                            margin-bottom: 0px;
                            margin-top: 7px;
                            width: 100%;
                            font-style: normal;
                        }
                    }

                    .panel-price {
                        float: right;
                        line-height: 22px;
                        text-transform: uppercase;

                        .old-price {
                            text-decoration: line-through;
                            font-size: 13px;
                            color: #888;
                        }

                        .price {
                            font-size: 15px;
                            font-weight: 600;
                            color: #ff9130;
                            margin-left: 5px;
                        }
                    }
                }
            }

            .panel-footer {
                -webkit-border-radius: 0;
                -moz-border-radius: 0;
                border-radius: 0;
                border-top: 1px solid #eee;
                background-color: transparent;

                .video-stat-left {
                    float: left;
                    max-width: 61%;
                    padding-right: 8px;
                    font-size: 12px;
                    color: #838d8f;
                    text-decoration: none;
                    white-space: nowrap;

                    a {
                        font-size: 12px;
                        color: #838d8f;
                        text-decoration: none;
                        white-space: nowrap;
                    }
                }

                .video-stat-right {
                    float: right;
                    max-width: 40%;
                    font-size: 12px;
                    color: #838d8f;
                    text-decoration: none;
                    white-space: nowrap;
                    line-height: 30px;

                    a {
                        font-size: 12px;
                        color: #838d8f;
                        text-decoration: none;
                        white-space: nowrap;
                    }
                }

                .video-stat-item {
                    color: #838d8f;
                    display: inline-block;
                    cursor: default;
                    margin-right: 5px;

                    i {
                        color: #cdd7d8;
                    }
                }

                .video-stat-item.views {
                    margin: 0;
                    white-space: nowrap;

                    &:hover {
                        color: black;

                        i {
                            color: black;
                        }
                    }
                }

                .panel-video-upload {
                    font-size: 12px;
                    line-height: 18px;
                    color: #838d8f;

                    i {
                        color: #ced7d8;
                    }
                }

                &:before {
                    content: " ";
                    display: table;
                    content: " ";
                    display: table;
                }

                &:after {
                    content: " ";
                    display: table;
                    clear: both;
                    content: " ";
                    display: table;
                    clear: both;
                }
            }
        }

        .panel-video.playlist-layout {
            .panel-heading {
                width: 49%;
                float: left;
                padding-bottom: 0;

                >a {
                    >img {
                        position: inherit;
                    }
                }
            }

            .panel-body {
                padding: 0px 5px;
                width: 49%;
                float: right;

                .panel-video-uploader {
                    font-size: 10px;
                }

                .panel-video-title {
                    font-size: 13px;
                    height: 38px;
                }
            }

            .panel-footer {
                float: none;
                clear: both;
                padding: 5px 10px;
            }
        }

        .video-overlay {
            position: absolute;
            top: 0;
            right: 50%;
            width: 50%;
            background: rgba(0, 0, 0, 0.5);
            height: 100%;
            z-index: 3;
            color: #ffffff;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            span {
                font-size: 18px;
                font-weight: 700;
            }
        }
    }

    .col-video.playlist-video {
        .panel-video {
            background-color: #e0e0e0;
        }

        .panel-footer {
            display: block;
        }
    }

    .col-video.playlist-poster-video {
        padding-left: 0;
        padding-right: 0;

        .panel-video {
            margin: 0;
            border: 0;

            .panel-heading {
                border: 0;
            }
        }
    }

    .label-black {
        background-color: #000;
        color: #ffffff;
    }

    .panel-video-uploader.label {
        background-color: #838d8f;
        color: #ffffff;
    }

    .panel-video-uploader.label[href] {
        &:hover {
            background-color: #6a7375;
        }

        &:focus {
            background-color: #6a7375;
        }
    }

    .panel-video-uploader.label-ch2 {
        background-color: #14478f;
        color: #ffffff;
    }

    .panel-video-uploader.label-ch2[href] {
        &:hover {
            background-color: white;
            color: #14478f;
        }

        &:focus {
            background-color: white;
            color: #14478f;
        }
    }

    .single-video-data-uploader-name.ch2 {
        a {
            color: #14478f !important;
        }
    }

    .stream-area {
        display: flex;
        margin-bottom: 30px;
    }

    .video-stream {
        width: 65%;
        -o-object-fit: cover;
        object-fit: cover;
        transition: 0.3s;
        background: #ffffff;

        &:hover {
            .video-js {
                .vjs-big-play-button {
                    opacity: 1;
                }
            }
        }
    }

    .video-p {
        margin-right: 12px;
        -o-object-fit: cover;
        object-fit: cover;
        flex-shrink: 0;
        border-radius: 50%;
        position: relative;
        top: 0;
        left: 0;

        .banner-img {
            width: 100px;
            height: 60px;
            border: 1px solid #dedede;
            background-image: -webkit-gradient(linear, left top, left bottom, from(#e66465), to(#9198e5));
            background-image: linear-gradient(#e66465, #9198e5);
            background-size: cover;
            background-position-x: center;
            background-position-y: center;
            // background-color: #ff9700;
            background-repeat: no-repeat;
        }

        svg {
            width: 15px;
            background-color: #0aa0f7;
            color: #ffffff;
            border-color: #ffffff;
            border-radius: 50%;
            border: 2px solid #0daabc;
            position: absolute;
            top: 50%;
            -ms-transform: translateY(-50%);
            transform: translateY(-50%);
            left: -17px;
        }
    }

    .lesson {
        svg {
            background-color: #ffffff;
        }

        &.active svg {
            background-color: #0aa0f7;
        }
    }

    .video-p-sub {
        font-size: 12px;
        color: #999;
    }

    .video-p-title {
        font-size: 24px;
        color: #000;
        line-height: 1.4em;
        margin: 16px 0 20px;
    }

    .video-p-name {
        margin-bottom: 8px;
        color: #999;
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: 600;

        &:after {
            content: "";
            width: 6px;
            height: 6px;
            background-color: #22b07d;
            border-radius: 50%;
            margin-left: 8px;
            display: inline-block;
        }
    }

    .video-p-name.offline {
        &:after {
            background-color: #ff7551;
        }
    }

    .video-content {
        width: 100%;
    }

    .button-wrapper {
        display: flex;
        align-items: center;
        margin-left: auto;
    }

    .like {
        display: flex;
        align-items: center;
        background-color: #353340;
        color: #ffffff;
        border: 0;
        font-family: "Inter", sans-serif;
        border-radius: 5px;
        padding: 5px 15px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;

        svg {
            width: 18px;
            flex-shrink: 0;
            margin-right: 10px;
            padding: 0;
        }

        + {
            .like {
                margin-left: 16px;
            }
        }
    }

    .like.bgred {
        background-color: #ea5f5f;
    }

    .video-stats {
        margin-left: 30px;
    }

    .video-detail {
        display: flex;
        padding: 15px 30px;
        width: 100%;
    }

    .vj-header {
        display: flex;
        align-items: center;
        padding: 10px 20px;
        font-size: 16px;
        font-weight: 600;
        color: #252836;
        background: #ffffff;
        border-bottom: 1px solid #eee;

        svg {
            width: 15px;
            margin-right: 6px;
            flex-shrink: 0;
        }

        span {
            margin-left: auto;
            color: #808191;
            font-size: 12px;
            display: flex;
            align-items: center;
        }
    }

    .vj-stream {
        flex-grow: 1;
        margin-left: 30px;
        width: 35%;
    }

    .vj-body {
        background-color: #ffffff;
        border-bottom: 1px solid #eee;

        .scroll-box {
            max-height: 414px;
            overflow-y: auto;
            overflow-x: hidden;
        }
    }

    .vj-footer {
        padding: 10px 0;
        background: #ffffff;
        border-bottom: 1px solid #eee;

        .btn {
            border: 1px solid #ea1e30;
            color: #ea1e30;
        }
    }

    .vj-vid__title {
        font-size: 16px;
        font-weight: 600;
        color: #252836;
    }

    .vj-vid__container {
        margin-top: 30px;
        background: #ffffff;
        border-radius: 7px;
        padding: 20px 10px;

        a {
            color: #333;
        }
    }

    .vj-vid__wrapper {
        display: flex;
        align-items: center;
        margin-top: 26px;
    }

    .vj-vid__name {
        color: #000;
        font-size: 14px;
        font-weight: 600;
        line-height: 1.5em;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        overflow: hidden;
        -webkit-box-orient: vertical;
    }

    .vj-vid__img {
        width: 100px;
        height: 80px;
        border-radius: 5px;
        -o-object-fit: cover;
        object-fit: cover;
        -o-object-position: center;
        object-position: center;
        margin-right: 16px;
        transition: 0.3s;

        &:hover {
            transform: scale(1.02);
        }
    }

    // .vj-vid__content {
    //     max-width: 20ch;
    // }

    .vj-vid__by {
        color: #808191;
        font-size: 13px;
        margin: 6px 0;
    }

    .vj-vid__info {
        color: #808191;
        font-size: 13px;

        .seperate {
            background-color: #808191;
        }
    }

    .vj-vid__button {
        background-color: #ff9700;
        border: 0;
        color: #ffffff;
        font-size: 14px;
        font-weight: 600;
        margin-top: 26px;
        display: flex;
        padding: 0 10px;
        align-items: center;
        justify-content: center;
        height: 40px;
        border-radius: 4px;
        cursor: pointer;
        transition: 0.3s;

        a {
            color: #ffffff;
            text-transform: uppercase;
        }

        &:hover {
            background-color: #ef920a;
        }
    }

    .lesson-container {
        padding: 0 10px 0 25px;
    }

    .lesson {
        display: flex;
        align-items: center;
        padding: 5px;
        margin-bottom: 5px;

        &:hover {
            background: #eee;
        }
    }

    .item__name {
        margin-bottom: 8px;
        color: #000;
        display: flex;
        align-items: center;
        font-size: 16px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        overflow: hidden;
        -webkit-box-orient: vertical;
    }

    .item__content {
        line-height: 1.4em;
        display: -webkit-box;
        overflow: hidden;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    .video-p-wrapper {
        display: flex;
        align-items: center;
    }

    .small-header {
        font-size: 14px;
        font-weight: 600;
        color: #444;
        margin: 20px 0 0;
        text-transform: uppercase;
    }
}

@media screen and (max-width: 905px) {
    .livestreams {
        .main-blog-left {
            width: 50%;
        }

        .main-blog-right {
            width: 50%;
        }

        // .main-blog {
        //     background-blend-mode: overlay;
        // }
    }
}

@media screen and (max-width: 940px) {
    .livestreams {
        .stream-area {
            flex-direction: column;

            .video-stream {
                width: 100%;
            }

            .vj-stream {
                margin-left: 0;
            }

            .video-js.vjs-fluid {
                min-height: 250px;
            }

            .item__content {
                max-width: 100%;
            }
        }

        .vj-stream {
            width: 100%;
        }
    }
}

// @media screen and (max-width: 980px) {
//     .livestreams .videos {
//         grid-template-columns: repeat(2, 1fr);
//     }
// }

@media screen and (max-width: 735px) {
    .livestreams {
        .main-blogs {
            flex-wrap: wrap;
        }

        .main-blog-left {
            width: 100%;
        }

        .main-blog-right {
            width: 100%;
            margin-left: 0;
            margin-top: 20px;
        }

        // .videos {
        //     grid-template-columns: 1fr;
        // }
    }
}

@media screen and (max-width: 650px) {
    .livestreams {
        .video-detail {
            padding: 10px;
        }

        .video-p-wrapper {
            flex-direction: column;

            .button-wrapper {
                margin: 20px auto 0;
            }

            .video-p-detail {
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            .video-p {
                margin-right: 0;
            }
        }
    }
}

@media screen and (max-width: 475px) {
    .livestreams {
        .main-blog__title {
            font-size: 20px;
        }

        .author-name {
            font-size: 14px;
        }

        .main-blog__author {
            flex-direction: column-reverse;
            align-items: flex-start;
        }

        .author-detail {
            margin-left: 0;
        }

        .main-blog {
            .author-img {
                margin-top: 14px;
            }
        }
    }
}

@media (min-width: 480px) {
    .livestreams .col-video {
        .panel-video {
            .panel-heading {
                &:hover {
                    .video-add-to-playlist {
                        display: block;
                    }
                }

                .video-length {
                    display: block;
                }
            }

            .panel-heading.processing {
                .video-add-to-playlist {
                    display: block;
                }
            }

            .panel-body {
                .panel-video-text {
                    .panel-video-upload {
                        display: block;
                    }
                }
            }

            .panel-footer {
                display: block;
            }
        }
    }
}

@media (min-width: 1060px) {
    .livestreams .col-video {
        .panel-video {
            padding: 0;
        }

        .panel-video.alt-layout {
            .panel-heading {
                width: 49%;
                float: left;
                padding-bottom: 0;

                >a {
                    >img {
                        position: inherit;
                    }
                }
            }

            .panel-body {
                padding: 0px 5px;
                width: 49%;
                float: right;

                .panel-video-uploader {
                    margin-top: 10px;
                    margin-bottom: 0;
                    font-size: 9px;
                }

                .panel-video-title {
                    margin-top: 5px;
                    font-size: 12px;
                    line-height: 1.2em;
                    height: 40px;
                }
            }

            .panel-footer {
                float: none;
                clear: both;
                padding: 5px 10px;
            }
        }
    }
}

@media (min-width: 1200px) {
    .livestreams .col-video {
        .panel-video.alt-layout {
            .panel-body {
                .panel-video-title {
                    height: 55px;
                }
            }
        }
    }
}

@media (max-width: 500px) {
    .livestreams .col-xxs-12 {
        position: relative;
        min-height: 1px;
        padding-left: 10px;
        padding-right: 10px;
        float: left;
        width: 100%;
    }
}

.vj-modal_notice {
    .modal-content {
        max-width: 500px;
    }

    .modal-header {
        display: flow-root;
        border-bottom: none;
        padding: 10px 10px 0;

        .close {
            width: 25px;
            height: 25px;
            text-align: center;
            line-height: 24px;
            background-color: rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            color: #F14D76;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.1s ease;
            opacity: 1;
        }
    }

    .modal-body {
        .icon {
            display: inline-block;
            width: 65px;
            height: 65px;
            border-radius: 50%;
            text-align: center;
            line-height: 65px;
            background-color: #f14d76;
            color: #ffffff;
            font-size: 30px;
            margin: 0 auto;

            i {
                margin-left: -5px;
            }
        }

        .text-notice {
            padding: 0 75px;
            font-size: 32px;
            font-weight: bold;
            color: #292A3A;
            margin-top: 20px;
            margin-bottom: 16px;
            text-align: center;
        }
    }

    .modal-footer {
        padding: 7px;
    }
}

@media only screen and (max-width : 480px) {
    .vj-modal_notice {
        .modal-body {
            .text-notice {
                padding: 0 20px;
            }
        }
    }
}
