.msg-icon {
    height: 50px;
    width: 50px;
    top: auto;
    left: auto;
    bottom: 30px;
    right: 15px;
    cursor: pointer;
    position: fixed;
    z-index: 9999;
}

.msg-image-background {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    top: 0px;
    left: 0px;
    width: 60px;
    height: 60px;
    background-image: url(/images/icon-messenger.png);
    background-repeat: no-repeat;
    background-position: left top;
    background-size: cover;
    background-attachment: scroll;
    background-origin: content-box;
    position: absolute;
    margin: 0 auto;
}

.msg-text-wrap {
    top: auto;
    left: auto;
    bottom: 30px;
    right: 70px;
    width: 270px;
    height: 117px;
    position: fixed;
    z-index: 9999;
    box-shadow: 18px 11px 20px -20px #000;
    -webkit-box-shadow: 18px 11px 20px -20px #000;
    opacity: 0.95;
    background-color: rgb(255, 255, 255);
    border-color: rgb(132, 132, 132);
    border-width: 1px;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    border-bottom-left-radius: 20px;
    padding: 10px;
    border: 1px solid #dedede;
}

.msg-text {
    p {
        color: rgb(0, 0, 0);
        font-size: 13px;
        line-height: 1.6;
        margin-bottom: 0;
    }

    span {
        font-weight: bold;
    }
}

.msg-dot {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    display: inline-block;
    background-color: rgb(48, 232, 73);
}

@media only screen and (max-width : 480px) {
    .msg-text-wrap {
        display: none;
        width: 250px;
        height: 110px;
    }

    .msg-text {
        p {
            font-size: 11px;
        }
    }
}

.chat {
    position: fixed;
    bottom: 24px;
    right: 10px;
    display: block;
    background-color: #0084da;
    color: #ffffff !important;
    padding: 3px;
    border-radius: 5px;
    font-size: 15px;

    img {
        height: 40px;
    }

    @media (max-width: 600px) {
        display: none;
    }
}
