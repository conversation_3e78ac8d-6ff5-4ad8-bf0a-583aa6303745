#initExam {
    position: relative;
    min-height: 50px;

    &::before,
    &::after {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
    }

    &:empty {
        &::before {
            content: "";
            top: 40%;
            width: 40px;
            height: 40px;
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-top-color: #3498db;
            border-radius: 50%;
            animation: spin 0.8s linear infinite;
        }

        &::after {
            content: "Đang xử lý... (<PERSON><PERSON><PERSON> không đ<PERSON> vui lòng tải lại trang)";
            top: calc(40% + 50px);
            font-size: 14px;
            color: #333;
        }
    }
}

@keyframes spin {
  to {
    transform: translateX(-50%) rotate(360deg);
  }
}

.main-page {
    min-height: 100vh;

    .lsidebar-body {
        height: calc(100vh - 60px);
        border-left: 1px solid #dedede;
    }

    .lsidebar {
        .lsidebar-main {
            top: 60px;
        }

        .btn-rsidebar {
            left: -30px;
            top: 35px;
            border-radius: 5px;
            width: 30px;
            height: 30px;

            @media (max-width: 992px) {
                right: 30px;
                left: auto;
            }
        }
    }

    .box-wrapper {
        .box-wrapper-header {
            border-bottom: 1px solid #dedede;
            background: #ffffff;
            padding: 10px;
            color: #fff;
            display: flex;
            font-size: 16px;
            font-weight: 700;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 999;
            width: 100%;
            height: 60px;
        }
    }

    .navigation-mb {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .toggle-lsidebar {
            opacity: 0;

            @media (max-width: 992px) {
                opacity: 1;
            }
        }
    }
}

.leave-site {
    border-radius: 50%;
    height: 40px;
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #eee;
    background: #f7dcdc75;
}

.submit-test {
    border-radius: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-weight: 900;
    background-color: #ff922e;
    min-width: 150px;
    text-align: center;
}

.timer-wrapper {
    font-weight: 700;
    font-size: 2.5rem;
    line-height: 1.5;
    color: #000000;
}

.exam-box {
    max-width: 900px;
    margin: 1.5rem auto;
}

.que-card {
    box-shadow: 0 .25rem 1rem 0 #282e3e1a;
    background-color: #ffffff;
    border-radius: 1rem;
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin: 0 auto 40px;
    width: 100%;
    min-height: 150px;
    padding: 1.5rem 2rem 1rem;
    position: relative;

    &.highlight {
        background: #fffcf6;
        box-shadow: 0 0 0 3px rgba(255,153,0,0.35);
        transition: all 0.3s;
    }

    .mcq-number {
        flex-shrink: 0;
        border-radius: 50%;
        background-color: #e8f2ff;
        color: #35509a;
        width: 35px;
        height: 35px;
        line-height: 35px;
        font-size: 15px;
        text-align: center;
        display: inline-block;
        font-weight: 600;
        margin: 5px 0;
    }
}

@import '_quiz';
