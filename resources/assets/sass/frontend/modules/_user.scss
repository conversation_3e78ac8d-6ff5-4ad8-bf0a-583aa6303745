.box-upload-images {
    max-width: 700px;
    margin: 0 auto;

    .box-show-images {
        padding: 20px 65px 0 65px;
        border: 1px dashed #d7d7d7;

        .img {
            background-color: #f1f3f5;
            padding: 2px;

            .main-img {
                border: 1px dotted #dddedf;
            }
        }
    }

    p {
        color: #8e8e8e;
    }

    .box-btn-upload {
        margin-top: 25px;

        .overflow {
            position: relative;
            padding-left: 25px;

            .btn-default-yellow {
                position: absolute;
                top: 0;
                right: 0;
                height: 32px;
                line-height: 30px;
                font-size: 14px;
                font-weight: normal;
                font-weight: 400;
                padding: 0 25px;
            }

            .btn-upload {
                position: relative;
                padding-right: 107px;

                input {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    width: 100%;
                    height: 100%;
                    opacity: 0;
                }

                span {
                    display: block;
                    width: 100%;
                    border: 1px solid #ccc;
                    height: 32px;
                    line-height: 30px;
                    @include border-radius(2px);
                    padding: 0 12px;
                    color: $color-333;
                    font-weight: normal;
                    font-weight: 400;
                }
            }
        }
    }
}

.table-buy {
    color: $color-333;

    .top-table {
        padding: 25px 0 0 0;
    }

    .txt-title {
        margin: 0 0 5px 0;
        font-weight: 500;
        font-size: 16px;
    }

    .row {
        margin-left: -3px;
        margin-right: -3px;

        .col-xs-1,
        .col-xs-2,
        .col-xs-3,
        .col-xs-4,
        .col-xs-5,
        .col-xs-6,
        .col-xs-7,
        .col-xs-8,
        .col-xs-9,
        .col-xs-10,
        .col-xs-11,
        .col-xs-12 {
            padding-left: 3px;
            padding-right: 3px;
        }

        .col-xs-1 {
            width: 10%;
        }

        .col-xs-2 {
            width: 20%;
        }

        .col-xs-5 {
            width: 30%;
        }
    }

    .content-table,
    .bottom-table {
        .txt-title {
            display: none;
        }

        .row {
            padding: 15px 0;
        }

        p {
            margin: 0;
            line-height: 1.4;
            padding: 1px 0;
        }
    }

    .bottom-table {
        .col-xs-2 {
            width: 20%;
        }

        .col-xs-9 {
            width: 70%;
        }

        .row {
            padding: 5px 0;
        }
    }

    .content-table {
        border-bottom: 1px solid #eee;
        margin-bottom: 20px;
    }
}

@media all and (max-width: 1024px) {
    .table-buy {
        .top-table {
            display: none;
        }

        .content-table {
            &.border {
                border-bottom: none;

                .row {
                    border-bottom: 1px solid #eee;
                    padding: 20px 0;
                }
            }

            .txt-title {
                display: block;
            }

            .col-xs-5 {
                width: 100%;
                margin-bottom: 15px;
            }

            .col-xs-2 {
                width: 27.5%;
            }

            .col-xs-1 {
                width: 15%;
            }
        }
    }
}

@media all and (max-width: 812px) {
    .table-buy {
        .content-table {
            .col-xs-2 {
                width: 50%;
            }

            .col-xs-1 {
                width: 33.33%;
                margin-top: 15px;
            }
        }

        .bottom-table {
            .col-xs-9 {
                width: 50%;
            }

            .col-xs-2 {
                width: 30%;
            }

            .col-xs-1 {
                width: 20%;
            }
        }
    }
}

@media all and (max-width: 480px) {
    .table-buy {
        .content-table {
            .col-xs-5 {
                margin-bottom: 0;
            }

            .col-xs-2 {
                width: 100%;
                margin-top: 15px;
            }

            .col-xs-1 {
                width: 100%;
                margin-top: 15px;
                text-align: left;
            }
        }

        .bottom-table {
            .col-xs-9 {
                display: none;
            }

            .col-xs-2 {
                width: 50%;
            }

            .col-xs-1 {
                width: 50%;
            }
        }
    }
}
