.banner-text {
    display: none;
}

.pvip-container {
    background: #ffffff;

    .pvip-title {
        font-size: 3rem;
        line-height: 1.3;
        color: #3c297b;
        font-weight: 900;
        margin: 0 0 35px;
        text-transform: uppercase;
        margin-bottom: 1.5rem;

        span {
            color: #ff4a39;
        }
    }

    .pvip-subtitle {
        color: #444;
        font-size: 2rem;
        line-height: 1.5;
        font-weight: 500;
        margin-bottom: 5rem;
        margin-top: 0;
    }

    .r-pvip {
        margin-top: 3rem;
    }

    .l-pvip {
        display: none;

        .pvip-subtitle {
            margin-bottom: 0;
        }
    }

    .mark {
        padding: 2px 5px;
        background-color: #e0ff64;
        color: red;
    }

    .btn-order {
        background: #265bf6;
        border: none;
        border-radius: 50px;
        color: #ffffff;
        font-size: 20px;
        line-height: 20px;
        font-weight: 700;
        margin: 30px auto 10px;
        max-width: 550px;
        padding: 17px 20px;
        text-align: center;
        text-transform: uppercase;
        width: 100%;
        position: absolute;
        z-index: 2;
        bottom: 0;
        left: 50%;
        transform: translate(-50%, 0);
    }

    .list-feature {
        margin-top: 4rem;

        .feature-item {
            display: flex;
            align-items: baseline;
            gap: .85rem;
            margin-bottom: 1.5rem;

            .feature-icon {
                flex-shrink: 0;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: #eef2fe;
                color: #265bf6;
                padding: 0.5rem;
                border-radius: 100%;
            }

            .feature-text {
                font-size: 1.85rem;
                margin-bottom: 0;
            }
        }
    }

    .list-fee {
        width: 100%;
        max-width: 550px;
        display: flex;
        flex-direction: column;
        margin: 0 auto;
        position: relative;
        padding-bottom: 10rem;

        .fee-item {
            label {
                position: relative;
                border-color: #c5c5c5;
                cursor: pointer;
                width: 100%;
                border-radius: .75rem;
                background-color: #fdfdfd;
                border: 2px solid #dbdbdb;
                padding: 2rem 1.5rem 2rem 6rem;
                display: flex;
                flex-wrap: wrap;
                align-items: center;
                justify-content: space-between;

                &~.pvip-btn {
                    display: none;
                }

                &:before {
                    content: '\f0c8';
                    font-family: "Font Awesome 5 Free";
                    font-weight: 400;
                    position: absolute;
                    top: 50%;
                    left: 2rem;
                    transform: translateY(-50%);
                    font-size: 2.5rem;
                    color: #999;
                }

                .fee-title {
                    color: #272675;
                    font-size: 2.5rem;
                    position: relative;
                    text-transform: capitalize;
                }

                .fee-subtitle {
                    font-weight: 400;
                    margin: 0;
                    font-size: 1.5rem;
                    color: #555;
                }

                .fee-price {
                    color: #ff5722;
                    font-size: 2.5rem;
                    white-space: nowrap;
                }

                .fee-duration {
                    color: #344d59;
                    font-size: 1.85rem;
                }

                .icon-sales-price {
                    position: absolute;
                    right: -10px;
                    top: -25px;
                    width: 40px;
                }
            }

            input[type="radio"] {
                margin: 0;
                opacity: 0;

                &:checked+label {
                    border-color: rgba(0, 0, 0, 0);
                    box-shadow: 0 4px 24px 0 rgba(59, 68, 89, .08);
                    background: linear-gradient(0deg, rgba(255, 255, 255, .9), rgba(255, 255, 255, .9)) padding-box, linear-gradient(92deg, #262cf6, #ff6464) border-box;

                    &::before {
                        content: '\f14a';
                        font-weight: 900;
                        color: #265bf6;
                    }

                    &~.pvip-btn {
                        display: block;
                    }
                }
            }
        }
    }

    @media (max-width: 992px) {
        .r-pvip {
            display: none;
        }

        .l-pvip {
            display: block;
        }
    }

    @media (max-width: 500px) {
        .pvip-title {
            font-size: 2.75rem;
        }

        .pvip-subtitle {
            margin-bottom: 3rem;
        }
    }
}

.preview-section {
    min-height: 294px;
    min-width: 320px;
    overflow: hidden;
    position: relative;
    width: 100%;
}

.preview-section .bg-img {
    background-color: #e6f7fd;
    height: 294px;
    width: 1440px;
    left: 50%;
    min-height: 100%;
    min-width: 100%;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
}

.preview-content {
    max-width: 1300px;
    margin: 0 auto;
    padding: 10px;
}

.review_list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    height: 100%;
    margin: 0 auto;
    position: unset;
    transition: unset;
    width: unset;
    overflow-x: hidden;
}

.review_box {
    width: 300px;
    margin: 10px;
}

.review_item {
    background-color: #ffffff;
    border-radius: .5rem;
    text-align: center;
    padding-bottom: 1rem;
    border: 1px solid rgba(0, 0, 0, .1);
    position: relative;
}

.review_item:after {
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 15px solid #fff;
    bottom: -14px;
    content: "";
    height: 0;
    left: 50%;
    position: absolute;
    transform: translate(-50%);
    width: 0;
}

.review_icon {
    position: relative;
    z-index: 1;
    padding: 1rem;
}

.review_icon span {
    background: #fff;
    display: block;
    margin: 0 auto;
    width: 130px;
}

.review_icon span img {
    height: 60px;
    margin: 0 auto;
    width: 60px;
    border-radius: 50%;
}

.review_content {
    -webkit-line-clamp: 7;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    font-size: 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: .5rem;
    margin: .25rem 0 1rem;
}

.review_avatar {
    height: 4rem;
    width: 4rem;
    display: inline-block;
    position: relative;
}

.review_avatar img {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%;
    border-radius: 50%;
}

.review_user {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    text-align: center;
    margin-top: 20px;
}

.qa-section ul {
    padding: revert;
}

.qa-section ul li {
    list-style: revert;
    margin-bottom: 10px;
    font-size: 18px;
}
