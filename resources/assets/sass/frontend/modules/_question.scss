.question {
    color: #000;
    font-size: 16px;
    line-height: 26px;
    counter-reset: blanks;

    span.flag-el {
        background-color: rgb(255, 247, 218);
        counter-increment: blanks;
        display: inline-block;
        line-height: 1;
        margin: 0px 5px;
        padding: 7px 10px;
        min-width: 60px;
        border: 1px solid #ccc;
        border-radius: 3px;

        &::before {
            content: "(" counter(blanks, decimal) ") ";
            // margin-right: 0.25em;
            color: #666;
            font-weight: 400;
            font-size: 14px;
        }
    }

    .options-blank {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        gap: 10px;
        margin: 5px 0 15px;

        .option-blank {
            border-radius: 5px;
            border: 1px solid #aab9c7;
            color: #090909;
            background-color: #e9fffcc9;
            padding: 4px 13px;
            font-size: 18px;
            font-weight: 500;
            margin-right: 15px;

            span {
                font-size: 14px;
                font-weight: 400;
                color: #555;
            }
        }
    }

    .option-essay {
        border: 1px solid #dbdbdb;
        border-radius: 5px;
        padding: 10px;
        max-height: 35vh;
        margin: 5px 0 15px;
        background: #f0f8ff;
    }

    .result {
        line-height: 1.7;
        font-size: 18px;
        overflow-x: auto;
    }

    table {
        min-width: 80%!important;
        width: 100%;
    }
}

.title-question {
    font-weight: 400;
    font-size: 18px;
    line-height: 1.7;
    color: #000;

    p {
        line-height: 1.7;
    }
}

.number-question {
    display: contents;
    text-transform: uppercase;
    font-size: 14px;
    color: #795548;
    font-weight: 500;

    span {
        color: #0874cc;
    }

    span.true {
        color: #0da613;
    }

    span.false {
        color: #f10;
    }
}

.quiz-answer {
    .quiz-answer-left {
        margin-bottom: 20px;
        border-bottom: 1px solid #d3d9e0;

        @media (min-width: 992px) {
            margin-bottom: 0;
            padding-right: 20px;
            border-right: 1px solid #d3d9e0;
            border-bottom: none;
        }
    }

    .quiz-answer-right {
        .btn-start {
            background: transparent;
            color: #E91E63;
            text-decoration: underline;
        }
    }
}

.quiz-answer-item {
    background: #ffffff;
    padding: 20px;
    border-radius: 5px;
    box-shadow: -1px 1px 4px 0 rgba(117, 138, 172, .12);
}

.answer-check {
    -webkit-user-select: none;
    user-select: none;
    margin-bottom: 20px;

    .option-choices {
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-bottom: 12px;
        line-height: 1.3;
        cursor: pointer;
        position: relative;
        overflow: visible;
        min-height: 62px;
        border: 1px solid #e6e6e6;
        box-shadow: 0 0 1px #888;
        padding: 5px;

        .option-answer-text {
            font-size: 14px;
            background-color: rgb(255, 255, 255);
            position: absolute;
            top: -10px;
            letter-spacing: -.5px;
            right: 20px;
            padding: 0 2px;
            height: fit-content;
        }

        &.option-correct {
            border: 2px solid #60b91f;

            .option-answer-text {
                color: #60b91f;
                font-size: 16px;
            }
        }

        &.option-incorrect {
            border: 2px solid #ff5a5a;

            .option-answer-text {
                color: #ff5a5a;
                font-size: 16px;
            }
        }

        &.option-selected {
            border: 2px solid #9C27B0;

            .option-answer-text {
                color: #9C27B0;
                font-size: 16px;
            }
        }

        &.option-none {
            .option-answer-text {
                display: none;
            }
        }

        &:last-child {
            margin-bottom: 16px;
        }
    }

    .option-disabled {
        pointer-events: none!important;
    }

    .option-content {
        font-weight: 400;
        padding: 10px 10px 10px 15px;
        flex: 1;
        font-size: 18px;
        line-height: 1.7;
        overflow-y: hidden;

        p {
            margin: 0;
        }
    }
}

.is-paragraph {
    border: 1px dashed #d5bab0;
    background: #ffffff;
    border-radius: 5px;
    padding: 10px;
    max-height: 35vh;
    overflow-y: auto;

    & p:last-child {
        margin-bottom: 0 !important;
    }
}

.question-verified-box {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    background: rgba(118, 210, 34, .2);
    border-radius: 30px;
    padding-right: 10px;
    margin-bottom: 15px;

    .question-text {
        font-weight: 700;
        font-size: 14px;
        line-height: 26px;
        letter-spacing: -.342857px;
        color: #59b504;
    }
}
