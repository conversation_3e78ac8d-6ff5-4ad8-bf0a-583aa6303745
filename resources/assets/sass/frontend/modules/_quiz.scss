.paragraph-box {
    .nav-tabs {
        border-bottom: none;
    }

    .is-paragraph {
        max-height: 25vh;
    }

    .nav>li>a {
        font-weight: 600;
        color: #337ab7;
        font-size: 14px;
        background: transparent;
        margin: 0;
    }

    .nav>li>a:hover {
        background-color: #ffe50047;
        border-color: transparent;
    }
}

.essay-textarea {
    width: 100%;
    outline: none!important;
    font-size: 18px;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    margin: 10px auto;

    &.correct {
        border-color: #17c917;
    }

    &.wrong {
        border-color: red;
    }
}

.auto-resize-input {
    box-sizing: border-box;
    border: 1px solid rgb(204, 204, 204);
    border-radius: 4px;
    box-shadow: rgba(0, 0, 0, 0.075) 0px 1px 1px inset;
    margin: 5px;
    padding: 5px 10px;
    background-color: rgb(255, 247, 218);
    color: rgb(0, 0, 255);
    font-size: 18px;

    &:focus {
        outline: none;
    }

    &.correct {
        border-color: #17c917;
    }

    &.wrong {
        border-color: red;
    }
}

.mcq {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow-x: auto;

    @media (max-width: 600px) {
        padding: 10px;
    }

    .mcq-content {
        padding: 1.5rem;
        max-height: 100%;
        overflow: auto;
        text-align: left;
        font-size: 130%;

        @media (max-width: 600px) {
            font-size: initial;
        }
    }

    .mcq-answer {
        width: 100%;
        padding: 15px;

        @media (max-width: 600px) {
            padding: 0;
        }
    }

    .mcq-reason {
        max-height: 100%;
        width: 100%;
        border: 1px solid #FFC107;
        border-radius: 5px;
        transition: all 0.3s ease-in-out;
        margin: 15px 0 40px;
    }

    .mcq-option {
        margin-bottom: 1.5rem;
        display: flex;
        align-items: stretch;
    }

    .mcq-option-content {
        margin-top: 10px;
        overflow-x: auto;
        overflow-y: hidden;
    }

    .mcq-option-box {
        border: 1px solid #cfd3d7;
        border-radius: 0;
        cursor: pointer;
        width: 100%;
        padding: 1rem 1.5rem;
        position: relative;
        transition: all .35s;

        &:hover {
            border: 1px solid #03A9F4;
        }

        &.selected {
            border: 2px solid #9c27b0;
        }

        &.isCorrect {
            border: 2px solid #43a351;
        }

        &.isWrong {
            border: 2px solid #E64C65;
        }

        &.correctAnswer {
            border: 2px dashed #43a351;
        }
        
    }
}

.exam-stt {
    padding: 10px;
    display: flex;
    justify-content: left;
    gap: 9px;
    flex-wrap: wrap;

    .stt {
        display: inline-flex;
        border-radius: 3px;
        border-color: #f7faff;
        border: 1px solid;
        font-weight: 600;
        width: 30px;
        height: 25px;
        font-size: 12px;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        &.active {
            color: #002fbbc4;
            background: #fffacd;
        }

        &.done, &:hover {
            background-color: #35509a !important;
            color: #fff;
            border-color: #35509a;
        }

        &.correct {
            background-color: #43a351 !important;
            color: #fff;
            border-color: #43a351;
        }

        &.wrong {
            background-color: #E64C65 !important;
            color: #fff;
            border-color: #E64C65;
        }
    }
}
