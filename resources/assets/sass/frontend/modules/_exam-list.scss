.exam-title {
    font-size: 18px;
    line-height: 24px;
    color: #44a500;
    font-weight: 700;
    margin-bottom: 15px;
    text-align: center;
    text-transform: uppercase;
}

.exam-item {
    position: relative;

    .is_free>img {
        position: absolute;
        width: 30px;
        height: auto;
        left: 0px;
        top: -7px;
    }

    .exam-body {
        margin-bottom: 10px;
    }

    .exam-test {
        >a {
            position: relative;
            outline: none;
            font-size: 16px;
            color: #2f6a4f;
            font-weight: 700;


            &.url-main:hover {
                text-decoration: underline !important;
                color: #e91e63;
            }
        }
    }

    &.last-item {
        &:nth-child(even) {
            border-radius: 5px;
            background: #f4f4f4;
        }

        .exam-test>a {
            font-weight: 400 !important;
        }

        .exam-body {
            padding: 5px 5px 7px;
        }
    }

    .exam-action {
        border: 1px dashed #c4c1c1;
        background: #ffffff;
        border-radius: 3px;
        padding: 3px 5px;
        font-size: 15px;
    }
}

.chapter-item {
    display: block;
    overflow: hidden;
    margin-bottom: 24px;
    padding: 12px 12px 0;
    border-radius: 4px;
    background-color: #ffffff;

    .chapter-head {
        display: flex;
        align-items: baseline;
        justify-content: space-between;
        gap: 10px;

        @media (max-width: 600px) {
            flex-direction: column;
        }

        >a {
            // text-transform: uppercase;
            font-weight: 700;
            font-size: 18px;
            line-height: 24px;
            color: rgb(240, 119, 51);
        }
    }
}

.exam-content .chapter-item>ul>li>.exam-body>.exam-test {
    &>a {
        margin-left: 10px;

        &[data-toggle="collapse"]::after {
            content: '';
            width: 5px;
            height: 5px;
            background: rgba(0, 0, 0, .87);
            border-radius: 50%;
            display: block;
            position: absolute;
            left: -15px;
            top: 15px;
            font-family: 'Glyphicons Halflings';
            font-size: 0.6em;
            color: red;
        }
    }

    &>a[aria-expanded="false"]::after {
        content: '\e259' !important;
        top: 4px !important;
        background: transparent !important;
    }

    &>a[aria-expanded="true"]::after {
        content: '\e259' !important;
        top: 4px !important;
        background: transparent !important;
        content: '\e260' !important;
    }
}

.lesson-wrapper {
    margin-top: 15px;
    padding-left: 10px;
}

.vertict-line {
    height: 16px;
    display: block;
    margin: -3px 12px 0;
    width: 3px;
    display: inline-block;
    background: #ccc;
    vertical-align: middle;
}

.view-more-exam {
    border-bottom: 1px solid #eee;
}

.list-exam {
    display: flex;
}

@media (max-width: 767px) {
    .list-exam {
        display: block;
    }
}

.exam-filter {
    .nav-tabs {
        display: flex;
        flex-wrap: wrap;
        border-bottom: none;

        @media (max-width: 576px) {
            flex-wrap: nowrap;
            overflow-x: scroll;
            overflow-y: hidden;

            &::-webkit-scrollbar {
                display: none;
            }
        }

        .nav-item {
            flex: 1 1 auto;
        }
    }

    .btn-filter {
        color: #9c27b0;
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        white-space: nowrap;
    }

    .active {
        .btn-filter {
            color: #009688;
            font-weight: 500;
        }

        .fa-square:before {
            content: "\f14a";
            font-weight: 900;
        }
    }
}

.video-item {
    color: #545454;
    display: flex;
    gap: 10px;
    margin-bottom: 15px;

    &:hover {
        text-decoration: underline !important;
    }

    .video-img {
        position: relative;
        width: 80px;
        height: 60px;
        flex-shrink: 0;
        margin-top: 4px;
        display: flex;

        img {
            object-fit: cover;
        }

        svg {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(0, 0, 0, 0.6);
            color: white;
        }
    }
}

.video-tree:not(:has(.leaf-video)),
.video-subtree:not(:has(.leaf-video)) {
    display: none;
}

.tree-stitle {
    font-weight: 400;
    line-height: 1.4;
    font-size: 1.4rem;
    color: #607D8B;
}
