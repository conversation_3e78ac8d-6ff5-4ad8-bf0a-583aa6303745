@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

@media (min-width: 1700px) {
  .container {
    max-width: 1600px;
  }
}

.banner-text {
    display: none;
}

.bs-callout {
    padding: 15px 15px 10px;
    margin: 10px 0 20px;
    border: 1px solid #eee;
    border-left-width: 5px;
    border-radius: 3px;
    background: #ffffff;
}

.bs-callout p {
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    margin-bottom: 5px;
}

.bs-callout-info {
    border-left-color: #1b809e;
}

.bs-callout-danger {
    border-left-color: #ce4844;
}

.ads_baner {
    width: auto;
    max-height: 280px;
}

.box-comment-detail {
    padding: 0 !important;
}

.breadcrumb {
    margin: 0 !important;
    padding: 5px 0 10px 0 !important;

    li {
        font-size: 13px;
        display: inline;
        color: #33659c;
    }

    &>li+li:before {
        color: #33659c;
    }
}

.main-qa {
    position: relative;
    padding: 0 40px 0 15px;

    @media (max-width: 992px) {
        padding: 0;
    }

    .left-qa {
        max-width: 1000px;
        margin: auto;
    }

    .title-qa {
        padding: 15px 30px;
        border: 1px solid #e2e2e2;
        border-radius: 5px;
    }

    .title-des {
        display: contents;
        text-transform: uppercase;
        font-size: 16px;
        color: #000 !important;
        font-weight: 500;
    }

    .title-des2 {
        display: contents;
        text-transform: uppercase;
        font-size: 18px;
        color: #458ea7 !important;
        font-weight: 800;
    }

    .title-des3 {
        display: contents;
        text-transform: uppercase;
        font-size: 14px;
        color: #262626 !important;
        font-weight: 800;
    }

    .title-exam {
        padding: 15px;
        border-bottom: 1px solid #e2e2e2;
        border-left: 1px solid #e2e2e2;
        border-right: 1px solid #e2e2e2;
    }

    .border-simple {
        border: 1px solid #e2e2e2;
        padding: 15px 30px;
    }

    .comment-info {
        div {
            font-size: 18px;
        }
    }

    .item-qa {
        // max-height: 200px;
        // overflow: auto;
        // margin-right: -30px;
        // padding-right: 10px;
        
        &::-webkit-scrollbar {
            width: 3px;
        }

        &::-webkit-scrollbar-thumb {
            border-radius: 1px;
            background: #ff940e;
        }
    }

    .footer-list {
        height: 30px;
        z-index: 997;
        margin-top: -30px;
        width: 100%;
        background-image: linear-gradient(#ffffff29, #ffffffd6 30%);
        position: relative;
    }

    .title-list {
        font-size: 14px;
        font-weight: 500;
        line-height: 24px;
        text-transform: uppercase;
    }

    .des-list {
        font-size: 15px;
    }

    .more-list {
        position: absolute;
        top: 0;
        right: 5px;
        color: #005e88;
        font-size: 13px;
        font-weight: 800;
        display: none;
    }
}

.box-list-other {
    .box-list-title {
        padding: 15px 10px;
        border-bottom: 1px solid #dedede;
        text-transform: capitalize;
    }

    .content-list {
        li {
            h4 {
                padding: 3px 0;
            }
        }

        a {
            color: #505050;
            font-size: 15px;
            padding-bottom: 3px;
            line-height: 1.5;

            i {
                width: 17px;
            }

            &:hover {
                color: #005e88;
            }
        }

        .item:not(:last-child) {
            margin-bottom: 20px;
        }

        .item {
            position: relative;
            display: flex;

            .course {
                height: auto;

                .img {
                    width: 135px;
                    float: left;
                    height: auto;
                    overflow: hidden;
                    position: relative;
                }

                .content {
                    padding: 0 0 0 10px;
                    overflow: hidden;

                    .txt {
                        max-height: 45px;
                        height: auto;
                        margin: 0 !important;
                        position: relative;
                        top: -4px;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                        overflow: hidden;
                    }

                    .price {
                        color: #f7941d;
                        text-transform: uppercase;
                        font-weight: 600;
                        margin: 3px 0;
                        font-size: 12px !important;
                    }

                    .old {
                        text-decoration: line-through;
                        color: #999;
                    }

                    .box-star {
                        margin-top: 0 !important;
                    }
                }
            }
        }
    }
}

.question-meta {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 5px;
}

.question-info {
    font-size: 14px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;

    span {
        font-size: 12px;
        color: #9c9c9c;
        font-weight: 400;
    }

    .btn-outline-like {
        padding: 3px 10px;
        border-radius: 5px;
    }
}

@media all and (max-width: 768px) {
    .box-comment-detail {
        padding: 7px !important;
    }

    .main-qa .title-des2 {
        font-size: 15px;
    }

    img {
        height: auto;
    }
}

@media all and (max-width: 480px) {
    .edit-qa {
        display: none;
    }

    .main-qa {
        .title-qa, .border-simple {
            padding: 10px;
        }
    }
}

.link-title_qa {
    color: #0377aa;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
}

.exam-icon_qa {
    margin: -3px 5px 0 0;
    width: 20px;
}

.br-5 {
    border-radius: 5px;
}

.relative {
    position: relative;
}

.answer-check, .result {
    &::-webkit-scrollbar {
        height: 4px;
        width: 4px;
        background: #dedede;
    }

    &::-webkit-scrollbar-thumb {
        background: #ff940e;
        border-radius: 10px;
    }
}

.fb_iframe_widget_fluid span {
    width: 100% !important;
}

.view-reason-btn {
    font-size: 15px;
    font-weight: 500;
    padding: 10px;
    color: #429606;
    cursor: pointer;
}

.answer-container {
    padding: 30px!important;
    box-shadow: rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 2px 6px 2px;

    .answer {
        border-bottom: 0;

        .question-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .question-heading {
            font-size: 20px;
            line-height: 24px;
            letter-spacing: -.4px;
            font-weight: 700;
            padding-bottom: 0;
        }
    }

    @media all and (max-width: 480px) {
        box-shadow: rgba(14, 30, 37, 0.12) 0px 2px 4px 0px, rgba(14, 30, 37, 0.32) 0px 2px 16px 0px;
        padding: 25px 15px!important;

        .result {
            padding: 15px 0;
        }
    }
}

.viewreason {
    font-size: 16px;
    color: #607D8B;
    font-weight: 600;
    cursor: pointer;

    &:hover {
        color: red;
    }
}

.col-5ths {
    position: relative;
    min-height: 1px;
    padding-right: 7px;
    padding-left: 7px;
    margin-bottom: 1rem !important;
    width: 25%;
    float: left;

    @media (max-width: 1024px) {
        width: 50%;
    }

    @media (max-width: 350px) {
        width: 100%;
    }
}

.course-inner {
    border: 1px solid #e6e6e6;
    margin: 5px 0;
}

.course-inner:hover {
    border: 1px solid #ee4d2d;
}

@media (max-width: 1025px) {
    .product-box:nth-of-type(n + 7) {
        display: none;
    }
}

@media (max-width: 500px) {
    .products {
        max-height: 300px;
        overflow-y: auto;
        overflow-x: hidden;
    }
}
.text-more {
    font-size: 16px;
    color: #078b07;
    font-weight: 500;
    text-transform: uppercase;
}
.pbox-products {
    border: 1px solid #dadada;
    background-color: #f6f9fe;
    border-radius: 5px;
    padding: 10px 15px 15px;

    .pbox-header {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        align-items: center;
        justify-content: space-between;
    }

    .pbox-title {
        display: flex;
        align-items: center;
        gap: 7px;
        font-weight: 500;
    }

    .pbox-subtitle {
        font-size: 16px;
        color: #377ebe !important;

        i {
            margin-right: 7px;
            font-size: 15px;
            color: #FF5722;
        }

        span {
            color: green;
            font-weight: 500;
        }
    }

    .phot-icon {
        background: red;
        color: #ffffff;
        font-size: 10px;
        font-weight: 700;
        padding: 3px;
        border-radius: 3px;
        box-sizing: border-box;

        span {
            padding: 1px 2px;
            border: 2px solid #ffffff;
        }
    }

    .pbox-body {
        background-color: #ffffff;
        padding: 10px 10px 5px;
        border-left: 3px solid #607D8B;
        margin-top: 15px;
    }

    .pbox-store li {
        margin: 5px 3px;
    }

    .pbox-store li a {
        font-size: 15px;
        color: #23527c;

        &:hover {
           color: #337ab7;
        }
    }

    ol.pbox-store {
        padding: 0;
        counter-reset: li;
        list-style: none;

        li {
            position: relative;
            padding-left: 32px;
            margin-bottom: 7px;

            &:before {
                position: absolute;
                top: 10%;
                left: 0;
                border: 1px solid #e4e4e4;
                background-color: #ededed;
                border-radius: 50%;
                width: 20px;
                height: 20px;
                line-height: 20px;
                text-align: center;
                font-size: 10px;
                font-weight: 700;
                color: #607D8B;
                content: counter(li);
                counter-increment: li;
            }

            @media (max-width: 500px) {
                &:last-child {
                    display: none;
                }
            }
        }
    }

    .price-store {
        display: inline-block;
        color: #000;
        font-weight: 500;
    }
}

.chat, .go_top {
    display: none !important;
}
