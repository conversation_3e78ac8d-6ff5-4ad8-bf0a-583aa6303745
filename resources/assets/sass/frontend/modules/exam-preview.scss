@media (min-width: 1250px) {
    .container {
        padding-left: 2.5rem;
        padding-right: 2.5rem;
    }
}

.main-page {
    .box-wrapper {
        .box-wrapper-body {
            @media (max-width: 1250px) {
                >* {
                    max-width: 800px;
                }
            }

            @media (min-width: 1600px) {
                gap: 7.5rem;
            }
        }
    }
}

.name-exam {
    margin-top: 10px;
    font-size: 26px;
    line-height: 1.3;

    @media (max-width: 992px) {
        font-size: 22px;
    }

    @media only screen and (max-width: 500px) {
        font-size: 20px;

        &.limit {
            -webkit-box-orient: vertical;
            display: -webkit-box;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: unset;
            -webkit-line-clamp: 1;
        }
    }
}

.tab-exam {
    display: flex;

    li {
        float: left;
        position: relative;
        display: block;
        margin-right: 10px;

        a {
            position: relative;
            display: block;
            padding: 15px 0;
            color: #3b4a54;
            font-weight: 500;
            font-size: 14px;
            text-align: center;
            min-width: 60px;

            &:hover {
                &:after {
                    content: "";
                    width: calc(100% + 1px);
                    height: 2px;
                    background: #f8bb18;
                    position: absolute;
                    bottom: 0;
                    left: -1px;
                    transition: .5s all;
                }
            }
        }

        &.active {
            a {
                background: #e0a609;
                color: #ffffff;
            }
        }
    }
}

.tab-preview {
    margin-bottom: 15px;

    &.pc {
        margin-top: 15px;

        button,
        a {
            outline: none !important;
            color: #2196F3;
            border: 1px solid #2196F3;
            font-weight: 500;
        }
    }

    &.mb {
        display: none;
        margin-bottom: 0;
        padding: 0 15px;
        border-bottom: none;
        margin-top: 10px;

        a {
            padding: 7px 10px;
            font-size: 15px;
            font-weight: 500;
        }
    }

    .active {
        button {
            color: #ffffff;
            background-color: #607D8B;
            border-color: #607D8B;
        }
    }

    @media (max-width: 992px) {
        &.pc {
            display: none;
        }

        &.mb {
            display: block;

            &~.bt-1 {
                padding-top: 3px;
            }
        }
    }
}

.qas {
    td,
    th,
    td,
    th,
    td,
    th {
        padding: 5px 10px !important;
    }
}

.answer-check,
.reason {
    &::-webkit-scrollbar {
        height: 4px;
        width: 4px;
        background: #dedede;
    }

    &::-webkit-scrollbar-thumb {
        background: #ff940e;
    }
}

.answer-item {
    background: #ffffff;
    margin-bottom: 10px;
    padding: 10px 0 0 45px;
    position: relative;
    overflow-x: auto;

    &.correct {
        border: 2px dashed #4CAF50;

        &:after {
            font-weight: 900;
            content: '\f058';
            font-family: 'Font Awesome\ 5 Free';
            color: #08af0f;
            position: absolute;
            left: 18px;
            top: 50%;
            -ms-transform: translateY(-50%);
            transform: translateY(-50%);
        }
    }

    &.true {
        background: rgba(0, 177, 132, 0.2) !important;
        border: none;

        &:after {
            font-weight: 900;
            content: '\f058';
            font-family: 'Font Awesome\ 5 Free';
            color: #08af0f;
            position: absolute;
            left: 18px;
            top: 50%;
            -ms-transform: translateY(-50%);
            transform: translateY(-50%);
        }
    }

    &.false {
        background: rgba(254, 107, 67, 0.2) !important;
        border: none;

        &:after {
            font-weight: 900;
            content: '\f057';
            font-family: 'Font Awesome\ 5 Free';
            color: #ec5151;
            position: absolute;
            left: 18px;
            top: 50%;
            -ms-transform: translateY(-50%);
            transform: translateY(-50%);
        }
    }
}

.pvideo-wrapper {
    position: relative;
    min-height: 200px;
    width: 100%;
    max-width: 600px;

    @media (min-width: 1300px) {
        max-width: 650px;
    }

    @media (min-width: 1366px) {
        max-width: 700px;
    }

    @media (min-width: 1400px) {
        max-width: 800px;
    }

    @media (min-width: 1536px) {
      max-width: 900px;
    }

    .pvideo-header {
        margin-bottom: 15px;
    }

    .pvideo-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 5px;

        .name-exam {
            font-size: 18px !important;
            -webkit-box-orient: vertical;
            display: -webkit-box;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: unset;
            -webkit-line-clamp: 1;
        }
    }

    ~ .rsidebar {
        margin-top: 20px;

        .rsidebar-wrapper {
            margin: 0;
        }
    }
}

.quiz-wrapper {
    position: relative;
    min-height: 200px;
    width: 100%;
    max-width: 600px;

    @media (min-width: 1300px) {
        max-width: 650px;
    }

    @media (min-width: 1366px) {
        max-width: 700px;
    }

    @media (min-width: 1400px) {
        max-width: 800px;
    }

    .quiz-card {
        height: 100%;
        opacity: 0;
        visibility: hidden;
        transition: all 1.2s ease;
        border-radius: 0.5rem;
        overflow: hidden;
        position: relative;
        padding: 0;
    }

    &::before,
    &::after {
        content: "";
        position: absolute;
        background-color: rgba(54, 65, 82, 0.11);
        border-radius: 4px / 6.7px;
        background-image: linear-gradient(90deg,
            rgba(255, 255, 255, 0),
            rgba(255, 255, 255, 0.5),
            rgba(255, 255, 255, 0));
        background-repeat: no-repeat;
        background-size: 100px 200px, 50px 200px, 150px 200px, 350px 200px,
            300px 200px, 250px 200px;
        background-position: 0 0, 0 0, 120px 0, 120px 40px, 120px 80px, 120px 120px;
    }

    &::before {
        top: 0;
        height: 27%;
        width: 100%;
        animation: shine 1.5s ease infinite;
    }

    &::after {
        bottom: 0;
        height: 70%;
        width: 100%;
        animation: shine 1.5s ease infinite;
    }

    &.quiz-initialied {
        .quiz-card {
            opacity: 1;
            visibility: visible;
        }

        &::before,
        &::after {
            content: none;
        }
    }

    .q-done {
        background: #ffffff;
        display: flex;
        flex-direction: column;
        align-items: center;
        box-shadow: 0 0.25rem 1rem 0 rgba(40, 46, 62, 0.1215686275);
        border-radius: 0.5rem;
        padding: 20px;

        .q-noti {
            display: flex;
            align-items: center;
            gap: 10px;
            text-align: center;

            @media (max-width: 450px) {
                display: block;
            }
        }

        .q-check {
            background-color: #edeff4;
            border-radius: 15px;
            display: flex;
            justify-content: space-between;
            padding: 1px 15px;
            margin: 10px 5px;
        }
    }

    .btn-nextq {
        background: #4255ff;
        color: #ffffff;
        padding: 10px 20px;
        font-weight: 500;
        font-size: 20px;
        border-radius: 5px;
        border: none;
        outline: none;
        display: inline-block;
        margin: 10px;

        &:hover:not([disabled]) {
            background: #423ed8;
        }

        &.ani {
            font-size: 16px;
            padding: 7px 15px;
            bottom: 5px;
            right: 5px;
            position: absolute;
            z-index: 401;
            transform: translateY(100%);
            animation: slideUp .25s ease-in 0.5s forwards;
            -webkit-animation: slideUp .25s ease-in 0.5s forwards;
            opacity: 0;

            @media (max-width: 600px) {
                position: static;
                width: 100%;
                margin: 0;
            }
        }
    }

    .btn-default:focus {
        outline: none;
    }

    .mcq-box {
        background: #ffffff;
        color: #000;
        box-shadow: rgba(17, 17, 26, 0.1) 0px 0px 16px;
        position: relative;
        transition: all 1.2s ease;
        border-radius: 0.5rem;
        margin-top: 5px;
        min-height: 500px;
        padding: 20px;

        .mcq-result-icon {
            position: absolute;
            right: 5px;
            top: 5px;
            color: #9c27b0;
            font-size: 16px;
            font-weight: 700;

            &.correct {
                color: #17c917;
            }

            &.wrong {
                color: red;
            }
        }
    }
}

.quiz-heard {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;

    .q-number {
        background: #d9dde8;
        padding: 1px 10px;
        border-radius: 10px;
        font-size: 14px;
        font-weight: 700;
        margin: 8px auto;

        &.correct-count {
            background: #a7e9d2;
            color: #12815a;
        }
    }
}

.quiz-body {
    position: relative;
    scroll-margin-top: 70px;

    &:focus {
        outline: none;
    }
}

.quiz-footer {
    margin: 20px 10px;
    background: transparent;
    text-align: center;

    .quiz-controll {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 20px;
        font-size: 20px;

        .btn-default {
            font-size: 20px;
        }
    }
}

@keyframes shine {
    to {
        // Move shine from left to right, with offset on the right based on the width of the shine - see background-size
        background-position: right -40px top 0;
    }
}

.flashcard-container {
    position: relative;
    cursor: pointer;
    margin-top: 15px;

    &:focus {
        outline: none;
    }

    .flashcard-box {
        margin: 0;
        perspective: 1600px;
        flex: 1 0;
        width: 100%;
        height: 500px;
        overflow: visible;

        .flashcard {
            position: relative;
            width: 100%;
            height: 100%;
            text-align: center;
            transform-style: preserve-3d;
            transition: transform 0.5s cubic-bezier(.33,.11,.02,.99);
            transform-origin: center center;

            .flashcard-content {
                position: absolute;
                inset: 0;
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                -webkit-backface-visibility: hidden;
                backface-visibility: hidden;
                background-color: #ffffff;
                box-shadow: 0 .25rem 1rem 0 #282e3e1f;
                border: 1px solid #d3d3d3;
                border-radius: 0.5rem;
                overflow-wrap: anywhere;
                overflow-y: auto;
            }

            .flashcard-front {
                border: 1px dashed #2196F3;
            }

            .flashcard-back {
                transform: rotateX(180deg);
                border: 1px dashed #FF5722;
            }

            .answer-container {
                height: 100%;
                width: 100%;
                background-color: #efa92933;
                border: 1px solid #efa92933;
            }
        }

        &.is-flipped .flashcard {
            transform: rotateX(180deg);
        }
    }
}

@keyframes flipBounce {
  0%   { transform: rotateX(0deg); }
  60%  { transform: rotateX(200deg) scale(1.05); }
  80%  { transform: rotateX(175deg) scale(0.98); }
  100% { transform: rotateX(180deg) scale(1); }
}

.btn-flip {
    position: absolute;
    right: 5px;
    bottom: 5px;
    z-index: 2;
    font-size: 15px;
    font-weight: 400;
    border: 1px solid #FF9800;
    border-radius: 5px;
    outline: none !important;
    background: #ffffff;
}

.btn-nextflip {
    position: absolute;
    z-index: 2;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size: 30px;
    text-decoration: none;
}

/*
==============================================
bigEntrance
==============================================
*/

.bigEntranceUp {
  animation-name: bigEntranceUp;
  -webkit-animation-name: bigEntranceUp;
  animation-duration: 1.6s;
  -webkit-animation-duration: 1.6s;
  animation-timing-function: ease-out;
  -webkit-animation-timing-function: ease-out;
  visibility: visible !important;
  animation-count: 1;
  animation-delay: 0;
}

@keyframes bigEntranceUp {
  0% {
    transform: scale(0.3) rotate(6deg) translateX(-30%) translateY(30%);
    opacity: 0.2;
  }
  30% {
    transform: scale(1.03) rotate(-2deg) translateX(2%) translateY(-2%);
    opacity: 1;
  }
  45% {
    transform: scale(0.98) rotate(1deg) translateX(0%) translateY(0%);
    opacity: 1;
  }
  60% {
    transform: scale(1.01) rotate(-1deg) translateX(0%) translateY(0%);
    opacity: 1;
  }
  75% {
    transform: scale(0.99) rotate(1deg) translateX(0%) translateY(0%);
    opacity: 1;
  }
  90% {
    transform: scale(1.01) rotate(0deg) translateX(0%) translateY(0%);
    opacity: 1;
  }
  100% {
    transform: scale(1) rotate(0deg) translateX(0%) translateY(0%);
    opacity: 1;
  }
}

@-webkit-keyframes bigEntranceUp {
  0% {
    -webkit-transform: scale(0.3) rotate(6deg) translateX(-30%) translateY(30%);
    opacity: 0.2;
  }
  30% {
    -webkit-transform: scale(1.03) rotate(-2deg) translateX(2%) translateY(-2%);
    opacity: 1;
  }
  45% {
    -webkit-transform: scale(0.98) rotate(1deg) translateX(0%) translateY(0%);
    opacity: 1;
  }
  60% {
    -webkit-transform: scale(1.01) rotate(-1deg) translateX(0%) translateY(0%);
    opacity: 1;
  }
  75% {
    -webkit-transform: scale(0.99) rotate(1deg) translateX(0%) translateY(0%);
    opacity: 1;
  }
  90% {
    -webkit-transform: scale(1.01) rotate(0deg) translateX(0%) translateY(0%);
    opacity: 1;
  }
  100% {
    -webkit-transform: scale(1) rotate(0deg) translateX(0%) translateY(0%);
    opacity: 1;
  }
}

.bigEntranceDown {
  animation-name: bigEntranceDown;
  -webkit-animation-name: bigEntranceDown;
  animation-duration: 1.6s;
  -webkit-animation-duration: 1.6s;
  animation-timing-function: ease-out;
  -webkit-animation-timing-function: ease-out;
  visibility: visible !important;
  animation-iteration-count: 1;
  animation-delay: 0;
}

@keyframes bigEntranceDown {
  0% {
    transform: scale(0.3) rotate(-6deg) translateX(-30%) translateY(-30%);
    opacity: 0.2;
  }
  30% {
    transform: scale(1.03) rotate(2deg) translateX(2%) translateY(2%);
    opacity: 1;
  }
  45% {
    transform: scale(0.98) rotate(-1deg) translateX(0%) translateY(0%);
    opacity: 1;
  }
  60% {
    transform: scale(1.01) rotate(1deg) translateX(0%) translateY(0%);
    opacity: 1;
  }
  75% {
    transform: scale(0.99) rotate(-1deg) translateX(0%) translateY(0%);
    opacity: 1;
  }
  90% {
    transform: scale(1.01) rotate(0deg) translateX(0%) translateY(0%);
    opacity: 1;
  }
  100% {
    transform: scale(1) rotate(0deg) translateX(0%) translateY(0%);
    opacity: 1;
  }
}

@-webkit-keyframes bigEntranceDown {
  0% {
    -webkit-transform: scale(0.3) rotate(-6deg) translateX(-30%) translateY(-30%);
    opacity: 0.2;
  }
  30% {
    -webkit-transform: scale(1.03) rotate(2deg) translateX(2%) translateY(2%);
    opacity: 1;
  }
  45% {
    -webkit-transform: scale(0.98) rotate(-1deg) translateX(0%) translateY(0%);
    opacity: 1;
  }
  60% {
    -webkit-transform: scale(1.01) rotate(1deg) translateX(0%) translateY(0%);
    opacity: 1;
  }
  75% {
    -webkit-transform: scale(0.99) rotate(-1deg) translateX(0%) translateY(0%);
    opacity: 1;
  }
  90% {
    -webkit-transform: scale(1.01) rotate(0deg) translateX(0%) translateY(0%);
    opacity: 1;
  }
  100% {
    -webkit-transform: scale(1) rotate(0deg) translateX(0%) translateY(0%);
    opacity: 1;
  }
}

/*
==============================================
slideRight
==============================================
*/

.slideRight {
    animation-name: slideRight;
    -webkit-animation-name: slideRight;
    animation-duration: 0.6s;
    -webkit-animation-duration: 0.6s;
    animation-timing-function: ease-in-out;
    -webkit-animation-timing-function: ease-in-out;
    visibility: visible !important;
    animation-count: 1;
    animation-delay: 0;
}

@keyframes slideRight {
    0% {
        transform: translateX(-100%);
    }
    50% {
        transform: translateX(5%);
    }
    65% {
        transform: translateX(-2%);
    }
    80% {
        transform: translateX(2%);
    }
    95% {
        transform: translateX(-1%);
    }
    100% {
        transform: translateX(0%);
    }
}

@-webkit-keyframes slideRight {
    0% {
        transform: translateX(-100%);
    }
    50% {
        transform: translateX(5%);
    }
    65% {
        transform: translateX(-2%);
    }
    80% {
        transform: translateX(2%);
    }
    95% {
        transform: translateX(-1%);
    }
    100% {
        transform: translateX(0%);
    }
}

/*
==============================================
slideLeft
==============================================
*/

.slideLeft {
    animation-name: slideLeft;
    -webkit-animation-name: slideLeft;
    animation-duration: 0.6s;
    -webkit-animation-duration: 0.6s;
    animation-timing-function: ease-in-out;
    -webkit-animation-timing-function: ease-in-out;
    visibility: visible !important;
    animation-count: 1;
    animation-delay: 0;
}

@keyframes slideLeft {
    0% {
        transform: translateX(100%);
    }
    // 50% {
    //     transform: translateX(-5%);
    // }
    // 65% {
    //     transform: translateX(2%);
    // }
    // 80% {
    //     transform: translateX(-2%);
    // }
    // 95% {
    //     transform: translateX(1%);
    // }
    100% {
        transform: translateX(0%);
    }
}

@-webkit-keyframes slideLeft {
    0% {
        -webkit-transform: translateX(100%);
    }
    // 50% {
    //     -webkit-transform: translateX(-5%);
    // }
    // 65% {
    //     -webkit-transform: translateX(2%);
    // }
    // 80% {
    //     -webkit-transform: translateX(-2%);
    // }
    // 95% {
    //     -webkit-transform: translateX(1%);
    // }
    100% {
        -webkit-transform: translateX(0%);
    }
}

@keyframes slideUp {
    0% {
        transform: translateY(100%);
    }

    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@-webkit-keyframes slideUp {
    0% {
        -webkit-transform: translateY(100%);
    }

    100% {
        opacity: 1;
        -webkit-transform: translateY(0%);
    }
}

.q-progress {
    width: 100%;
    margin: 10px auto;

    .q-progress-main {
        position: relative;
        overflow: hidden;
        width: 100%;
        background-color: #7b4cad61;
        border-radius: 5px;
        /* box-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.2); */
    }

    .q-progress-bar {
        display: block;
        float: left;
        background-color: #673ab7;
        box-shadow: inset 0px -1px 2px rgba(0, 0, 0, 0.1);
        transition: width 0.8s ease-in-out;
        width: 0%;
        height: 18px;
        border-radius: 5px;
    }

    .q-progress-label {
        position: absolute;
        overflow: hidden;
        left: 0px;
        right: 0px;
        color: rgb(255 255 255);
        font-size: 12px;
        font-weight: 600;
        margin: 0 10px;
    }

    .q-progress-label2 {
        display: block;
        margin: 2px 0;
        padding: 0 8px;
        font-size: 0.8em;
    }

    .q-progress-label3 {
        position: absolute;
        overflow: hidden;
    }

    &.q-progress-danger {
        .q-progress-main {
            background-color: #ec0b4336;
        }

        .q-progress-bar {
            background-color: #ec0b43;
        }

        .q-progress-label {
            color: #6a6a6a;
        }
    }

    &.q-progress-warning {
        .q-progress-main {
            background-color: #ffe60e47;
        }

        .q-progress-bar {
            background-color: #ffe60e;
        }

        .q-progress-label {
            color: #6a6a6a;
        }
    }

    &.q-progress-success {
        .q-progress-main {
            background-color: #4caf5040;
        }

        .q-progress-bar {
            background-color: #4caf50;
        }

        .q-progress-label {
            color: #6a6a6a;
        }
    }

    .q-progress-active {
        // background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.125) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.125) 50%, rgba(255, 255, 255, 0.125) 75%, transparent 75%, transparent);
        background-size: 35px 35px;
        animation: ProgressActive 2s linear infinite;
    }
}

@function pseudo-random($seed, $max) {
    @return (abs(sin($seed) * 10000)) % $max;
}

// Số lượng hạt và kích thước
$particles: 50;
$width: 500;
$height: 500;
$box-shadow: ();
$box-shadow2: ();

// Vòng lặp để tạo box-shadow với giá trị cố định
@for $i from 0 through $particles {
    $x: pseudo-random($i, $width) - $width / 2 + px;
    $y: pseudo-random($i + $particles, $height) - $height / 1.2 + px;
    $hue: pseudo-random($i + 2 * $particles, 360);
    $color: hsl($hue, 100%, 50%);
    $box-shadow: $box-shadow, $x $y $color;
    $box-shadow2: $box-shadow2, 0 0 #ffffff;
}

// Định nghĩa keyframes và class như ban đầu
@keyframes bang {
    to {
        box-shadow: $box-shadow;
    }
}

@keyframes gravity {
    to {
        transform: translateY(0);
        opacity: 0;
    }
}

@keyframes position {

    0%,
    100% {
        margin-top: 20%;
        margin-left: 50%;
    }
}

.pyro>.before,
.pyro>.after {
    position: absolute;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    box-shadow: $box-shadow2;
    animation: bang 1s ease-out 1 backwards,
        gravity 2s ease-in 1 backwards,
        position 5s linear 1 backwards;
}

// .pyro > .after {
//   animation-delay: 1.25s, 1.25s, 1.25s;
//   animation-duration: 1.25s, 1.25s, 6.25s;
// }

.quizmode {
    display: flex;
    align-items: center;
    width: 100%;
    overflow-x: auto;
    gap: 15px;
    padding: 5px 0 10px 0;

    .quizmode-btn {
        font-size: 16px;
        font-weight: 500;
        padding: 7px 15px;
        border: none;
        border-radius: 5px;
        box-shadow: 0 .0625rem .1875rem 0 #282e3e1f;
        border-bottom: .25rem solid #0000;
        width: 160px;
        outline: none;
        background: #ffffff;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        flex-shrink: 0;

        &.active {
            background: #eeff45;
        }

        &:hover {
            box-shadow: 0 .25rem 1rem 0 #282e3e1f;
            border-bottom-color: #dbdfff;
        }

        .quizmode-icon {
            height: 32px;
            width: 32px;
            flex-shrink: 0;
        }

        @media (max-width: 600px) {
            padding: 5px 10px;
            width: 130px;
            font-size: 14px;
            gap: 5px;

            .quizmode-icon {
                height: 25px;
                width: 25px;
            }
        }
    }
}

.number-item {
    background: #ffffff;
    border: 1px solid #607D8B;
    color: #607D8B;
    border-radius: 30px;
    height: 40px;
    width: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 7px;
    margin-bottom: 7px;
    cursor: pointer;

    &:last-child {
        margin-right: 0;
    }

    &.true {
        background: #4CAF50;
        color: #ffffff;
    }

    &.false {
        background: red;
        color: #ffffff;
    }
}

.question-header {
    color: #900;
    font-size: 16px;
    font-weight: 500;

    &.true {
        color: #4CAF50;
    }

    &.false {
        color: red;
    }
}

.go_top {
    display: none !important;
}

// .quiz-action {
//     display: flex;
//     width: 100%;
//     gap: 10px;
//     margin: 10px;

//     .btn-soft {
//         outline: 0!important;
//         background-color: rgba(6, 106, 201, .1);
//         color: #066ac9;

//         &:hover {
//             background-color: #066ac9;
//             color: #fff;
//         }
//     }
// }

@import '_quiz';
