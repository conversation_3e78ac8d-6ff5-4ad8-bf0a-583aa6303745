/*
 * Style Select2
 * ---------------
 */
.form-control {
    border-radius: 0;
    box-shadow: none;
    border-color: #d2d6de;
}

.select2-container--default.select2-container--focus {
    outline: none;
    .select2-selection--multiple {
        border-color: #3c8dbc !important;
        border-color: #d2d6de;
    }
}
.select2-selection.select2-container--focus {
    outline: none;
}
.select2-container--default {
    &:focus {
        outline: none;
    }
    &:active {
        outline: none;
    }
    .select2-selection--single {
        border: 1px solid #d2d6de;
        border-radius: 0;
        padding: 6px 12px;
        height: 34px;
        .select2-selection__arrow {
            height: 28px;
            right: 3px;
            b {
                margin-top: 0;
            }
        }
    }
    .select2-results__option--highlighted[aria-selected] {
        background-color: #3c8dbc;
        color: white;
    }
    .select2-search--dropdown {
        .select2-search__field {
            border-color: #3c8dbc !important;
        }
    }
    .select2-results__option[aria-disabled=true] {
        color: #999;
    }
    .select2-results__option[aria-selected=true] {
        background-color: #ddd;
        color: #444;
        &:hover {
            color: #444;
        }
    }
    .select2-selection--multiple {
        border: 1px solid #d2d6de;
        border-radius: 0;
        &:focus {
            border-color: #3c8dbc;
        }
        .select2-selection__choice {
            background-color: #3c8dbc;
            border-color: #367fa9;
            padding: 1px 10px;
            color: #ffffff;
        }
        .select2-selection__choice__remove {
            margin-right: 5px;
            color: rgba(255, 255, 255, 0.7);
            &:hover {
                color: #ffffff;
            }
        }
    }
}
.select2-selection {
    &:focus {
        outline: none;
    }
    &:active {
        outline: none;
    }
    .select2-selection--single {
        border: 1px solid #d2d6de;
        border-radius: 0;
        padding: 6px 12px;
        height: 34px;
    }
}
.select2-container--default.select2-container--open {
    border-color: #3c8dbc;
}
.select2-dropdown {
    border: 1px solid #d2d6de;
    border-radius: 0;
    .select2-search__field {
        border: 1px solid #d2d6de;
        &:focus {
            outline: none;
        }
    }
}
.select2-results__option {
    padding: 6px 12px;
    user-select: none;
    -webkit-user-select: none;
    font-size: 14px;
}
.select2-container {
    .select2-selection--single {
        .select2-selection__rendered {
            padding-left: 0;
            padding-right: 0;
            height: auto;
            margin-top: -4px;
            padding-right: 10px;
        }
    }
}
.select2-container[dir="rtl"] {
    .select2-selection--single {
        .select2-selection__rendered {
            padding-right: 6px;
            padding-left: 20px;
        }
    }
}
.select2-search--inline {
    .select2-search__field {
        border: 1px solid #d2d6de;
        &:focus {
            outline: none;
        }
    }
}

.select2 {
    width: 100% !important;
}
