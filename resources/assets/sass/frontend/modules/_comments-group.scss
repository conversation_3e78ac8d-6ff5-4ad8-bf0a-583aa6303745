.commnets-group {
    background: #ffffff;
    border-radius: 7px;
    padding: 20px 15px 3px;
    margin-bottom: 30px;

    .btn-comment {
        all: unset; 
        position: absolute; 
        right: 20px; 
        cursor: pointer;
        font-size: 20px; 
        top: 7px; 
        color: #169bd5;
    }
    .panel {
        position: relative;
        display: inline-block;
        width: 100%;
        border: none;
        box-shadow: none;
        margin: 0;
        border-radius: 0;

        .panel-body {
            padding: 0;
        }

        textarea {
            border-radius: 20px; 
            margin-left: 10px; 
            padding: 5px 40px 5px 15px;
            resize: none;
            outline: none;
            width: 100%;
            font-size: 16px;
            line-height: 30px;
            color: #555;
            overflow-y: hidden;
            background: white;
        }

        .view-reply {
            color: #313131;
            font-size: 14px;
        }
    }

    .media {
        &:first-child {
            margin-top: 5px;
        }
    }
    .media-body {
        color: #607D8B;
        overflow: visible;

        .media-heading {
            font-size: 14px;
            color: #009688;

            > span {
                font-size: 12px;
                color: #D5164E;
                margin-left: 5px;
            }
        }

        p {
            font-size: 16px;
            margin: 10px 0 5px;
            color: #555;
        }

        .nav>li>* {
            color: #607d8b;
            padding: 3px 0;
            font-size: 13px !important;
            margin-right: 20px;

            &:hover,
            &:focus,
            &:active {
                background-color: transparent;
            }
        }

    }
}

.has-error .help-block {
    color: red;
    padding-left: 10px;
    font-size: 14px;
}

.grid-images {
    position: relative;

    .remove-img {
        cursor: pointer;
        position: absolute;
        left: 10px;
        top: 10px;
        color: #ffffff;
        background: #818181;
        border: 1px solid #ffffff;
        padding: 10px;
    }
}

.form-comment {
    position: relative;

    .js-upload-btn {
        position: absolute;
        left: 62px;
        bottom: 5px;
        display: none;
        z-index: 2;
        background: #ffffff;
    }
}
