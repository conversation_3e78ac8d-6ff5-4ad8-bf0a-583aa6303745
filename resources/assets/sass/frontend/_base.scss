body {
    line-height: 1.6;
    font-size: 16px;
    color: $text-color-default;
    background-color: #e8eaed;
    font-family: 'Roboto', sans-serif;
}

// Links
a {
    color: $link-color;
    text-decoration: none;
    @include transition(color .1s ease-in-out);

    &:hover,
    &:focus {
        outline: none;
        text-decoration: none;
    }
}

ul {
    padding: 0;
    margin: 0;

    li {
        list-style: none;
    }
}

b,
strong {
    font-weight: 700;
}

.container {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .container {
    max-width: 96%;
  }
}
@media (min-width: 768px) {
  .container {
    max-width: 94%;
  }
}
@media (min-width: 992px) {
  .container {
    max-width: 94%;
  }
}
@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
}
@media (min-width: 1366px) {
  .container {
    max-width: 1230px;
  }
}
@media (min-width: 1400px) {
  .container {
    max-width: 1320px;
  }
}

.checkbox label:after,
.radio label:after {
    content: '';
    display: table;
    clear: both;
}

.checkbox .cr,
.radio .cr {
    position: relative;
    display: inline-block;
    border: 1px solid #898989;
    border-radius: .25em;
    width: 1em;
    height: 1em;
    float: left;
    margin-right: .5em;
}

.radio .cr {
    border-radius: 50%;
}

.checkbox .cr .cr-icon,
.radio .cr .cr-icon {
    position: absolute;
    font-size: .8em;
    line-height: 0;
    top: 50%;
    left: 20%;
}

.radio .cr .cr-icon {
    margin-left: 0.04em;
}

.checkbox label input[type="checkbox"],
.radio label input[type="radio"] {
    display: none;
}

.checkbox label input[type="checkbox"]+.cr>.cr-icon,
.radio label input[type="radio"]+.cr>.cr-icon {
    transform: scale(3) rotateZ(-20deg);
    opacity: 0;
    transition: all .3s ease-in;
}

.checkbox label input[type="checkbox"]:checked+.cr>.cr-icon,
.radio label input[type="radio"]:checked+.cr>.cr-icon {
    transform: scale(1) rotateZ(0deg);
    opacity: 1;
}

.checkbox label input[type="checkbox"]:disabled+.cr,
.radio label input[type="radio"]:disabled+.cr {
    opacity: .5;
}

.btn-default-danger {
    border: 1px solid #E91E63;
    @include border-radius(2px);
    overflow: hidden;
    line-height: 42px;
    padding: 0 15px;
    color: #ffffff!important;
    background-color: #E91E63;
    text-align: center;
    display: inline-block;
    font-size: 18px;

    &:hover,
    &:focus,
    &.active {
        border-color: #c10847;
        background-color: #c10847;
    }

    &.btn-small {
        height: 32px;
        line-height: 30px;
        font-size: 16px;
        padding: 0 30px;
    }
}

.btn-default-yellow {
    border: 1px solid $color-yellow;
    @include border-radius(2px);
    overflow: hidden;
    line-height: 42px;
    padding: 0 15px;
    color: #ffffff!important;
    background-color: $color-yellow;
    text-align: center;
    display: inline-block;
    font-size: 18px;

    &:hover,
    &:focus,
    &.active {
        border-color: #FF5722;
        background-color: #FF5722;
    }

    &.btn-small {
        height: 32px;
        line-height: 30px;
        font-size: 16px;
        padding: 0 30px;
    }
}

.btn-default-white {
    border: 1px solid $color-999;
    @include border-radius(2px);
    overflow: hidden;
    line-height: 42px;
    padding: 0 15px;
    color: #ffffff!important;
    background-color: $color-999;
    text-align: center;
    display: inline-block;
    font-size: 18px;

    &:hover,
    &:focus,
    &.active {
        border-color: #6a6868;
        background-color: #6a6868;
    }

    &.btn-small {
        height: 32px;
        line-height: 30px;
        font-size: 16px;
        padding: 0 30px;
    }
}

.btn-white {
    height: 28px;
    line-height: 26px;
    @include border-radius(20px);
    padding: 0 16px;
    border: 1px solid $color-border;
    display: inline-block;
    color: $color-666;
    background-color: #ffffff;

    &:hover,
    &.active {
        background-color: $color-999;
        color: #ffffff;
    }

    &:focus {
        border: 1px solid $color-border;
        color: $color-666;
        background-color: #ffffff;
    }
}

.btn-start {
    text-align: center;
    text-transform: uppercase;
    font-weight: 600;
    border-radius: 4px;
    background-color: #ff5722;
    outline: 0;
    cursor: pointer;
    width: auto;
    vertical-align: middle;
    color: #ffffff;
    text-decoration: none;
    -webkit-font-smoothing: antialiased;
    font-size: 16px;
    padding: 0 20px;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 40px;
    height: 40px;
    margin: 0;

    &:hover, &:focus {
        background-color: red;
        color: #ffffff;
    }

    &.btn-start-exam {
        width: 280px;
        font-size: 14px;
        padding: 0 3px;
    }

    &.btn-outline-like {
        width: 150px;
        font-size: 14px;
        padding: 0 3px;
    }
}

.btn-outline-like {
    background-color: #ffffff !important;
    border: 1px solid #FF5722 !important;
    color: #FF5722 !important;
    text-transform: inherit !important;
    outline: none !important;
    box-shadow: none !important;
}

.heart-color {
    color: #FF5722;
}

.custom-btn-wrapper {
    display: flex;
    gap: 7px;
    align-items: center;
    justify-content: center;

    .btn-down {
        font-size: 14px;
        padding: 0 3px;
        width: 150px!important;
        height: 40px;
        line-height: 40px;
    }

    @media (max-width: 335px) {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    
        .btn-start, .btn-down {
            width: 100% !important;
        }
    }
}

.text-white {
    color: #ffffff!important;
}

.p-title {
    font-weight: 700;
    color: #000;
    font-size: 22px;
    text-transform: uppercase;
    margin: 0 0 15px 0;
    line-height: 25px;

    @media (max-width: 480px) {
        font-size: 18px;
    }
}

.box-shareq {
    padding: 0 15px 15px;
    text-align: center;

    .bgroup-action {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
        width: 100%;
        margin-bottom: 10px;
    }

    @media (max-width: 1200px) {
        padding: 0 0 15px;

        .bgroup-action {
            grid-template-columns: repeat(1, 1fr);
        }
    }

    @media (max-width: 992px) {
        .box-shareq-body {
            display: flex;
            gap: 10px;

            .bgroup-action {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    }

    @media (max-width: 600px) {
        .box-shareq-body {
            .bgroup-action {
                grid-template-columns: repeat(1, 1fr);
            }
        }
    }

    @media (max-width: 300px) {
        .box-shareq-body {
            display: block;
        }
    }
}

.btn-copy {
    color: #ffffff!important;
    font-weight: 500;
    background-color: #9C27B0;
    background-image: linear-gradient(90.57deg, #FF5722 0%, #9C27B0 100%);
    outline: none !important;
}

.btn-down {
    color: #ffffff!important;
    font-weight: 500;
    background-color: #009688;
    outline: none !important;
}

.box-qr {
    padding: 5px 5px 0;

    .btn-link {
        outline: none !important;
        margin: 0;
        font-weight: 500;
    }
}

@media (max-width: 1200px) {
    .text-lg-none {
        display: none;
    }
}

[data-notify="container"] {
    max-width: 400px;
}

.progress[data-notify="progressbar"] {
    margin: 5px;
    height: 4px;
}

.media-group {
    display: flex;
    overflow-x: auto;
    overscroll-behavior-x: contain;
    gap: 20px;
    max-width: 100%;
}

.ex-item {
    width: 50%;
    max-width: 35rem;
    min-width: 30rem;
    position: relative;
    flex-grow: 1;
    border: 1px solid #adc8d5;
    border-radius: .5rem;

    p {
        font-size: 18px;
        color: #000;
    }

    h3 {
        display: -webkit-box;
        margin: 10px 0;
        height: 47px;
        overflow: hidden;
        font-size: 17px;
        line-height: 1.3;
        font-weight: 500;
        color: #444;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    .ex-action {
        display: flex;
        justify-content: space-between;
        gap: 5px;
        flex-direction: row-reverse;
    }
}

.enterExam {
    font-size: 16px;
    font-weight: 500;
    padding: 10px;
    border: none;
    background: transparent;
    color: #607D8B;
    border-radius: 0;
    cursor: pointer;
}

.bg-profile {
    height: 100px;
    background-color: #264666;
    display: flex;
    align-items: center;
}

[data-toggle="scroll"] {
    cursor: pointer;
}

.sidebar-overlay {
    position: fixed;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.7);
    z-index: 998;
    display: none;
    top: 0;
}

.breadcrumb {
    background-color: transparent !important;
    margin: 8px 0 !important;
    padding: 0;
    white-space: nowrap;
    overflow: hidden;
    font-size: 14px;

    @media (max-width: 992px) {
        font-size: 12px;
    }

    &>li+li:before {
        content: "»" !important;
        padding: 0 5px;
    }

    li {
        a {
            color: inherit;
        }
    }
}

.text-center {
    position: relative;
}

.medium-zoom-image {
    max-width: 100%;
    z-index: 9999;
}

.box-adv {
    margin: 40px 0;

    &.bg-adv {
        background: #ffffff;
        border-radius: 5px;
        padding-bottom: 15px;
    }

    &.border-adv {
        border: 1px solid #e2e2e2;
    }

    &.border-adv-top {
        border-top: 1px solid #f2f2f2;
    }

    &.adv-center iframe {
        margin: auto !important;
    }

    div {
        margin: 0 auto !important;
    }

    .label-adv {
        display: block;
        margin: 0;
        text-align: center;
        position: relative;
        color: #a7a7a7;
        text-transform: uppercase;
        font-size: 10px;

        span {
            background: #ffffff;
            display: inline-block;
            padding: 0 3px;
            position: relative;
            top: -7px;
        }
    }
}

.slider {
    display: none;
    opacity: 0;
    visibility: hidden;
    transition: all 1.2s ease;

    &.slick-initialized {
        opacity: 1;
        visibility: visible;
        display: block;
    }
}

@media (min-width: 992px) {
    .show-mobile {
        display: none!important;
    }
}

@media (max-width: 991px) {
    .hide-mobile {
        display: none!important;
    }
}

.scroll-off {
    overflow: hidden;
    position: relative;
    display: flex;
    width: 100%;
    height: 40px;
}

.scroll-on {
    position: relative;
    overflow-x: scroll;
    display: flex;
    scroll-snap-type: x mandatory;
    height: 100px;
}

.scroll-slider {
    opacity: 0;

    ul {
        display: flex;
        width: auto;
        height: 40px;
        margin: 0;
        padding: 0;
        list-style: none;
        gap: 8px;

        li {
            scroll-snap-align: start;
            line-height: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            height: 40px;
            cursor: pointer;
            -moz-user-select: none;
            -webkit-user-select: none;
            -ms-user-select: none;
            user-select: none;
            color: #0a75e6;
            font-weight: 600;
            white-space: nowrap;

            &.active {
                background: #fff59d !important;
                color: #ffffff;
                position: relative;
            }
        }
    }

    .scroll-overlay {
        content: "";
        position: absolute;
        top: 0;
        bottom: 0;
        width: 70px;
        z-index: 5;
        cursor: pointer;
        // pointer-events: none;
        transition: opacity 200ms ease;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
            padding: 0 10px;
        }
    }

    .scroll-overlay:first-of-type {
        left: 0;
        opacity: 0;
        background-image: linear-gradient(to right, #d2d4d9, rgba(255, 255, 255, 0));
        justify-content: flex-start!important;
    }

    .scroll-overlay:last-of-type {
        right: 0;
        background-image: linear-gradient(to left, #d2d4d9, rgba(255, 255, 255, 0));
        justify-content: flex-end!important;
    }
}

.q-separator {
    color: #666f74;
    font-size: 16px;
    margin-bottom: 20px;
    margin-top: 20px;
    position: relative;
    text-align: center;

    &:before {
        border-top: 1px solid #e2e7ea;
        bottom: 0;
        content: "";
        left: 0;
        margin: 0 auto;
        position: absolute;
        right: 0;
        top: 50%;
        width: 95%;
        z-index: 0;
    }

    & > :first-child {
        background-color: #ffffff;
        display: inline-block;
        position: relative;
        color: #ff9700;
        padding: 0 30px;
    }
}

.img-byname {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.chart-line {
    @include border-radius(2px);
    height: 3px;
    background-color: #eee;
    position: relative;
    margin-top: 15px;
    margin-bottom: 10px;

    .finish {
        background-color: $color-yellow;
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
    }
}

.has-underline {
    &:hover {
        text-decoration: underline;
    }
}

.bg-overflow {
    background-color: rgba(0, 0, 0, .5);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 997;
    display: none;
}

.bg-overflow.active {
    display: block;
}

.red {
    color: #fe4343 !important;
}

.limit-content {
    position: relative;
    max-height: var(--max-h, 200px);
    overflow: hidden;
    transition: max-height .3s ease;
    margin-bottom: 5px;

    &.has-limit {
        /* Dải fade bằng mask-image */
        -webkit-mask-image: linear-gradient(
            to top,
            rgba(255,255,255,0) 0%,
            rgba(255,255,255,1) 60%,
            rgba(255,255,255,1) 100%
        );
        mask-image: linear-gradient(
            to top,
            rgba(255,255,255,0) 0%,
            rgba(255,255,255,1) 60%,
            rgba(255,255,255,1) 100%
        );
        /* Đảm bảo mask áp cho toàn block */
        -webkit-mask-size: 100% 100%;
        mask-size: 100% 100%;

        // dùng mask-image thì ko cần after
        // &::after {
        //   content: "";
        //   position: absolute;
        //   left: 0; right: 0; bottom: 0;
        //   height: 50px;               /* cao dải fade, bạn chỉnh tuỳ ý */
        //   pointer-events: none;       /* click xuyên xuống nội dung */
        //   background: linear-gradient(
        //     to bottom,
        //     rgba(255,255,255,0) 0%,
        //     rgba(255,255,255,0.6) 60%,
        //     rgba(255,255,255,1) 100%
        //   );
        //   transition: opacity .3s ease;
        // }
    }

    &.expanded {
      max-height: none;
      /* Bỏ mask khi expanded */
      -webkit-mask-image: none;
      mask-image: none;

        // &::after {
        //   opacity: 0;
        //   visibility: hidden;
        // }
    }
}

.limit-content-btn {
    position: relative;
    align-items: center;
    color: #6d28d2;
    border: none;
    border-radius: 0.4rem;
    background-color: transparent;
    cursor: pointer;
    display: inline-flex;
    padding: 3px 7px;
    justify-content: center;
    user-select: none;
    -webkit-user-select: none;
    vertical-align: bottom;
    white-space: nowrap;
    background-color: transparent;
    font-size: 15px;
    margin-bottom: 15px;

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-52%);
      height: 1px;
      width: 80%;
      background-color: #c094f7;
    }

    &[aria-expanded="true"]::after {
        transform: translateX(-42%);
        width: 65%;
    }

    &:hover {
        color: #6d28d2;
        background-color: rgba(109, 40, 210, 0.12);
    }
}

.box-hint {
    filter: blur(7px);

    .limit-content-btn {
        display: none;
    }

    @media (max-width: 500px) {
        .limit-content {
            max-height: 50px !important;
        }
    }
}

.fb_iframe_widget_fluid_desktop iframe {
    width: 100%!important;
}

nav.nav-menu-list {
    position: relative;
    display: flex;
    align-items: center;
    margin: 20px 0;
    border-bottom: 1px solid #ddd;

    &:not(:has(.active)) {
        border: 1px solid #ddd;
        background: #fffaf0;
    }

    ul, li {
        list-style-type: none;
    }

    button {
        align-self: stretch;
        transition: all .4s ease-out;
        padding: 0 1rem;
        outline: 0;
        border: 0;
        background: #eaeaea;
        color: #00a96c;
        font-weight: 400;
        font-size: 14px;
        position:  relative;
        min-height: 40px;

        &::after {
            position: absolute;
            content: attr(count);
            background: #a2a2a2;
            width: 16px;
            height: 16px;
            color: #ffffff;
            /* box-shadow: 0 0 1px 0 rgba(0,0,0,0.8); */
            border-radius: 50%;
            font-size: 10px;
            line-height: 16px;
            top: 1px;
            right: 1px;
        }

        &.hidden {
            transition: none;
            width: 0;
            padding: 0;
            overflow: hidden;

            &::after {
                content: none;
            }
        }
    }

    ul.links {
        display: flex;
        justify-content: flex-start;
        flex: 1;
        margin: 0;
        overflow: hidden;
        margin-bottom: -1px;

        li {
            flex: none;
            margin-bottom: -1px;

            a {
                display: block;
                padding: 7px 15px;
                margin-right: 2px;
                color: #404040;
                border-radius: 4px 4px 0 0;

                &:hover {
                    background: #00b6ff0f;
                    color: #00aeef;
                }
            }

            &.active {
                a {
                    background-color: #ffffff;
                    border: 1px solid #ddd;
                    border-bottom-color: transparent;
                    color: #ff5722;
                    font-weight: 600;
                }
            }
        }
    }

    ul.hidden-links {
        position: absolute;
        background: #fff;
        box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px;
        right: 0;
        top: 100%;
        z-index: 2;
        padding: 0.5rem;
        min-width: 200px;

        &.hidden {
            display: none;
        }

        li {
            a {
                display: block;
                padding: 0.5rem 1rem;
                color: #404040;

                &:hover {
                    color: #00aeef;
                    background: #00b6ff0f;
                }
            }

            &.active {
                a {
                    background: #eee;
                    color: #ff5722;
                    font-weight: 600;
                }
            }
        }
    }
}
