.box-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, .5);
    display: none;
    z-index: 9999;

    .main-popup {
        position: fixed;
        width: 560px;
        @include border-radius(3px);
        overflow: hidden;
        background-color: #ffffff;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        padding: 20px 30px;

        .header-popup {
            position: relative;

            .txt-popup {
                margin: 0 0 25px 0;
                color: $color-333;
                font-size: 18px;
                line-height: 1.4;
            }

            .close-popup {
                position: absolute;
                top: -10px;
                right: -10px;
                width: 30px;
                height: 30px;
                z-index: 9999;
                cursor: pointer;

                &:before {
                    font-weight: 900;
                    content: '\f00d';
                    font-family: 'Font Awesome\ 5 Free';
                    position: absolute;
                    top: 20%;
                    left: 32%;
                    color: #ffffff;
                    font-size: 16px;
                }
            }
        }
    }

    .form-group {
        position: relative;
        margin-bottom: 20px;

        .input-form {
            border-left: none;
            border-top: none;
            border-right: none;
            outline: none!important;
            height: 45px;
            line-height: 45px;
            padding: 0;
            border-bottom: 1px solid #CFD8DC;
            width: 100%;
        }

        .box-errow {
            position: absolute;
            bottom: -25px;
            z-index: 1;
            left: 0;
        }

        .box-check-small {
            padding: 10px 0 0 0;
        }
    }

    .btn-default-yellow {
        height: 36px;
        line-height: 34px;
        width: 170px;
        text-align: center;
        @include border-radius(20px);
        margin-top: 20px;
        font-weight: 700;
    }
}

.box-popup-login {
    .main-popup {
        padding: 0;
        width: 700px;

        .header-popup .close-popup {
            top: 0;
            right: 0;
        }
    }

    .left,
    .right {
        width: 50%;
        float: left;
        position: relative;
    }

    .popup-login,
    .popup-register {
        position: relative;
        overflow: hidden;
        clear: both;
        display: block;
    }

    .content-popup {
        position: relative;

        &:before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            right: 0;
            background-image: url("/images/icons/bg-signin.png");
            background-size: cover;
            background-color: $color-yellow-1;
            bottom: 0;
        }
    }

    .left {
        padding: 40px 30px 0 30px;

        .txt-popup {
            color: #52b700;
            font-size: 18px;
            text-align: center;
            display: block;
            margin: 0 0 35px 0;
            font-weight: 700;
        }

        .bottom {
            position: relative;
            margin-top: 45px;
            padding: 12px 0 15px 0;
            color: $color-666;
            font-size: 16px;

            &:before {
                content: '';
                position: absolute;
                left: -30px;
                right: -30px;
                top: 0;
                height: 1px;
                background-color: #efefef;
            }

            a {
                color: $color-yellow-1;
                font-weight: 900;
            }

            p {
                margin: 0;
            }
        }

        .login-other {
            margin-bottom: 20px;

            a {
                color: $color-666;
                font-size: 16px;

                i {
                    padding-right: 5px;
                }

                &.google {
                    padding-right: 15px;

                    i {
                        color: #ff4b4b;
                    }
                }

                &.facebook {
                    padding-left: 15px;

                    i {
                        color: #3f639f;
                    }
                }
            }
        }

        .text-change-login {
            margin-bottom: 20px;

            p {
                margin: 0;
                display: inline-block;
                position: relative;
                width: 170px;
                margin: 0;

                &:before {
                    content: '';
                    position: absolute;
                    height: 1px;
                    background-color: #efefef;
                    top: 50%;
                    left: 0;
                    right: 0;
                    width: 100%;
                }

                span {
                    font-size: 13px;
                    color: $color-999;
                    background-color: #ffffff;
                    width: 50px;
                    position: relative;
                    z-index: 1;
                    display: inline-block;
                }
            }
        }

        .txt-forgot {
            margin-top: 20px;

            a {
                font-size: 14px;
                color: $color-666;
            }
        }
    }

    .right {
        color: #ffffff;
        padding: 0 40px;
        font-size: 12px;

        .logo {
            margin: 120px 0 80px 0;
            font-size: 32px;
            text-align: center;
            display: block;
            font-weight: 700;
        }

        p {
            // text-align: justify;
            margin: 0;
            font-size: 16px;
            text-align: center;
        }
    }
}

// custom box-login
.box-popup-login-sm {
    .main-popup {
        max-width: 370px;

        .content-popup {
            &:before {
                content: none;
            }

            .left {
                width: 100%;
            }

            .right {
                display: none;
            }
        }
    }
}

@media only screen and (max-width : 1024px) {
    .box-popup-login-sm {
        .main-popup {
            max-width: initial;
        }
    }
}

@media all and (max-width: 767px) {
    .box-popup-login .main-popup {
        width: 100%;
    }

    .box-popup .main-popup {
        width: 96%;
        overflow-y: auto;
    }
}

@media all and (max-width: 540px) {
    .close-popup {
        &:before {
            color: #ff9700 !important;
        }
    }
}

@media all and (max-width: 480px) {
    .box-popup-login {
        .left,
        .right {
            width: 100%;
        }

        .content-popup:before {
            display: none;
        }

        .left .txt-popup {
            margin-bottom: 30px;
        }

        .right {
            position: relative;

            &:before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                background-image: url("/images/icons/bg-signin.png");
                background-size: cover;
                background-color: $color-yellow-1;
                bottom: 0;
            }

            span,
            p {
                position: relative;
            }

            p {
                padding-bottom: 40px;
            }

            .logo {
                margin: 40px 0 20px 0;
            }
        }
    }
}
