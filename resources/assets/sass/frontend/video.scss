@import '~video.js/dist/video-js.css';
@import '~@silvermine/videojs-quality-selector/dist/css/quality-selector.css';

.main-video-view {
    position: relative;
    box-shadow: rgb(38, 57, 77) 0px 20px 30px -10px;

    .btn-muted {
        position: absolute;
        z-index: 999;
        background-color: rgba(0, 0, 0, .5);
        right: 5px;
        top: 5px;
        color: #ffffff;
    }

    .video-js {
        width: 100% !important;
    }
}

$browser-context: 16;

@function em($pixels, $context: $browser-context) {

    @return #{$pixels/$context}em;
}

@mixin transition($string: $transition--default) {
    -webkit-transition: $string;
    -moz-transition: $string;
    -o-transition: $string;
    transition: $string;
}

.video-js {
    .vjs-upnext-content {
        font-size: 1.8em;
        font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
        pointer-events: auto;
        position: absolute;
        top: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.6);
        width: 100%;

        @include transition(opacity 0.1s);
    }

    .vjs-upnext-top {
        width: 100%;
        position: absolute;
        z-index: 1;
        margin-left: auto;
        margin-right: auto;
        bottom: 50%;
        margin-bottom: 60px;
    }

    .vjs-upnext-bottom {
        width: 100%;
        position: absolute;
        margin-left: auto;
        margin-right: auto;
        top: 50%;
        margin-top: 52px;
        z-index: 999;
    }

    .vjs-upnext-cancel {
        display: block;
        float: none;
        text-align: center;
    }

    .vjs-upnext-headtext {
        display: block;
        font-size: 14px;
        text-align: center;
        padding-bottom: 7px;
    }

    .vjs-upnext-title {
        display: block;
        padding: 10px 10px 2px;
        text-align: center;
        font-size: 22px;
        font-weight: 600;
        overflow: hidden;
        white-space: nowrap;
        word-wrap: normal;
        -o-text-overflow: ellipsis;
        text-overflow: ellipsis;
    }

    .vjs-upnext-cancel-button {
        cursor: pointer;
        display: inline-block;
        float: none;
        padding: 10px !important;
        font-size: 16px !important;
        border: none;
    }

    .vjs-upnext-cancel-button,
    .vjs-upnext-cancel-button:focus {
        outline: 0;
    }

    .vjs-upnext-cancel-button:hover {
        background-color: rgba(255, 255, 255, 0.25);
        border-radius: 2px;
    }

    &.vjs-no-flex .vjs-upnext-content {
        padding-bottom: 1em;
    }

    .vjs-upnext-autoplay-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 98px;
        height: 98px;
        margin: -49px 0 0 -49px;
        -moz-transition: all 0.1s cubic-bezier(0.4, 0, 1, 1);
        -webkit-transition: all 0.1s cubic-bezier(0.4, 0, 1, 1);
        transition: all 0.1s cubic-bezier(0.4, 0, 1, 1);
        cursor: pointer;
        z-index: 999;
    }

}

.video-js.vjs-upnext--showing {
    pointer-events: none;
}

.video-slider {
    .video {
        padding: 7px;
        position: relative;

        .info {
            min-height: 80px;
        }

        .caption {
            display: -webkit-box;
            color: #26282a;
            font-size: 15px;
            line-height: 20px;
            word-break: break-word;
            font-weight: inherit;
            overflow: hidden;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            margin: 5px 0;
            font-weight: 500;
        }

        .author-avatar {
            display: inline-table;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            margin-right: 5px;
            background-image: linear-gradient(red, yellow);
            background-repeat: no-repeat;
            background-position: top;
            background-size: cover;
            border: 1px solid rgba(0,0,0,.1);
        }

        .author-des {
            align-self: center;
            display: -webkit-box;
            overflow: hidden;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            font-size: 13px;
            line-height: 1.3;
        }

        .overflow-video {
            position: absolute;
            background: transparent;
            width: 100%;
            height: 100%;
            z-index: 2;
            cursor: pointer;
        }

        .tag-video {
            margin-left: 25px;
            background-color: rgba(0, 0, 0, .07);
            font-size: 13px;
            padding: 2px 7px;
            border-radius: 4px;
            color: rgba(0, 0, 0, .7);
        }
    }

    .vjs-autoplay-icon {
        position: absolute;
        top: 23%;
        left: 41%;
        width: 40px;
        height: 40px;
        cursor: pointer;
        z-index: 999;
    }

    .video-js {
        .vjs-control-bar {
            bottom: 65px;
        }
    }

    .duration-info {
        line-height: normal;
        border-radius: 2px;
        font-size: 12px;
        padding: 3px 4px;
        position: absolute;
        font-family: arial, sans-serif-medium, sans-serif;
        color: #ffffff;
        left: 15px;
        bottom: 116px;
        background-color: rgba(0, 0, 0, .54);
    }

    .video-action-btn {
        line-height: normal;
        border-radius: 2px;
        font-size: 12px;
        padding: 3px 5px;
        position: absolute;
        font-family: arial, sans-serif-medium, sans-serif;
        color: #ffffff;
        right: 15px;
        bottom: 116px;
        background-color: rgba(0, 0, 0, .54);
        cursor: pointer;
    }

    .vjs-has-started {
        cursor: pointer;
    }

    .caption-link:hover p {
        color: #ff9700;
    }

    .vjs-loading-spinner,
    .vjs-error .vjs-error-display:before {
        top: 35%;
    }

    .livestream-btns {
        font-size: 12px;
        position: absolute;
        font-family: arial, sans-serif-medium, sans-serif;
        left: 15px;
        top: 10px;

        .livestream-btn {
            background-color: #e91e30;
            color: #ffffff;
            border-radius: 2px;
            padding: 3px 6px;
            text-transform: uppercase;
            font-size: 10px;
            font-weight: 600;
            margin-right: 5px;
        }

        .view-btn {
            background-color: rgba(0, 0, 0, .4);
            color: #ffffff;
            border-radius: 2px;
            padding: 3px 6px;
            font-size: 10px;
        }
    }
}

@media all and (max-width: 768px) {
    .video-slider {
        .vjs-autoplay-icon {
            top: 13%;
            left: 34%;
        }
    }
}

@media all and (max-width: 480px) {
    .video-slider {
        .vjs-autoplay-icon {
            top: 30%;
            left: 45%;
        }
    }
}

.livestreams {
    .video-js {
        .vjs-tech {
            -o-object-fit: cover;
            object-fit: cover;
        }

        .vjs-control-bar {
            display: flex;
            align-items: center;
        }

        .vjs-control-bar {
            width: 100%;
            position: absolute;
            bottom: 14px;
            padding-left: 70px;
            left: 14px;
            width: calc(100% - 30px);
            right: 0;
            border-radius: 10px;
            background-color: #2b333f;
            background-color: rgba(43, 51, 63, 0.7);
        }

        &:hover .vjs-big-play-button {
            background-color: rgba(43, 51, 63, 0.5);
        }

        .vjs-big-play-button {
            transition: 0.3s;
            opacity: 0;
            border: 0;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);

            &:hover {
                background-color: rgba(43, 51, 63, 0.7);
                border-color: transparent;
            }
        }

        &.vjs-fluid {
            border-radius: 0;
            overflow: hidden;
            min-height: 414px;
        }

        .vjs-poster {
            background-size: 150%;
        }

        .vjs-play-control:after {
            content: "LIVE";
            position: absolute;
            left: -60px;
            top: 3px;
            background-color: #f00;
            height: 24px;
            font-size: 10px;
            padding: 0 12px 0 26px;
            display: flex;
            font-weight: 700;
            letter-spacing: 0.03em;
            align-items: center;
            border-radius: 6px;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' fill='%23fff' stroke='%23fff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-circle'%3e%3ccircle cx='12' cy='12' r='10'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-size: 6px;
            background-position: 12px;
        }

        .vjs-playback-rate .vjs-playback-rate-value {
            font-size: 1.1em;
            line-height: 28px;
            opacity: 0.6;
            font-weight: 700;
        }
    }
}

@media screen and (max-width: 625px) {
    .livestreams {
        .video-js {
            .vjs-control-bar {
                padding-left: 0;
            }

            .vjs-play-control:after {
                display: none;
            }
        }
    }
}

.vjs-quality-selector .vjs-menu-button:after {
    content: "HD";
    position: absolute;
    right: 0;
    top: 5px;
    background-color: #f00;
    font-size: 9px;
    padding: 1px 2px;
    font-weight: 900;
}

.vjs-waiting .vjs-poster {
    display: none !important;
}
