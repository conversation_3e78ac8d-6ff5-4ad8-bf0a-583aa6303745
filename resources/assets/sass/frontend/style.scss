@import 'var';
@import 'popup';
@import 'utilities';
@import 'base';
@import 'headerV2';
@import 'pagination';
@import 'footer';

.main-page {
    position: relative;
    width: 100%;
    min-height: 450px;
    transition: all 0.3s;
    display: flex;
    flex-direction: row;

    .lsidebar-body {
        width: 30rem;
        height: calc(100vh - 20px);
        overflow-y: auto;
        overflow-x: hidden;
        display: flex;
        flex-direction: column;
        transition: width .3s ease-in-out;

        @media (min-width: 1200px) {
            width: 32rem;

            &.lsidebar-body-lg {
                width: 34rem!important;
            }
        }

        @media (min-width: 1600px) {
            width: 34rem;

            &.lsidebar-body-lg {
                width: 38rem!important;
            }
        }

        .lsidebar-title {
            background-color: #ffffff;
            padding: 1.25rem 1.25rem 1.25rem 1rem;
            border-top: 1px solid #e8eaed;
            border-bottom: 1px solid #e8eaed;
            font-size: 16px;
            line-height: 1.4;
            margin: 0;
            font-weight: 500;
            padding-right: 50px;

            .tree-stitle {
                color: #795548;
            }

            &.lstitle-small {
                p {
                    display: none;
                }
            }
        }

        .lsidebar-subtitle {
            color: #607D8B;
            font-size: 14px;
            font-weight: 400;
            display: inline-block;
            text-transform: capitalize;
        }

        .lsidebar-footer {
            background: #f6f9fe;
            border: 1px solid #ffffff;
            border-top: 1px solid #e8eaed;
            padding: 0.75rem;
            text-align: center;
            font-size: 14px;
            font-weight: 500;

            a {
                color: #009688;
            }
        }

        &.lsidebar-body-small {
            width: 30rem!important;
        }
    }

    .lsidebar-nav {
        overflow-y: auto;
        padding: 2rem 0.75rem;
        background: #ffffff;

        >ul>li {
            background-color: #fbfafa;
            border-radius: 0.5rem;
            border: 1px solid #dedede;
            margin-bottom: 2rem;
            padding: 1rem;
        }

        li {
            a {
                padding: 10px 0;
                font-size: 15px;
                display: block;
                position: relative;
                margin-left: 20px;
                font-weight: 400;
                line-height: 1.3;
                color: #323232;
                word-break: break-word;

                &:hover {
                    color: #ff3d00;
                }

                &.leaf-nav:before {
                    content: "";
                    width: 13px;
                    height: 13px;
                    background: #ffffff;
                    border: 2px solid #CFD8DC;
                    border-radius: 50%;
                    display: block;
                    position: absolute;
                    left: -20px;
                    top: 12px;
                }

                &.leaf-exam {
                    &:before {
                        border: none;
                        background: transparent!important;
                        content: "\f274";
                        font-family: "Font Awesome 5 Free";
                        position: absolute;
                        left: -20px;
                        top: 9px;
                        color: #009688;
                    }

                    &:after {
                        content:none!important;
                    }
                }

                &.leaf-video {
                    &:before {
                        border: none;
                        background: transparent!important;
                        content: "\f144";
                        font-family: "Font Awesome 5 Free";
                        position: absolute;
                        left: -20px;
                        top: 9px;
                        color: #9C27B0;
                    }

                    &.viewed:before {
                        content: "\f144";
                        font-weight: 900;
                        color: #E91E63;
                    }

                    &:after {
                        content:none!important;
                    }
                }

                &.leaf-file {
                    &:before {
                        border: none;
                        background: transparent!important;
                        content: "\f1c1";
                        font-family: "Font Awesome 5 Free";
                        position: absolute;
                        left: -20px;
                        top: 9px;
                        color: #ff796f;
                    }

                    &:after {
                        content:none!important;
                    }
                }

                .info-nav {
                    color: #3f51b5;
                    font-size: 14px;
                    padding: 5px 0;
                    display: block;

                    > * {
                        display: inline-block;
                    }
                }

                .meta-nav {
                    font-size: 12px;
                    font-weight: 400;
                    color: #9a9a9a;
                    // display: inline-block;
                    display: none;
                }
            }

            .active {
                background: #e8f0fe;
                border-radius: 5px;
                color: #ff3d00;

                a {
                    color: #ff3d00;

                    &.leaf-nav {
                        &:before {
                            background: #ff5722;
                            border-color: #ff5722;
                        }

                        &:after {
                            content: "\e114";
                            display: block;
                            position: absolute;
                            left: -17px;
                            top: 15px;
                            font-family: "Glyphicons Halflings";
                            font-size: 0.5em;
                            color: #ffffff;
                        }
                    }
                }
            }

            a[data-toggle="collapse"], a.collapse-nav {
                &:before {
                    content: '\e258';
                    display: block;
                    position: absolute;
                    background: transparent;
                    left: -17px;
                    top: 13px;
                    font-family: 'Glyphicons Halflings';
                    font-size: 0.6em;
                }
            }

            a[aria-expanded="false"] {
                &::before {
                    content: '\e258' !important;
                }
            }

            a[aria-expanded="true"] {
                &::before {
                    content: '\e259' !important;
                }
            }

            a.root-nav {
                display: flex;
                flex-direction: column;
                gap: 5px;

                &[data-toggle="collapse"] {
                    font-weight: 500;
                }
            }

            .leaf-nav:has(+ .leaf-meta) {
                padding-bottom: 0px;
            }

            .leaf-meta {
                display: flex;
                gap: 10px;
                justify-content: end;
                padding: 5px 10px;

                > * {
                    padding: 5px 10px;
                    font-size: 12px;
                    line-height: 1.5;
                    margin: 0;
                }
            }

            .leaf-panel {
                margin: 0;
                border: none;
                box-shadow: none;
            }
        }
    }

    .lsidebar {
        background-color: #ffffff;
        z-index: 2;
        border-right: 1px solid #dedede;

        .lsidebar-main {
            position: sticky;
            top: 50px;
            min-width: 2.5rem;
        }

        .lsidebar-wrapper {
            position: relative;
        }

        .btn-lsidebar {
            position: absolute;
            right: 3px;
            top: 0.75rem;
            transform: translateX(50%);
            border-radius: 50%;
            outline: none !important;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        &.active {
            .lsidebar-body {
                display: none;
            }
        }
    }

    .lsidebar-small {
        .lsidebar-title {
            padding-top: 0.7rem;
            padding-left: 0.7rem;
            padding-bottom: 0.7rem;
        }

        .lsidebar-nav {
            >ul>li {
                padding: 0.5rem;
            }

            .tree-stitle {
                display: none;
            }

            li {
                a {
                    font-size: 14px!important;
                    padding: 7px 0;

                    &.leaf-nav {
                        &:before {
                            top: 9px;
                        }

                        &:after {
                            top: 11px;
                        }
                    }

                    &.cursor-pointer {
                        margin-top: 7px;
                        margin-bottom: 7px;
                    }
                }

                a[data-toggle="collapse"], a.collapse-nav {
                    &:before {
                        top: 10px;
                    }
                }
            }
        }
    }

    .rsidebar-body {
        padding: 5px;
        background: #ffffff;
        border: 1px dashed #b9b9b9;
    }

    .rsidebar {
        min-width: 200px;
        width: 260px;
        max-width: 800px;

        .rsidebar-wrapper {
            position: sticky;
            top: 70px;

            @media (min-width: 1600px) {
                margin-right: -70px;
                margin-left: 70px;
            }
        }

        .rsidebar-header {
            padding: 5px 10px;
            background: #607D8B;
            color: #ffffff;
            font-size: 14px;
            font-weight: 500;
        }
    }

    .box-wrapper {
        background-color: #e8eaed;
        min-width: 0;
        width: 100%;

        .box-wrapper-body {
            display: flex;
            justify-content: center;
            gap: 2.5rem;
            margin-top: 25px;

            >*:first-child {
                flex: 1 1 auto;
                min-width: 0;
            }

            @media (max-width: 1250px) {
                flex-direction: column;
                align-items: center;
                margin-top: 0;

                >* {
                    width: 100%!important;
                }
            }
        }
    }

    .navigation-mb {
        position: sticky;
        bottom: 0;
        z-index: 2;
        background-color: #ffffff;
        border-top: 1px solid #dedede;
        padding: 10px;
        display: none;

        @media (max-width: 700px) {
            padding-bottom: 24px;
        }

        button {
            font-size: 16px;
            outline: none!important;
        }
    }

    @media (max-width: 992px) {
        display: initial;

        .lsidebar {
            display: none;

            &.active {
                background: rgba(0, 0, 0, 0.7)!important;
                display: block;
                display: block;
                position: fixed;
                top: 0;
                bottom: 0;
                right: 0;
                left: 0;
                z-index: 1000;
                width: 100%;

                .lsidebar-main {
                    top: 0!important;
                    height: 100%;
                    background: #ffffff;
                }

                .lsidebar-body {
                    display: flex;
                    width: 100%!important;
                    height: calc(100vh - 20px)!important;
                }

                .btn-lsidebar {
                    right: 30px;

                    i:before {
                        content: "\f00d";
                    }
                }
            }
        }

        .navigation-mb {
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .btree {
        padding-left: 2em;
        position: relative;

        &:before {
            border-left: 1px solid #e4e4e4;
            bottom: 0;
            content: "";
            display: block;
            left: 10px;
            position: absolute;
            top: 0;
            width: 0;
        }

        &>li {
            position: relative;

            &:before {
                border-top: 1px solid #dedede;
                bottom: 0;
                content: "";
                display: block;
                height: 0;
                position: absolute;
                left: -15px;
                top: 20px;
                width: 15px;
            }
        }
    }
}

.filter-dropdown {
    &>li {
        border-bottom: 1px solid #eaeaea;
        cursor: pointer;

        .filter-name {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;

            &[aria-expanded="true"] {
                .fa-angle-down:before {
                    content: "\f106";
                }
            }
        }

        i {
            color: #737373;
        }

        &>ul {
            &>li {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                background: #f5f7f9;
                border-top: 1px dashed #dfdfdf;

                &.active {
                    background-color: #e3fdff;
                }

                i {
                    margin-right: 8px;
                }

                a {
                    padding: 10px 15px;
                    color: #333;
                    display: block;
                    width: 100%;
                }
            }
        }
    }
}

.banner-text {
    color: #ffffff;
    // background-image: linear-gradient(to right, #ffc000, #ed7237, #ff950c);
    // background-image: linear-gradient(to right, #ed7237, #ff950c, #ffc000);
    background: #264666;
    padding: 10px;
    position: relative;

    a {
        color: #ffffff;
    }

    h2 {
        font-weight: 700;
    }

    .banner-text-close {
        position: absolute;
        top: 5px;
        right: 5px;
        font-size: 14px;
    }

    .banner-text-content {
        text-align: center;
    }

    .banner-text-title {
        font-weight: bold;
        font-size: 35px;
        line-height: 1.2;
    }

    .banner-text-des {
        font-size: 15px;
    }

    .banner-text-link {
        text-decoration: underline;
        font-weight: 500;
    }

    .banner-text-btn {
        font-weight: bold;
        padding: 6px 16px;
        background-color: #f3b100;
        border-radius: 5px;
        display: inline-block;
        text-transform: uppercase;

        &:hover {
            background: #ffffff;
            color: #f8bb18;
        }
    }
}

.form-sale {
    margin-top: 20px;

    .form-group {
        position: relative;

        .txt-form {
            width: 100%;
            border: 1px solid #dcdcdc;
            padding: 0 110px 0 10px;
            @include border-radius(1px);
            height: 38px;
            line-height: 38px;
        }

        &.btn-small {
            height: 32px;
            line-height: 30px;
            font-size: 16px;
            padding: 0 30px;
        }

        .btn {
            border: 1px solid #FF5722;
            border-radius: 1px;
            overflow: hidden;
            font-size: 14px;
            font-weight: 700;
            height: 39px;
            line-height: 39px;
            color: #FF5722!important;
            background-color: #FBE9E7;
            padding: 0;
            width: 110px;
            position: absolute;
            top: -1px;
            right: 0;
        }
    }
}

.form-check {
    label {
        color: #333;
        margin: 0;
        padding-left: 30px;
        cursor: pointer;

        input {
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;

            &:checked {
                +.icon {
                    color: #F44336;

                    i {
                        font-weight: 900;

                        &:before {
                            content: "\f14a";
                        }
                    }
                }
            }

            &:disabled {
                +.icon {
                    color: #F44336;
                    cursor: not-allowed;
                }
            }
        }
    }
}

.go_top {
    // position: fixed;
    // background: #e7700d;
    // display: none;
    // color: #ffffff !important;
    // width: 44px;
    // height: 44px;
    // text-align: center;
    // line-height: 1;
    // font-size: 16px;
    // border-radius: 50%;
    // right: 20px;
    // bottom: 20px;
    // transition: background .5s;
    // z-index: 999;
    // opacity: 1;

    position: fixed;
    background: #e7700d2e;
    display: none;
    color: #FF5722 !important;
    padding: 5px;
    border-radius: 3px;
    text-align: center;
    font-size: 16px;
    right: 20px;
    bottom: 70px;
    transition: background 0.5s;
    z-index: 999;
    opacity: 1;

    i {
        display: block;
        // line-height: 42px;
    }
}

.less {
    color: $color-666;
    font-weight: 500;
    margin-top: 15px;
    font-size: 13px;
    margin-bottom: 40px;
    display: inline-block;

    &:before {
        content: 'Xem thêm';
        /*Xem them*/
    }

    i {
        padding-left: 10px;
    }

    &.more {
        width: 100%;
        background: linear-gradient(hsla(0, 0%, 100%, 0), hsla(0, 0%, 100%, .95), #ffffff);
        bottom: 0;
        display: block;
        padding-top: 30px;
        position: absolute;
        width: 100%;

        &:before {
            content: 'Thu gọn';
            /*Thu gon*/
        }

        i {
            &:before {
                content: '\f077';
            }
        }
    }
}

.icon-title {
    position: relative;
}

.shopping-count {
    position: absolute;
    right: -7px;
    top: -2px;
    color: #ffffff;
    background: #f34848;
    border-radius: 100%;
    width: 15px;
    height: 15px;
    line-height: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 10px;
}

.flash-backdrop {
    background: rgba(0, 0, 0, .5);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    display: flex;
    justify-content: center;
    align-items: center;

    .flash-content {
        background-image: url(/images/icons/bg-signin.png);
        background-size: cover;
        background-color: #ff922e;
        max-width: 550px;
        color: #ffffff;
        padding: 30px;
        position: relative;
        font-size: 18px;

        &.error {
            background-color: #FF5722;
        }

        &.warning {
            background-color: #428baf;
        }

        &.info {
            background-color: #2894c4;
        }

        .flash-content-logo {
            margin: 15px 0;
            font-weight: 700;
            font-size: 20px;
            text-align: center;
            display: block;
        }

        .flash-close {
            position: absolute;
            right: 10px;
            top: 10px;
            font-size: 20px;
            cursor: pointer;
        }
    }
}

.radio {
    position: relative;
    font-size: 16px;
    color: #3f3f3f;
    margin: 0 0 30px 0;

    label {
        display: block;
        margin-left: 0;
        padding-left: 0;
        font-size: 18px;

        input[type=radio] {
            display: none;

            &:checked+.cr>.cr-icon {
                opacity: 1;
            }

            +.cr {
                position: relative;
                display: inline-block;
                border: 1px solid #434343;
                @include border-radius(50%);
                width: 15px;
                height: 15px;
                float: left;
                margin-right: 7px;
                color: #ff9700;
                margin-top: 5px;

                >.cr-icon {
                    opacity: 0;
                    position: absolute;
                    font-size: .6em;
                    line-height: 0;
                    top: 50%;
                    left: 13%;
                    margin-left: 0.04em;
                }
            }
        }
    }

    p {
        margin: 0 !important;
    }
}

.open-in-app {
    font-weight: 600;
    text-transform: none;
    margin: 0 10px;
}

// .main-qa, .main-body, .post-main {
//     td, th {
//         padding: 5px 10px !important;
//     }
// }

.load-more-btn {
    font-size: 15px !important;
    text-align: center;
    justify-content: center;
    display: block;
    color: #9C27B0 !important;
    cursor: pointer;
    background-color: #fffaf0;
    border: 1px solid #EEEEEE;
    padding: 3px!important;
    margin: 3px 0 0 !important;

    &:hover {
        color: #009688 !important;
    }
}

div.mce-fullscreen {
    z-index: 9999;
}

.mce-top-part {
    position: sticky !important;
    top: 0;
}

.mce-fullscreen .mce-top-part {
    top: 0 !important;
}

.read-more-footer {
    text-align: center;
    padding: 10px;
    margin-top: 10px;
    border-top: 1px solid #dbdbdb;
    // font-weight: 500;

    a {
        display: block;
        cursor: pointer;
        color: #f0782e;

        &:hover {
            color: #900;
        }
    }
}

.footer-list {
    height: 30px;
    z-index: 997;
    margin-top: -30px;
    width: 100%;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#ffffff29), color-stop(30%, #ffffffd6));
    background-image: linear-gradient(#ffffff29, #ffffffd6 30%);
    position: relative;
}

.buy-vip {
    background: #f9e8ce;
    border-radius: 3px;
    padding: 10px 15px;
}

.vip-title {
    // text-align: justify;
    font-size: 18px;
    color: #000 !important;
    // margin: 0;

    @media (max-width: 500px) {
        font-size: 16px;
    }

    i {
        margin: 0 7px;
        font-size: 15px;
        color: #FF5722;
    }

    span {
        a {
            font-weight: 500;
            color: #2d49ed;

            i {
                color: #2d49ed;
            }

            &:hover {
                color: #169bd5;

                i {
                    color: #169bd5;
                }
            }
        }
    }
}

#package-vip {
    background: #ffffff;

    .panel {
        margin: 5px 0;
        border-bottom: 1px dashed #c1c0c0;
        box-shadow: none;
    }

    .panel-heading {
        border-bottom: none;

        &.active {
            background: #a2f6ec5e;
        }
    }

    .panel-body {
        padding: 3px 5px 20px
    }

    .panel-title {
        font-weight: bold;
        text-transform: uppercase;
        position: relative;
        white-space: normal;
        padding: 5px 20px 5px 0;

        .pac-item {
            display: flex;
            align-items: center;
            gap: 20px;
            line-height: 28px;
        }

        .pac-title {
            color: #272675;
            font-size: 24px;
            position: relative;

            &:before {
                content: " ";
                width: 45px;
                height: 45px;
                position: absolute;
                border-radius: 50%;
                z-index: 1;
                top: -9px;
                left: -15px;
                background: #ffc1073d;

            }

            &.pac-vip2 {
                &:before {
                    background: #11ca0547;
                }
            }

            &.pac-vip3 {
                &:before {
                    background: #c4fff6;
                }
            }

            &.pac-vip4 {
                &:before {
                    background: #f4433624;
                }
            }

            span {
                position: relative;
                z-index: 2;
            }
        }

        .pac-duration {
            color: #344d59;
            text-transform: capitalize;
            font-size: 16px;
        }

        .pac-price {
            color: #ff5722;
            font-size: 20px;
            white-space: nowrap;
        }

        .icon-sales-price {
            position: absolute;
            top: -20px;
            right: -25px;
            width: 40px;
        }

        p {
            margin: 0;
        }

        .pac-more {
            font-size: 14px;
            text-transform: none;
            font-weight: 400;
            font-style: italic;
            color: #607D8B;
            text-align: right;
        }
    }

    .pac-box {
        border: 2px dashed #2196F3;
        padding: 10px;
        word-break: break-word;

        .pac-title {
            font-size: 18px;
            font-weight: bold;
            color: #9C27B0;
            text-transform: uppercase;
        }

        .btn-order {
            padding: 3px 20px;
            background: #f65c94;
            color: #ffffff;
            font-weight: bold;
            text-transform: uppercase;
            border-radius: 50px;
            font-size: 18px;
            text-align: center;
            border: none;
        }
    }
}

.gbox-wrapper {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;

    .gbox {
        margin-bottom: 2rem;
        display: flex;
        align-items: stretch;

        .card {
            word-wrap: break-word;
            background-clip: border-box;
            background-color: #ffffff;
            border: 1px solid rgba(0, 0, 0, .125);
            border-radius: .25rem;
            display: flex;
            flex-direction: column;
            min-width: 0;
            position: relative;
            width: 100%;
            margin-bottom: 1rem;
            padding: .5rem;

            @media only screen and (max-width: 500px) {
                margin-bottom: 0;
            }

            .card-header {
                background-color: #ffffff;
                margin-bottom: 0;
                padding: .25rem;
                display: flex;
                align-items: center;

                a {
                    display: flex;
                    align-items: center;
                }
            }

            .card-body {
                flex: 1 1 auto;
                min-height: 1px;
                padding: .25rem .5rem;
            }

            .card-footer {
                border-top: 1px solid rgba(0, 0, 0, .125);
                padding: .75rem 0.5rem;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .card-footer-meta {
                display: flex;
                justify-content: space-between;
                width: 100%;
            }

            .gbox-icon {
                background-color: grey;
                border-radius: 0 19px 19px 0;
                box-sizing: border-box;
                font-weight: 700;
                left: -15px;
                margin-right: 2px;
                padding: 7px 15px;
                position: relative;
                vertical-align: middle;
                color: #ffffff;
                display: inline-block;
                text-align: center;
            }

            .gbox-title {
                margin: .5rem 0;
                font-size: 22px;
                line-height: 1.545454;
                font-weight: 600;
                letter-spacing: -.03rem;
            }

            .gbox-des {
                font-size: 16px;
                color: #545454;
            }

            .gbox-star {
                display: block;
                color: #FF9800;
            }

            .gbox-meta {
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                flex-wrap: nowrap;
                color: #6c757d;
            }
        }
    }
}

.main-subject {
    padding: 5px 10px;

    .subject-item {
        padding: 5px 10px;
        text-align: center;
        font-size: 15px;
        font-weight: 700;
        cursor: pointer;
        border-radius: 3px;
        min-width: 150px;
        height: 100%;
        background: #ffffff;
        border: 1px solid rgba(255, 151, 0, 0.4);
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: rgb(96, 169, 214);

        &:hover {
            background: -webkit-linear-gradient(#fff, #52b7002e);
            border: 1px solid #52b700;
            color: #ee5e65;
        }

        .subject-item__image {
            height: 32px;
            width: auto;
        }

        .subject-item__name {
            align-self: flex-start;
            width: 100%;
            overflow: hidden;
            padding: 7px 0;
        }

        .subject-item__meta {
            font-size: 13px;
            font-weight: 400 !important;
        }

        &.active {
            background: #f3b100;
            color: #ffffff;
        }
    }

    .active {
        border-radius: 3px;

        .subject-item {
            background: -webkit-linear-gradient(#fff, #ffd871);
            border: 2px solid #f3b100;
            font-weight: 900;
            color: #265a92;
            border: 1px solid #265a92;
        }
    }
}

.s-box {
    padding: 10px 20px;

    .ex-item {
        width: 100%;
        max-width: 100%;
        min-width: 100%;
    }

    @media (max-width: 500px) {
        padding: 0;
    }
}

.s-nav {
    display: flex;
    border-bottom: 1px solid #ddd;

    > li {
        padding: 0 15px;

        > a {
            color: #586380;
            border-radius: 0;
            background-color: transparent !important;
            font-weight: 500;
            font-size: 16px;
            padding: 5px 0;

            &:hover, &:focus {
                color: #ff5722;
                border-bottom: 2px solid #ff5722;
            }
        }

        @media (max-width: 500px) {
            padding: 0 10px;

            > a {
                font-size: 14px;
            }
        }
    }

    > li.active > a {
        color: #F44336 !important;
        border-bottom: 2px solid #F44336;
    }
}

.exam-hot-list {
    padding: 10px;
    counter-reset: li;
    list-style: none;

    .exam-hot-item {
        position: relative;
        padding-left: 35px;
        cursor: pointer;

        &::before {
            content: counter(li);
            counter-increment: li;
            position: absolute;
            top: 50%;
            left: 0;
            transform: translateY(-50%);
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #e6fcf4;
            border: 1px solid #999;
            text-align: center;
            line-height: 30px;
            font-size: 10px;
            font-weight: bold;
            color: #5c5b5b;
        }

        &:hover::before {
            background-color: #008c6e;
            color: white;
            border-color: #008c6e;
        }

        &:hover .l-item {
            background-color: #f0faff; 
        }

    }
}

.nav-scroll-x {
    overflow-x: auto;

    .nav {
        display: flex;
        white-space: nowrap;
    }

    &::-webkit-scrollbar {
        display: none;
    }
}

// modules
@import './modules/_course';
@import './modules/_question';
@import './modules/_home';
@import './modules/_vj-slide';
@import './modules/_app-install';
@import './modules/_btn-msg';
@import './modules/_book-picker';
@import './modules/_comments-group';
@import './modules/_payment';
@import './modules/_exam-list';
@import './modules/_post';
@import './modules/_user';
@import './modules/table-custom';
