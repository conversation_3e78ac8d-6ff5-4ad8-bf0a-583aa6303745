/* =============================================
            Header Style                  
============================================= */
.tg-header {
    position: relative;
    z-index: 998;
    background: #ffffff;
    min-height: 50px;
}
.tg-topbar {
    width: 100%;
    border-bottom: 1px solid #dbdbdb;
}
.tg-themedropdown.tg-currencydropdown {
    margin: 10px 0;
    padding: 0 0 0 15px;
    border-left: 1px solid #dbdbdb;
}
.tg-currencydropdown .tg-themedropdownmenu {
    border: 0;
    padding: 0;
    width: 300px;
    margin: 11px 0 0;
    list-style: none;
    font-size: 13px;
    line-height: 40px;
}
.tg-currencydropdown .tg-themedropdownmenu li {
    width: 100%;
    float: left;
    line-height: inherit;
    list-style-type: none;
}
// .tg-currencydropdown .tg-themedropdownmenu li + li {border-top: 1px solid #dbdbdb;}
.tg-currencydropdown .tg-themedropdownmenu li a {
    color: #666;
    width: 100%;
    float: left;
    padding: 10px 20px;
    white-space: normal;
    display: flex;
    align-items: baseline;
    gap: 10px;
    font-size: 15px;
}
.tg-currencydropdown .tg-themedropdownmenu li a:hover {background: #fafafa;}
.tg-currencydropdown .tg-themedropdownmenu li a i,
.tg-currencydropdown .tg-themedropdownmenu li a span{
    float: left;
    font-style: normal;
    line-height: inherit;
}
.tg-currencydropdown .tg-themedropdownmenu li a span {
    margin-left: 10px;
    padding-left: 10px;
    position: relative;
}
.tg-currencydropdown .tg-themedropdownmenu li a span:before {
    top: 0;
    left: -2px;
    content: '-';
    position: absolute;
    font-size: inherit;
    line-height: inherit;
}
.tg-userlogin a img {
    display: block;
    border-radius: 50%;
    background: #ffffff;
    height: 40px;
    width: auto;
    border: 1px solid #dddddd;
}
.tg-userlogin .img-byname {
    border-radius: 50%;
    background: #ffffff;
    border: 1px solid #dddddd;
}
.tg-middlecontainer {
    width: 100%;
    padding: 10px 0;
    position: relative;
}
.tg-logo {
    z-index:2;
    float: left;
    position:relative;
    height: 50px;
    display: flex;
    align-items: center;
}
.tg-logo a { display: block; }
.tg-logo a img { display: block; }
.tg-actionlist {
    z-index: 2;
    float: right;
    position: relative;
    display: flex;
    align-items: center;
    height: 50px;
}
.tg-wishlistandcart {
    z-index: 2;
    float: right;
    position: relative;
}
.tg-themedropdown {
    float: left;
    padding: 0 15px;
}
.tg-themedropdown + .tg-themedropdown { border-left: 1px solid #dbdbdb; }
.tg-btnthemedropdown {
    color: #404040;
    display: block;
    font-size: 15px;
    font-weight: 600;
    line-height: 20px;
    position: relative;
}
.tg-btnthemedropdown:focus,
.tg-btnthemedropdown:hover { color: #404040; }
.tg-btnthemedropdown i,
.tg-btnthemedropdown span {
    float: left;
    color: #666;
}
.tg-btnthemedropdown i {
    font-size: 16px;
    line-height: 20px;
    padding: 0 12px 0 0;
}
.tg-themebadge {
    top: -10px;
    left: 10px;
    min-width: 18px;
    font-size: 10px;
    line-height: 18px;
    border-radius: 50%;
    position: absolute;
    text-align: center;
    background: red;
    vertical-align: top;
    display: inline-block;
    color: #ffffff !important;
}
.tg-themedropdownmenu .tg-description p { margin: 0; }
.tg-themedropdownmenu {
    top: 100px;
    right: 0;
    left: auto;
    border: 0;
    opacity: 0;
    z-index: 10;
    width: 330px;
    padding: 15px;
    display: block;
    border-radius: 0;
    margin: 25px 0 0;
    visibility: hidden;
    border-top: 3px solid;
}
.tg-wishlistdropdown { padding-right: 0 }
.tg-minicartbody {
    width: 100%;
    float: left;
    padding: 0 33px 0 0;
    border-bottom: 1px solid #dbdbdb;
}
.tg-minicarproduct {
    width: 100%;
    float: left;
    padding: 0 0 20px;
}
.tg-minicarproduct figure {
    float: left;
    margin: 0 20px 0 0;
}
.tg-minicarproduct figure img {
    z-index: 1;
    display: block;
    position: relative;
}
.tg-minicarproductdata {
    padding: 12px 0;
    overflow: hidden;
}
.tg-minicarproductdata h5 {
    margin: 0 0 9px;
    color: #404040;
    font-size: 14px;
    line-height: 17px;
}
.tg-minicarproductdata h5 a {
    width: 100%;
    display: block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.tg-minicarproductdata h6 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    line-height: 20px;
}
.tg-minicartfoot {
    width: 100%;
    float: left;
    padding: 20px 0 0;
}
.tg-btnemptycart {
    float: left;
    color: #666;
    font-size: 13px;
    line-height: 16px;
}
.tg-btnemptycart i,
.tg-btnemptycart span {
    float: left;
    font-size: inherit;
    line-height: inherit;
}
.tg-btnemptycart span {padding: 0 0 0 10px;}
.tg-subtotal {
    float: right;
    font-size: 13px;
    line-height: 16px;
}
.tg-subtotal strong { font-size: 16px; }
.tg-minicartfoot .tg-btns {padding: 20px 0 10px;}
.tg-minicartfoot .tg-btns .tg-btn {
    margin: 0;
    padding: 0;
    width: 45%;
    float: left;
}
.tg-minicartfoot .tg-btns .tg-btn + .tg-btn { float: right; }
.tg-themedropdown.open .tg-themedropdownmenu {
    top: 100%;
    opacity: 1;
    visibility: visible;
    z-index: 1000;
}
.tg-searchbox{
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    padding: 0 350px 0 300px;
    position: absolute;
    display: flex;
    align-items: center;
    @media (max-width: 1200px) {
        padding: 0 350px 0 200px;
    }
}
.tg-formsearch, .tg-formsearch fieldset {
    position: relative;
}
.tg-formsearch fieldset .form-control{
    border: 0;
    z-index: 2;
    font-size: 16px;
    line-height: 24px;
    position: relative;
    padding: 10px 54px 10px 20px;
    height:50px;
    border: 1px solid #ccc;
    border-radius: 25px;
    box-shadow:none;
    outline: none;
}
.tg-formsearch fieldset button{
    z-index: 2;
    height: 40px;
    width: 40px;
    font-size: 20px;
    line-height: 40px;
    position: absolute;
    border: none;
    box-shadow: none;
    outline: none;
    background: #00a96c;
    border-radius: 50%;
    color: #ffffff;
    top: 50%;
    right: 5px;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}
.tg-navigationarea {
    width: 100%;
    position: relative;
    background: #ff922e;
}
.tg-navigationarea:before {
    left: 0;
    bottom: 0;
    width: 100%;
    height: 2px;
    content: '';
    position: absolute;
    background: rgba(0, 0, 0, 0.20);
}
.tg-nav {
    width: 100%;
    float: left;
    padding: 0;
    line-height: 50px;
}
#tg-navigation {
    position: relative;
}
.tg-navigation {
    padding: 0;
    width: 100%;
    float: left;
    text-align: center;
}
.tg-close-navbar {
    display: none;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
    padding: 0 10px;
    cursor: pointer;
    color: #919191;
}
.tg-navigation ul {
    list-style: none;
    font-size: inherit;
    line-height: inherit;
}
.tg-navigation > ul {
    width:100%;
    float:left;
    position: relative;
    display: inline-block;
    vertical-align: top;
}
.tg-navigation > ul > li{
    float: left;
    font-size: inherit;
    line-height: inherit;
}
.tg-navigation ul li {list-style-type: none;}
.tg-navigation ul li a, .tg-navigation ul li .a-item {
    display: block;
    position: relative;
    background: transparent;
    cursor: pointer;
}
.tg-navigation .menu-item-root {
    color: #ffffff;
    padding: 0 7px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;

    i {
        display: block;
        font-size: 20px;
        line-height: inherit;
    }
}
.tg-navigation .menu-item-has-children:hover,
.tg-navigation .menu-item-root:hover,
.tg-navigation .current-menu-item .menu-item-root { background: rgba(0, 0, 0, 0.15); }
li.menu-item-has-children .menu-item-root {
    padding-right: 25px;
    position: relative;
}
li.menu-item-has-children.menu-item-has-mega-menu .menu-item-root {
    padding-right: 12px;
    padding-left: 12px;
}
li.menu-item-has-children { position: relative; }
li.menu-item-has-children:has(> .mega-menu) > .a-item:before,
li.menu-item-has-children:has(> .sub-menu) > .a-item:before {
    top: 0;
    right: 10px;
    content: '\f107';
    position: absolute;
    font-weight: 900;
    font-size: 12px;
    line-height: inherit;
    font-family: Font Awesome\ 5 Free;
}

.sub-menu,
.mega-menu {
    top: 100px;
    left: 0;
    margin: 0;
    opacity: 0;
    width: 230px;
    text-align: left;
    background: #ffffff;
    visibility: hidden;
    position: absolute;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.20);
    -webkit-box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.20);
    font-size: 16px;
    line-height: 1.4;
    font-weight: 400;
}
.sub-menu li {
    width: 100%;
    float: left;
    line-height: inherit;
}
.sub-menu li + li { border-top: 1px solid #dbdbdb; }
.sub-menu li.menu-item-has-children:has(> .sub-menu) > .a-item:before { content: '\f105'; }
.sub-menu li a,
.sub-menu li .a-item {
    color: #666;
    padding: 0 20px;

    &:hover { background: #e1f5fe; }
}
.sub-menu .sub-menu { left: 100%; }
.tg-navigation > ul > li:last-child .sub-menu {
    left: auto;
    right: 0;
}
.tg-navigation > ul > li:last-child .sub-menu .sub-menu { right: 100%; }
li.menu-item-has-children:hover > .sub-menu,
li.menu-item-has-children:hover > .mega-menu{
    top: 98%;
    border-top: 1px solid #ff922e;
    opacity: 1;
    visibility: visible;
    z-index: 999;
}
li.menu-item-has-children:hover > .sub-menu{
    top: 100%;
}
li.menu-item-has-children:hover > .sub-menu > li.menu-item-has-children:hover > .sub-menu { top: 0; }
li.menu-item-has-mega-menu { position: static; }
.mega-menu {
    opacity: 0;
    width: 100%;
    visibility: hidden;
}
.mega-menu .tg-themetabnav {
    margin: 0;
    width: 230px;
    background: #ffffff;
    position: relative;
    float: left;
    max-height: 500px;
    overflow-y: auto;
    overflow-x: hidden;
    border-right: 1px solid #e9e9e9;
}
.mega-menu .tg-themetabnav li {
    width: 100%;

    &.active, &:hover a {
        background: #e3f2fd;
    }
}
.mega-menu .tg-themetabnav li a {
    position: relative;
    color: #666;
    padding: 10px 20px;
    white-space: break-spaces;
}
// .mega-menu .tg-themetabnav li a:before {
//     top: 0;
//     right: 20px;
//     content: '\f105';
//     position: absolute;
//     font-size: inherit;
//     line-height: inherit;
//     font-family: Font Awesome\ 5 Free;
// }
.mega-menu .tg-tabcontent {
    width: auto;
    float: none;
    padding: 10px 15px;
    max-height: 500px;
    overflow-x: hidden;
    overflow-y: auto;
    border-left: 1px solid #eee;
}
.mega-menu ul.mega-text {
    margin: 0;
    width: 100%;
    float: left;
    display: table;
    table-layout: fixed;
}
.mega-menu ul.mega-text > li {
    padding: 0 15px;
    float: left;
    width: 33%;
}
.tg-linkstitle {
    width: 100%;
}
.tg-linkstitle p {
    font-size: 16px;
    font-weight: 600;
    color: #900;
    padding: 0 15px;
}
.mega-menu a {
    color: #666;
    position: relative;

    &:hover { color: #009688; }
}
.tg-btnviewall{
    color: #2196F3;
    font-size: 14px;
    margin-bottom: 15px;
    margin-top: 10px;
}
.mega-container {
    overflow: hidden;

    .mega-nav {
        display: flex;
        align-items: center;
        padding: 15px 15px 5px;

        .mega-nav_title {
            flex-shrink: 0;
            color: #900;
            font-size: 16px;
            font-weight: 600;
            margin: 0;
        }

        .nav {
            width: 100%;
            display: flex;
            flex-wrap: nowrap;
            overflow-x: auto;
            overflow-y: hidden;
            position: relative;
            margin-left: 10px;

            @media (max-width: 600px) {
                &::-webkit-scrollbar {
                    display: none;
                }
            }

            &>li>a {
                white-space: nowrap;
                padding: 5px 10px;
            }
        }
    }
}
/* =============================================
            Header V Two Style            
============================================= */
.tg-headervtwo .tg-navigation > ul{position: static;}
.tg-navigationholder{
    width: 100%;
    float: left;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
}
.tg-headervtwo .tg-navigationarea:before{display:none;}
// .tg-headervtwo .tg-nav{width:auto;}
.tg-headervtwo .tg-navigation{text-align:left;}
.tg-headervtwo .tg-navigation ul li:first-child a:after,
.tg-headervtwo .tg-navigation > ul > li:nth-child(2) a:after{display:none;}
.tg-headervtwo .sub-menu li a:after{display:none;}
.tg-rnav .tg-themedropdown{
    border:0;
    padding:10px;
}
.tg-rnav .tg-btnthemedropdown i{
    padding:0;
    color:#ffffff;
}
.tg-rnav .tg-userlogin {margin:0 0 0 2px;}

.tg-rnav .tg-currencydropdown .tg-themedropdownmenu{margin:1px 0 0;}
.tg-rnav .tg-userlogin {
    padding: 5px;
}
.tg-rnav .tg-themedropdownmenu {
    margin: 0;
}
.tg-dropdowarrow {
    top: 10px;
    z-index: 5;
    color: #333;
    right: 10px;
    width: 20px;
    height: 20px;
    display: none;
    cursor: pointer;
    font-size: 8px;
    line-height: 20px;
    position: absolute;
    text-align: center;
    background: #ffffff;
}
.tg-dropdowarrow i{
    display: block;
    font-size: 8px;
    line-height: inherit;
    background: #ECEFF1;
}
.tg-open > .tg-dropdowarrow i:before{content: '\f107';}
.m-logo {
    display: block;
    float: left;
    height: 50px;
    padding: 5px;

    img {
        width: auto;
        height: 100%;
        background: #ffffff;
        padding: 5px;
        border-radius: 7px;
        margin-right: 10px;
    }

    @media (min-width: 876px) {
        display: none;
    }
}

.nav-custom-mb {
    display: flex;
    gap: 5px;
    align-items: center;
    flex-shrink: 0;
}

.nav-custom-mb .tg-minicartdropdown {
    display: none;
}

#scroll-menu {
    overflow: auto;
    display: flex;
    white-space: nowrap;
    max-width: 850px;
    scrollbar-width: none;
    scrollbar-width: none;
    margin: auto;

    @media (min-width: 1366px) {
        max-width: 1100px;
    }

    &::-webkit-scrollbar {
        display: none
    }

    .lscroll-menu_btn,.rscroll-menu_btn {
        color: #fff;
        position: absolute;
        height: 100%;
        top: 50%;
        transform: translateY(-50%);
        z-index: 2;
        display: none;
        align-items: center;
        cursor: pointer
    }

    .lscroll-menu_btn {
        left: 0;
        background: linear-gradient(to right,#d98508 70%,transparent 100%);
        padding: 5px 20px 5px 10px
    }

    .rscroll-menu_btn {
        right: 0;
        background: linear-gradient(to left,#d98508 70%,transparent 100%);
        padding: 5px 10px 5px 20px
    }

    > ul {
        display: flex;
        flex-grow: 1;
        white-space: nowrap;
        align-items: center;
        margin: 0;
    }
}

@media (max-width: 1200px) {
    .tg-navigationarea .container {
        max-width: 100% !important;
        padding: 0;
    }

    .menu-item-root .fa-list-ul {
        display: none;
    }

    .tg-themedropdown .fa-lock {
        display: none;
    }
}

@media (min-width: 1020px) {
    .menu-item-has-mega-menu > .a-item:before,
    .menu-item-has-mega-menu > .a-item:before {
       display: none;
    }
}

@media (max-width: 1020px) {
    #scroll-menu {
        width: 100%;
        max-width: 100%!important;
        overflow: hidden;

        > ul {
            display: initial;
        }

        .lscroll-menu_btn, .rscroll-menu_btn {
            display:none!important;
        }
    }

    .mega-container {
        .mega-nav {
            .mega-nav_title {
                display: none;
            }
        }
    }

    .nav-custom-mb .tg-minicartdropdown {
        display: inline;
    }

    .tg-middlecontainer {
        padding: 5px 0;
    }
    .tg-searchbox {
        width: 100%;
        float: left;
        padding: 10px 0 5px;
        position: relative;
    }

    /*=====================Nav Toggle On mobile, if change max-width px then need change of main.js=====================*/
    .navbar-toggle {
        float:left;
        margin-left: 5px;
        display: block;
        border-radius: 5px;
        border:2px solid #ffffff;
    }
    .navbar-toggle .icon-bar{background: #ffffff;}
    .navbar-collapse.collapse {
        display: none!important;
    }
    .collapse.in{display:block !important;}
    .tg-nav{line-height:40px;}
    .tg-nav .navbar-collapse{box-shadow: 0px 3px 10px 0 rgb(0 0 0 / 22%); height: auto!important;}
    // .tg-navigation {
    //     left: 0;
    //     top: 100%;
    //     padding: 0 10px;
    //     background: #ffffff;
    //     position: absolute;
    // }
    .tg-navigation > ul > li{
        width:100%;
        text-align:left;
        border-bottom:1px solid #dbdbdb;
        position: relative!important;
    }
    .tg-navigation .menu-item-root{
        color:#666;
        font-weight: 400;
        font-size: 15px;
        padding:0;

        i {
            font-size: 15px;
        }
    }
    .tg-navigation .menu-item-root:hover,
    .tg-navigation .current-menu-item .menu-item-root { background: none; }

    .tg-navigation > ul > li.menu-item-has-children > a{padding:0 15px;}

    li.menu-item-has-children > a:before{display:none;}
    li.menu-item-has-children:hover > .sub-menu,
    li.menu-item-has-children:hover > .mega-menu{
        border-top: none;
    }
    .tg-dropdowarrow{display:block;}
    .sub-menu {
        opacity: 1;
        width: 100%;
        float: left;
        display: none;
        position: static;
        box-shadow: none;
        visibility: visible;
    }
    .mega-menu {
        float: left;
        opacity: 1;
        display: none;
        position: static;
        box-shadow: none;
        visibility: visible;
        width: 100% !important;

        p {
            font-size: 15px;
        }
    }
    .mega-menu .tg-themetabnav{
        width:30%;
        float:left;

        > li > a {
            padding-left:5px;
            padding-right:5px;
        }
    }
    .mega-menu .tg-themetabcontent{
        width:70%;
        float:left;
        box-shadow:none;
        padding: 0;
    }
    .mega-menu ul.mega-text > li {
        width: 100%;
        padding: 0;
    }
    .tg-linkstitle p {
        padding: 0;
    }
    .tg-headervtwo .tg-navigation li.menu-item-has-mega-menu{background:none;}
    .tg-navigation .tg-close-navbar { display: block;}
    /*=====================Nav Toggle mobile=====================*/
}

@media (max-width:480px){
    .tg-header .container {
        padding: 0;
    }
    .tg-logo{
        display:block;
        margin:0 auto;
    }
    .tg-logo a img {
        margin: 0 auto;
    }
    .tg-actionlist {
        // width: 100%;
        text-align: center;
        justify-content: center;
    }

    .tg-wishlistdropdown, .tg-minicartdropdown {
        position: static;
        display: inline-block;
        vertical-align: middle;
    }

    .tg-themedropdownmenu {
        margin: 0;
        width: 100%;
        position: absolute;
        z-index: 1000;
    }

    .tg-wishlistandcart {
        width: auto;
        position: static;
        text-align: center;
    }
}

@media (max-width:430px){
    .tg-logo{
        float:none;
    }

    .tg-actionlist {
        width: 100%;
    }
}

@media (max-width:320px){
    .tg-logo{
        float:none;
    }

    .tg-actionlist {
        width: 100%;
    }

    .tg-currencydropdown .tg-themedropdownmenu {
        width: 250px;
    }
}

@media (max-width:275px){
    .tg-currencydropdown .tg-themedropdownmenu {
        width: 100%;
    }
}

#tg-navigation.tg-menu-mobile {
    background: #ffffff;
    height: 100vh !important;
    left: 0;
    overflow-y: auto!important;
    overflow-x: hidden;
    position: fixed;
    top: 0;
    transform: translate3d(-100%, 0, 0);
    transition: transform .6s cubic-bezier(.785,.135,.15,.86);
    max-width: 350px;
    width: 100%;
    z-index: 1012;
    padding: 0 10px 50px;

    &.collapse.in {
        transform: translateZ(0);
    }

    .menu-item-has-children:not(.menu-item-has-mega-menu) li {
       padding-left: 1rem;
    }

    .menu-item-has-children.tg-open {
        .menu-item-root {
            font-weight: 900;
            color: #900;
        }
    }
}

.tg-currencydropdown .tg-themedropdownmenu {
    .info-user {
        align-items: center;
        padding: 10px 0;

        .img {
            height: 50px;
            width: 50px;
            flex-shrink: 0;

            img {
                height: 100%;
                width: 100%;
            }
        }

        .overflow {
            word-break: break-word;

            .name {
                color: rgb(51, 51, 51);
                font-size: 15px;
                font-weight: 700;
                margin: 0px;
            }

            .gmail {
                font-size: 14px;
                color: rgb(153, 153, 153);
                margin: 0px;
            }
        }
    }
}

.tg-boxlist {
    max-height: 320px;
    overflow-y: auto;
    padding-right: 10px;

    &::-webkit-scrollbar {
        width: 3px;
    }

    &::-webkit-scrollbar-thumb {
        border-radius: 1px;
        background: #ff940e;
    }

    .list {
        a {
            display: inline-block;
            color: $color-666;
        }

        .img {
            width: 50px;
            height: 50px;
            overflow: hidden;
            border-radius: 50%;

            img {
                width: 100%;
            }
        }

        .overflow {
            padding-left: 15px;

            .txt {
                margin: 0;
                font-weight: normal;
                font-size: 16px;
                line-height: 1.6;
                color: $color-666;

                &:hover {
                    color: $color-yellow !important;

                    a {
                        color: $color-yellow !important;
                    }
                }
            }

            .day {
                font-size: 12px;
                color: $color-999;
            }
        }
    }

    &.box-info {
        .list {
            margin-bottom: 20px;

            .img {
                border: none;
                border-radius: 2px;
                height: auto;
                max-height: 60px;
                width: 77px;
            }

            .price {
                font-weight: 500;
                margin-top: 10px;
            }
        }
    }
}

.list-cart {
    .bottom {
        border-top: 1px solid #d7d7d7;

        .box-total-price {
            margin: 10px 0;
        }

        .btn {
            width: 100%;
        }
    }
}

.sticky-header {
    .tg-navigationarea {
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, .2);
        left: 0;
        position: fixed;
        top: 0;
        z-index: 999;
    }
}

.slistbox {
    list-style-type: none;

    li {
        margin: 0;
        padding: 0;
        background: #0000;

        a {
            color: #282e3e;
            display: flex;
            align-items: baseline;
            padding: 5px 10px;
            font-size: 16px;
            gap: 10px;
            width: 100%;

            i {
                color: #607D8B;
                padding: 5px;
            }
        }

        &:hover {
            background: #f6f7fb;
        }
    }
}

.slist {
    max-height: 500px;
    overflow-y: auto;

    hr:first-child {
        display: none;
    }
}

.vj-search_results {
    background-color: #ffffff;
    border: 1px solid #ccc;
    border-radius: 10px;
    border-top: none;
    box-shadow: 0 1px 7px rgba(0, 0, 0, .2);
    display: none;
    margin-top: 8px;
    padding: 10px 0;
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    z-index: 301;

    .stitle {
        span {
            font-size: 12px;
            color: #777;
        }
    }

    .stext {
        font-weight: 600;
        font-size: 13px;
        padding: 0 15px 5px;
        margin: 0;
        color: #5e6b92;
        text-transform: uppercase;
    }

    .smore {
        padding: 5px 20px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        strong  {
            color: #555;
        }

        &:hover {
            background: #f6f7fb;
        }
    }

    hr {
        margin: 10px 0;
    }
}

.vj-search-close {
    position: absolute;
    top: 3px;
    right: 42px;
    font-size: 14px;
    color: #949494;
    z-index: 999;
    cursor: pointer;
    padding: 10px 10px 10px 30px;
    display: none;
  }

@media (max-width: 340px) {
    .tg-themedropdown.btn-vip {
        display: none;
    }
}

@media (max-width: 386px) {
    .tg-rnav .tg-themedropdown {
        padding: 5px;
    }
}

.header_search {
    display: none;

    @media all and (max-width: 876px) {
        display: inline;
    }
}

.shide {
    overflow: hidden;
    position: absolute;
    top: -70px;
    width: 100%;
    height: 100%;
    z-index: 1000;
    left: 0;
    display: none;
    background-color: #ffffff;
    -webkit-transition: all 0.5s;
    -o-transition: all 0.5s;
    transition: all 0.5s;

    .search-hide {
        position: relative;
        width: 100%;
        height: 100%;

        form {
            position: relative;

            input {
                outline-width: 0;
                background-color: white;
                border: 0 none;
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                height: 50px;
                padding: 0 0 0 15px;
                width: 90%;
                float: left;
                margin-top: 5px;

                &:focus {
                    outline: none;
                }
            }

            .remove {
                background-color: transparent;
                border: 0 none;
                color: #555;
                padding: 15px 10px;
                font-size: 16px;
                float: right;
                cursor: pointer;
            }
        }
    }
}

.box-vip_active {
    position: relative;

    .btn-vip_active {
        background-color: red;
        border-color: #ffd2d2;
        border-radius: 20px;
        color: #ffffff;
        font-size: 14px;
        margin-left: 10px;
        padding: 5px 10px;
        font-weight: 700;

        &:hover {
            background-color: #ca0a0a;
        }

        @media all and (max-width: 600px) {
            span {
                display: none;
            }
        }
    }

    .form-vip_active {
        position: absolute;
        min-width: 300px;
        width: 100%;
        top: 50px;
        right: 0;
        padding: 20px;
        box-shadow: 0 0 15px 0 rgba(1, 41, 112, .1);
        border: 1px solid #cacaca;
        background: #ffffff;
        z-index: 100;
        transition: .3s;
        display: none;

        &.show {
            display: block;
        }

        @media all and (max-width: 300px) {
            position: fixed;
            top: 60px;
            min-width: 100%;
        }

        .form-control {
            border: 1px solid #E91E63;
            font-size: 16px;
            height: 40px;
            display: flex;
            align-items: center;
        }

        .btn-activevip {
            align-items: center;
            background: #E91E63;
            border: none;
            color: #ffffff;
            display: flex;
            flex-direction: column;
            font-size: 16px;
            height: 40px;
            justify-content: center;
        }
    }
}

.login-box {
    margin: 0 auto;
    width: 100%;
    text-align: center;

    .btn-login {
        padding: 9px 10px;
        border-radius: 2px;
        display: inline-block;
        color: #ffffff;
        background: #ec5252;
        width: 130px;
        margin: 5px;
        font-weight: 500;
        font-size: 15px;
    }

    .btn-register {
        padding: 9px 10px;
        border-radius: 2px;
        display: inline-block;
        color: #ffffff;
        background: #00aeef;
        width: 130px;
        margin: 5px 5px 5px 0;
        font-weight: 500;
    }
}

.img-user {
    width: 40px;
    height: 40px;
    @include border-radius(50%);
    border: 1px solid $color-border;
    background: #ECEFF1;
    line-height: 0;
    flex-shrink: 0;

    img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
    }
}

.uservip {
    position: relative;

    .vip-tag {
        position: absolute;
        bottom: -5px;
        left: -10px;
        font-size: 11px;
        line-height: 11px;
        font-weight: 600;
        color: red;
        background: #ffffff;
        padding: 2px 3px;
        display: flex;
        align-items: center;
        z-index: 2;
        border-radius: 3px;
        border: 1px solid #bfbfbf;
    }
}

@media (min-width: 999px) and (max-width: 1103px) {
    .tg-themedropdown.btn-vip{
        padding: 0 !important;
    }
}
