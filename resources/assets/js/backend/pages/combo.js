import { ajaxSubmit } from '../modules/ajax-submit';

$(function() {
    const $modalCourseType = $('#modal-form-courseType');

    $modalCourseType.on('show.bs.modal', function(event) {
        const $button = $(event.relatedTarget); // Button that triggered the modal

        $modalCourseType.find('.modal-title').text($button.data('title'));

        const $form = $modalCourseType.find('form');

        $form.attr('action', $button.data('form-action'));
        $form.find('input[name=_method]').val($button.data('form-method'));
        $form.find('.has-error').removeClass('has-error');
        $form.find('.help-block').text('');
        $modalCourseType.find('#courseType-name').val($button.data('input-name'));
    });

    $modalCourseType.on('shown.bs.modal', function() {
        $modalCourseType.find('#courseType-name').focus();
    });

    ajaxSubmit($modalCourseType.find('form'));

    const $modalCombo = $('#modal-form-combo');

    $modalCombo.on('show.bs.modal', function(event) {
        const $button = $(event.relatedTarget); // Button that triggered the modal

        $modalCombo.find('.modal-title').text($button.data('title'));

        const $form = $modalCombo.find('form');

        $form.attr('action', $button.data('form-action'));
        $form.find('input[name=_method]').val($button.data('form-method'));
        $form.find('.has-error').removeClass('has-error');
        $form.find('.help-block').text('');
        $modalCombo.find('#combo-name').val($button.data('input-name'));
        $modalCombo.find('#combo-price').val($button.data('input-price'));
        $modalCombo.find('#combo-type').val($button.data('input-type'));
        $modalCombo.find('#combo-courseTypes').val($button.data('input-course-types')).trigger('change');
    });

    $modalCombo.on('shown.bs.modal', function() {
        $modalCombo.find('#combo-name').focus();
    });

    ajaxSubmit($modalCombo.find('form'));

    $('.js-combo-status').on('change', function() {
        axios({
            method: 'POST',
            url: $(this).data('action'),
            params: {
                status: this.checked
            },
        })
    });
});
