import { ajaxSubmit } from '../modules/ajax-submit';

$(function() {
    $('#lesson-name').keyup(function () {
        $('#lesson-slug').val(window.helperFunc.titleToSlug($(this).val()));
        $('#lesson-seo_title').val($(this).val());
    })

    $('#sortable').sortable({
        axis: 'y',
        update: function (event, ui) {
            var data = $(this).sortable('serialize');

            $('#sortable').slideUp(function () {
                axios({
                    method: 'POST',
                    url: $('#sortable').data('url'),
                    data: data
                }).then(function(res) {
                    $('#sortable').slideDown();
                });
            });
        }
    });
    $('#sortable').disableSelection();

    const $modal = $('#modal-form-lesson');

    $modal.on('show.bs.modal', function(event) {
        const $button = $(event.relatedTarget); // Button that triggered the modal

        $modal.find('.modal-title').text($button.data('title'));

        const $form = $modal.find('form');

        $form.attr('action', $button.data('form-action'));
        $form.find('input[name=_method]').val($button.data('form-method'));
        $form.find('.has-error').removeClass('has-error');
        $form.find('.help-block').text('');

        $modal.find('#lesson-name').val($button.data('input-name'));
        $modal.find('#lesson-slug').val($button.data('input-slug'));
        $modal.find('#lesson-seo_title').val($button.data('input-seo_title'));
        $modal.find('#lesson-seo_description').val($button.data('input-seo_description'));
        $modal.find('#season_id').val($button.data('input-season_id'));
    });

    $modal.on('shown.bs.modal', function() {
        $modal.find('#lesson-name').focus();
    });

    ajaxSubmit($modal.find('form'));
});
