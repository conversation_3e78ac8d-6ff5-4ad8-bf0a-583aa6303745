const BASE_URL = process.env.MIX_APP_URL;

$(function() {
    const URL_UPLOAD_VIDEO = BASE_URL + 'services/course/curriculum/video-update';

    $('.js-submit-btn').click(function () {
        $('.js-loading-icon').removeClass('d-none');

        axios({
            method: 'POST',
            url: URL_UPLOAD_VIDEO,
            data: new FormData($('#js-upload-video')[0])
        }).then(function(res) {
            const { data } = res;

            if (data.updated) {
                $('.js-preview').attr('href', data.urlPreview)
                $('.js-errors').text('Cập nhật thành công!');
                $('#js-upload-video').trigger("reset");
                $('#uploadPreview').html('');
            } else {
                $('.js-errors').text('<PERSON>ậ<PERSON> nhật thất bại!');
            }
        }).catch(function(error) {
            if (error.response && error.response.status === 422) {
                const { errors } = error.response.data;
                let errorMsg = '';

                for (let [key, value] of Object.entries(errors)) {
                    errorMsg += value[0];
                }

                $('.js-errors').text(errorMsg);
            } else if (error.response && error.response.data && error.response.data.message) {
                $('.js-errors').text(error.response.data.message);
            }
        })
        .finally(function() {
            $('.js-loading-icon').addClass('d-none');
        });
    })
})
