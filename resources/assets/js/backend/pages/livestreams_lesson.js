import { ajaxSubmit } from '../modules/ajax-submit';

$(function() {
    const $fromWrapper = $('.js-form-wrapper');

    $('.js-edit-livestreamsLesson').on('click', function(event) {
        const $button = $(this);

        $fromWrapper.find('.from-title').text($button.data('title'));

        const $form = $fromWrapper.find('form');

        $form.attr('action', $button.data('form-action'));
        $form.find('input[name=_method]').val($button.data('form-method'));
        $form.find('.has-error').removeClass('has-error');
        $form.find('.help-block').text('');

        $form.find('#name-input').addClass('edit');
        $form.find('#name-input').val($button.data('input-name'));
        $form.find('#slug-input').val($button.data('input-slug'));
        $form.find('#description-input').val($button.data('input-description'));
        $form.find('#lesson-type').val($button.data('input-type'));
        $form.find('#url-input').val($button.data('input-url'));
        $form.find('#published_at-input').val($button.data('input-published_at'));
        $form.find('#index-input').val($button.data('input-index'));
        $form.find(`#lesson-status input[type=radio][value=${$button.data('input-status')}]`).attr('checked', 'checked');
        $form.find('#image_preview').attr('src', $button.data('input-banner'));

        $form.find('#lesson-name').focus();
    });

    ajaxSubmit($fromWrapper.find('form'));
});
