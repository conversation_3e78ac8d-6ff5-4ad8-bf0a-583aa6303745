import { initImageUploadInput } from '../modules/ajax-upload-image';
import helperFunc from '../modules/helpers';

window.helperFunc = helperFunc;

$(function () {
    initImageUploadInput();

    $('.js-change-status').on('change', function() {
        axios({
            method: 'POST',
            url: $(this).data('action'),
            params: {
                status: this.checked
            },
        })
    });

    $('#name-input').not('.edit').on('input', function () {
        $('#slug-input').val(window.helperFunc.titleToSlug($(this).val(), 100));
        $('#seo_title-input').val($(this).val());
    })

    $('#slug-input').closest('form').find('button[type="submit"]').on('click', function() {
        $('#slug-input').val(window.helperFunc.titleToSlug($('#slug-input').val(), 100));
    })
})

// remove FREE TRIAL EXPIRED foreground MathType
function loaded(selector, callback) {
    // Trigger after page load.
    $(function () {
        callback($(selector));
    });

    // Trigger after page update using MutationObserver.
    const observer = new MutationObserver(function (mutationsList) {
        mutationsList.forEach(function (mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function (node) {
                    if ($(node).is(selector)) {
                        callback($(node));
                    }
                });
            }
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

loaded('.wrs_modal_dialogContainer', function (el) {
    // some action
    el.find('.wrs_tickContainer').remove();
});
