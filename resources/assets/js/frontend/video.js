import videojs from 'video.js';

import 'videojs-youtube';

window.videojs = videojs;

require('@silvermine/videojs-quality-selector')(videojs);

$(function() {
    if ($('#vj-video').length) {
        const options = {
            html5: {
                hls: {
                    overrideNative: true,
                    enableLowInitialPlaylist:true,
                    cacheEncryptionKeys:true
                },
                nativeAudioTracks: false,
                nativeVideoTracks: false
            },
            controlBar: {
                // https://docs.videojs.com/tutorial-components.html
                children: [
                    'playToggle',
                    'volumePanel',
                    'progressControl',
                    'remainingTimeDisplay',
                    'qualitySelector',
                    'pictureInPictureToggle',
                    'fullscreenToggle',
                ],
            }
        };

        const player = videojs('vj-video', options);
        const urlUpdateProcess = $('#js-updateProcess')?.data('update-process') || '';
        let viewed = false;

        const videoHD = $('#js-videourl').data('videourl');
        const videoSD = $('#js-videourl').data('videourl_480');

        let source = [
            {
                src: videoHD,
                type: 'application/x-mpegURL',
                label: '720P',
                selected: true,
            },
        ];

        if (videoSD) {
            source = [
                {
                    src: videoHD,
                    type: 'application/x-mpegURL',
                    label: '720P',
                    selected: false,
                },
                {
                    src: videoSD,
                    type: 'application/x-mpegURL',
                    label: '480P',
                    selected: true,
                },
            ];
        }

        player.src(source);

        player.ready(function() {
            videojs.log('Your player is ready!');
            // In this context, `this` is the player that was created by Video.js.
            const playPromise = player.play();

            if (playPromise) {
                playPromise.catch(function(error) {
                    videojs.log('video.play() failed with error:', error);
                    // Autoplay was prevented.
                    if (error.name === 'NotAllowedError') {
                        videojs.log('Attempting to play with video muted');

                        $('#js-main-video').prepend(btnMutedTemplate());
                        player.autoplay('muted');

                        return player.play();
                    }
                });
            }

            player.on('playing', function() {
                $('.btn-muted').click(function() {
                    $('.vjs-mute-control').trigger('click');
                    $(this).hide();
                });
            })

            player.on('timeupdate', function() {
                if (urlUpdateProcess && this.currentTime() / this.duration() > 0.8 && !viewed) { // 80% time
                    viewed = true;
                    axios.post(new URL(urlUpdateProcess).pathname);
                }
            });

            // How about an event listener?
            player.on('ended', function() {
                videojs.log('Awww...over so soon?!');
                const $upnext = $('#js-upnext');

                if ($upnext.length) {
                    upNextVideo(player, false, $upnext.data('upnext-url'), $upnext.data('upnext-name'));
                } else {
                    const $nextLecture = $('.js-list-lecture').find('.sub-part a:first');

                    if ($nextLecture.length) {
                        upNextVideo(youtubePlayer, false, $nextLecture.attr('href'), $nextLecture.text());
                    } else {
                        upNextVideo(youtubePlayer, true);
                    }
                }
            });

            // How about an event listener?
            player.on('error', function() {
                $('#vj-video').attr('poster', '../../../images/video-not-found.png');
            });
        });
    }

    if ($('#vj-video_youtube').length) {
        const youtubeUrl = $('#js-videourl').data('videourl');
        const youtubePlayer = videojs('vj-video_youtube', {
            fluid: true,
            techOrder: ['youtube'],
            sources: [{
                type: 'video/youtube',
                src: youtubeUrl
            }],
            youtube: {
                // ytControls: 2,
                rel: 0,           // Không hiển thị video liên quan sau khi kết thúc
                iv_load_policy: 3 // Ẩn các chú thích của video
            }
        });

        youtubePlayer.ready(function() {
            // tech_ là videojs-youtube nội bộ, chứa YT.Player
            const ytPlayer = this.tech_.ytPlayer;
            // 1) Bắt event state change
            ytPlayer.addEventListener('onStateChange', function(e) {
                // e.data giống YT.PlayerState
                if (e.data === YT.PlayerState.BUFFERING) {
                    console.log('YouTube đang buffer…');
                }
                if (e.data === YT.PlayerState.ENDED) {
                    console.log('YouTube đã phát xong!');
                }
            });

            // How about an event listener?
            youtubePlayer.on('waiting', function() {
                youtubePlayer.one('canplaythrough', function() {
                  youtubePlayer.play();
                });
            });

            youtubePlayer.on('ended', function() {
                const $nextLecture = $('.js-list-lecture .sub-part.active').next();

                if ($nextLecture.length) {
                    upNextVideo(youtubePlayer, false, $nextLecture.find('a').attr('href'), $nextLecture.text());
                } else {
                    upNextVideo(youtubePlayer, true);
                }
            });
        });
    }
})

function upNextVideo(player, isLastVideo = false, upnextUrl, upnextName = 'Bài học tiếp theo') {
    if (isLastVideo) {
        upnextName = 'Bài học đã kết thúc (Xem lại)';
        upnextUrl = window.location.href;
    }

    const option = {
        timeout: 5000,
        headText: isLastVideo ? '' : 'Tiếp tục bài học',
        cancelText: 'Hủy',
        titleText: upnextName,
        next: function() {
            window.location.href = upnextUrl
        }
    }

    const dashOffsetTotal = 586;
    const dashOffsetStart = 293;
    const interval = 50;
    const chunkSize = (dashOffsetTotal - dashOffsetStart) / (option.timeout / interval);
    let timeout;
    let start;
    let now;
    let newOffset;

    const upNextEvents = new videojs.EventTarget();

    upNextEvents.one('cancel', () => {
        clearTimeout(timeout);
        showUpNextVideo(false);
    });

    upNextEvents.one('next', () => {
        clearTimeout(timeout);
        showUpNextVideo(false);
        option.next();
    });

    upNextEvents.one('playing', () => {
        clearTimeout(timeout);
        showUpNextVideo(false);
    });

    player.addClass('vjs-upnext');
    $('.vjs-upnext').prepend(getMainTemplate(option));

    const autoplayRing = $('.vjs-upnext-svg-autoplay-ring');
    const cancelButton = $('.vjs-upnext-cancel-button');
    const nextButton = $('.vjs-upnext-autoplay-icon');

    cancelButton.click(function() {
        upNextEvents.trigger('cancel');
    });

    nextButton.click(function() {
        upNextEvents.trigger('next');
    });

    autoplayRing.attr('stroke-dasharray', dashOffsetStart);
    autoplayRing.attr('stroke-dashoffset', -dashOffsetStart);

    const update = function() {
        now = option.timeout - (new Date().getTime() - start);

        if (now <= 0) {
            clearTimeout(timeout);
            if (!hasShowDialog() && !isLastVideo) option.next();
            // showUpNextVideo(false);
        } else {
            newOffset = Math.max(-dashOffsetTotal, autoplayRing.attr('stroke-dashoffset') - chunkSize);
            autoplayRing.attr('stroke-dashoffset', newOffset);
            timeout = setTimeout(update, interval);
        }
    };

    start = new Date().getTime();
    timeout = setTimeout(update, interval);

    player.on('playing', function() {
        upNextEvents.trigger('playing');
    });
}

function showUpNextVideo(flag = true) {
    if (flag) {
        $('.vjs-upnext-content').show();
    } else {
        $('.vjs-upnext-content').hide();
    }
}

function hasShowDialog() {
    return ($('#login-box').css('display') == 'block' || $('#buy-course-notification').css('display') == 'block');
}

function getMainTemplate(options) {
    return `
        <div class="vjs-upnext-content">
            <div class="vjs-upnext-top">
                <span class="vjs-upnext-headtext">${options.headText}</span>
                <div class="vjs-upnext-title">${options.titleText}</div>
            </div>
            <div class="vjs-upnext-autoplay-icon">
                <svg height="100%" version="1.1" viewbox="0 0 98 98" width="100%">
                    <circle class="vjs-upnext-svg-autoplay-circle" cx="49" cy="49" fill="#000" fill-opacity="0.8" r="48"></circle>
                    <circle class="vjs-upnext-svg-autoplay-ring" cx="-49" cy="49" fill-opacity="0" r="46.5" stroke="#FFFFFF" stroke-width="4" transform="rotate(-90)"></circle>
                    <polygon class="vjs-upnext-svg-autoplay-triangle" fill="#fff" points="32,27 72,49 32,71"></polygon>
                </svg>
            </div>
            <span class="vjs-upnext-bottom">
                <span class="vjs-upnext-cancel">
                    <button class="vjs-upnext-cancel-button" tabindex="0" aria-label="Cancel autoplay">${options.cancelText}</button>
                </span>
            </span>
        </div>
    `;
}

function btnMutedTemplate() {
    return `
        <button class="btn btn-muted">
            <i class="fas fa-volume-mute"></i>
            <span class="muted-text">Nhấn để bật tiếng</span>
        </button>
    `;
}
