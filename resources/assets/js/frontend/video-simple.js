import videojs from 'video.js';

window.videojs = videojs;

require('@silvermine/videojs-quality-selector')(videojs);

$(function () {
    if ($('#vj-video').length) {
        const options = {
            html5: {
                hls: {
                    overrideNative: true,
                    enableLowInitialPlaylist:true,
                    cacheEncryptionKeys:true
                },
                nativeAudioTracks: false,
                nativeVideoTracks: false
            },
            controlBar: {
                // https://docs.videojs.com/tutorial-components.html
                children: [
                    'playToggle',
                    'volumePanel',
                    'progressControl',
                    'remainingTimeDisplay',
                    'qualitySelector',
                    'pictureInPictureToggle',
                    'fullscreenToggle',
                ],
            }
        };

        const player = videojs('vj-video', options);
        let source = [];

        $('#vj-video span.source').each(function () {
            source.push({
                src: $(this).data('src'),
                type: $(this).data('type'),
                label: $(this).data('label'),
            });
        })

        player.src(source);

        player.ready(function() {
            videojs.log('Your player is ready!');

            // In this context, `this` is the player that was created by Video.js.
            const playPromise  = player.play();

            if (playPromise) {
                playPromise.catch(function(error) {
                    videojs.log('video.play() failed with error:', error);
                    // Autoplay was prevented.
                    if (error.name === 'NotAllowedError') {
                        videojs.log('Attempting to play with video muted');
                        player.autoplay('muted');

                        return player.play();
                    }
                });
            }

            // How about an event listener?
            player.on('ended', function() {
                videojs.log('Awww...over so soon?!');
            });
        });
    }
})
