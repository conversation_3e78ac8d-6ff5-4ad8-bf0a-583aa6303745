$(function() {
    $('.js-home-course-slide').owlCarousel({
        nav: true,
        loop: true,
        dots: false,
        slideBy: 'page',
        rewind: false,
        responsive: {
            0: { items: 1 },
            375: { items: 1.5 },
            420: { items: 1.75 },
            500: { items: 2 },
            600: { items: 2.5 },
            800: { items: 3.25 },
            1000: { items: 4 },
            1200: { items: 4.75 },
            1300: { items: 5 }
        },
        smartSpeed: 70,
        onInitialized: function(e) {
            $(e.target).find('.course_banner').each(function(e) {
                if (this.complete) {
                    $(this).closest('.course-inner').find('.loader-circle').hide();
                    $(this).closest('.course-inner').find('.course-img').css('background-image', 'url(' + $(this).attr('src') + ')');
                } else {
                    $(this).bind('load', function(e) {
                        $(e.target).closest('.course-inner').find('.loader-circle').hide();
                        $(e.target).closest('.course-inner').find('.course-img').css('background-image', 'url(' + $(this).attr('src') + ')');
                    });
                }
            });
        },
        navText: ['<svg viewBox="0 0 24 24"><path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"></path></svg>', '<svg viewBox="0 0 24 24"><path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"></path></svg>']
    });

    const w = $(window).width();

    $('.js-vicon-slide').owlCarousel({
        nav: false,
        loop: true,
        autoplay: w < 1200,
        dots: false,
        slideBy: 1,
        rewind: false,
        autoplayTimeout: 3000,
        autoplaySpeed: 500,
        autoplayHoverPause: true,
        smartSpeed: 70,
        startPosition: 0,
        center: false,
        autoWidth: true,
        responsive: {
            0: { items: 1 },
            400: { items: 2 },
            600: { items: 2.5 },
            800: { items: 2.7 },
            1000: { items: 3.5 },
            1200: { items: 4 },
        },
    });

    $('.js-vreview-slide').owlCarousel({
        nav: false,
        loop: true,
        autoplay: w < 1200,
        dots: false,
        slideBy: 1,
        rewind: false,
        autoplayTimeout: 3000,
        autoplaySpeed: 500,
        autoplayHoverPause: true,
        smartSpeed: 70,
        startPosition: 0,
        center: false,
        autoWidth: true,
        responsive: {
            0: { items: 1 },
            400: { items: 2 },
            600: { items: 2.5 },
            800: { items: 2.7 },
            1000: { items: 3.5 },
            1200: { items: 4 },
        },
    });
});
