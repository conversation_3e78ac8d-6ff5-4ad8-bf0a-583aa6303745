$(function() {
    initModalBookPicker();
})

function initModalBookPicker() {
    function loadBooks($data) {
        $modal.find('.js-loading').show();
        $listBooks.html('');

        let params = {};

        if ($data.class_id) {
            params.class_id = $data.class_id;
        }

        if ($data.subject_id) {
            params.subject_id = $data.subject_id;
        }

        axios
            .get('/suggest/question/books', { params })
            .then(function(response) {
                const { data } = response;
                let html = '';

                data.results.forEach(book => {
                    html += renderBook($data, book);
                });

                html += renderBook($data);

                $listBooks.html(html);
            })
            .finally(function() {
                $modal.find('.js-loading').hide();
            });
    }

    const $modal = $('#modal-book-picker');
    const $listBooks = $modal.find('.js-books');

    $modal.on('show.bs.modal', function(event) {
        const $button = $(event.relatedTarget);
        const $data = {
            'class_id': $button.data('classlevel'),
            'subject_id': $button.data('subject'),
            'href': $button.data('action'),
            'type': $button.data('type') || null,
        };

        loadBooks($data);
    });

    function renderBook($data, book = null) {
        let $href = $data.href;

        if (book) {
            let type = 'test';

            if ($data.type && book[`${$data.type}_count`]) {
                type = $data.type;
            } else if (book?.test_count == 0 && book?.essay_count > 0) {
                type = 'essay';
            }

            $href += `?type=${type}&book=${book.id}`;

            return `
                <a href="${ $href }" class="list-group-item list-group-item-action">
                    ${ book.text }
                </a>
            `;
        }

        return `
            <a href="${ $href }" class="list-group-item list-group-item-action">
                Chương trình cũ
            </a>
        `;
    }
}
