import { addValidator } from '../../modules/add-validator';
import { authSubmit } from '../../modules/auth-submit';

$(function() {
    addValidator();

    const validateLogin = {
        formElement: '.js-login-form',
        rulesValidate: {
            rules: {
                email: {
                    required: true,
                    email: true
                },
                password: {
                    required: true,
                    password: true
                }
            },
            messages: {
                email: {
                    required: '* Bạn chưa nhập email',
                    email: '* Không đúng định dạng email'
                },
                password: {
                    required: '* <PERSON>ạn chưa nhập mật khẩu',
                    password: '* Mật khẩu không được có khoảng trắng'
                },
            }
        }
    };

    const validateRegister = {
        formElement: '.js-register-form',
        rulesValidate: {
            rules: {
                first_name: {
                    required: true,
                    minlength: 3,
                    maxlength: 30
                },
                email: {
                    required: true,
                    email: true
                },
                password: {
                    required: true,
                    password: true
                }
            },
            messages: {
                first_name: {
                    required: '* <PERSON><PERSON><PERSON> chưa nhập tên',
                    minlength: '* Tên người dùng dài từ 3 đến 30 ký tự',
                    maxlength: '* Tên người dùng dài từ 3 đến 30 ký tự'
                },
                email: {
                    required: '* Bạn chưa nhập email',
                    email: '* Không đúng định dạng email'
                },
                password: {
                    required: '* Bạn chưa nhập mật khẩu',
                    password: '* Mật khẩu không được có khoảng trắng'
                },
            }
        }
    };

    const studentAskSupport = {
        formElement: '.js-student-ask-support',
        rulesValidate: {
            rules: {
                first_name: {
                    required: true,
                    minlength: 3,
                    maxlength: 30
                },
                phone: {
                    required: true,
                    phone: true
                },
                email: {
                    required: true,
                    email: true
                },
            },
            messages: {
                first_name: {
                    required: '* Bạn chưa nhập tên',
                    minlength: '* Tên người dùng dài từ 3 đến 30 ký tự',
                    maxlength: '* Tên người dùng dài từ 3 đến 30 ký tự'
                },
                phone: {
                    required: '* Bạn chưa nhập số điện thoại',
                    phone: '* Số điện thoại không hợp lệ'
                },
                email: {
                    required: '* Bạn chưa nhập email',
                    email: '* Không đúng định dạng email'
                },
            }
        }
    };

    validateForm(validateLogin);
    validateForm(validateRegister);
    validateForm(studentAskSupport, false);

    // Prevent multiple time form submit with enter key 
    const $passwordInput = $('.form-signin1').find('input[type="password"]');
    const $emailInput = $('.form-signin1').find('input[name="email"]');

    $passwordInput.bind('keyup', function(e) {
        inputKeyupHandler(e);
    });

    $emailInput.bind('keyup', function(e) {
        inputKeyupHandler(e);
    });

    function inputKeyupHandler(e) {
        if (e.which === 13) {
            console.log('Enter key!');
            $passwordInput.unbind('keyup');
            $emailInput.unbind('keyup');

            return setTimeout(function() {
                console.log('Rebind keyup event');
                $passwordInput.keyup(inputKeyupHandler);
                $emailInput.keyup(inputKeyupHandler);
            }, 20000);
        }
    }
    // end
})

function validateForm({ formElement, rulesValidate }, reload = true){
    $(formElement).validate({
        ...rulesValidate,
        errorElement: 'div',
        errorPlacement: function(place, element) {
            place.addClass('error-message').appendTo(element.closest('div'));
        },
        highlight: function(element, errorClass, validClass) {
            if (element.type === "radio") {
                this.findByName(element.name).addClass(errorClass).removeClass(validClass);
            } else if (element.type === "select-one" || element.type === "select-multiple") {
                var $element = $(element);
                $element.addClass(errorClass).removeClass(validClass);
                var $next = $element.next();
                if ($next.length > 0 && $next.hasClass('select2')) {
                    $next.addClass(errorClass).removeClass(validClass);
                }
            } else {
                $(element).addClass(errorClass).removeClass(validClass);
            }
        },
        unhighlight: function(element, errorClass, validClass) {
            if (element.type === "radio") {
                this.findByName(element.name).addClass(validClass).removeClass(errorClass);
            } else if (element.type === "select-one" || element.type === "select-multiple") {
                var $element = $(element);
                $element.addClass(validClass).removeClass(errorClass);
                var $next = $element.next();
                if ($next.length > 0 && $next.hasClass('select2')) {
                    $next.addClass(validClass).removeClass(errorClass);
                }
            } else {
                $(element).addClass(validClass).removeClass(errorClass);
            }
        },
        submitHandler: function(form) {
            var $thisForm = $(form);

            if ($thisForm.valid()) {
                authSubmit($thisForm, reload);
            }
        }
    });
}
