$(function() {
    var systemType = window.helperFunc.getMobileOperatingSystem();
    const URL_IOS = 'https://apps.apple.com/us/app/vietjack/id1490262941';
    const URL_ANDROID = 'https://play.google.com/store/apps/details?id=com.jsmile.android.vietjack';

    switch (systemType) {
        case 'iOS':
            $('#header .js-app-install').show();
            break;
        case 'Android':
            $('#header .js-app-install').show();
            break;
        default:
            $('#header .js-app-install').hide();
    }

    $('.js-download_app').on('click', function(event) {
        event.preventDefault();

       if (systemType == 'iOS') {
            window.open(URL_IOS, '_blank');
        } else {
            window.open(URL_ANDROID, '_blank');
        }
    })

    if ($('.js-app-install').css('display') == 'block') {
        $('.js-space-clear').css('padding-top', '110px');
    }

    $('.js-btn-close').on('click', function() {
        $('.js-app-install').hide();
        $('.js-space-clear').css('padding-top', '55px');
    })

    // Header scroll class
    // $(window).scroll(function() {
    //     var height = $('.js-app-install').css('display') == 'none' ? 0 : $('.js-app-install').height();

    //     if ($(this).scrollTop() > height) {
    //         $('#header .main-menu-header').addClass('fixed-top');
    //     } else {
    //         $('#header .main-menu-header').removeClass('fixed-top');
    //     }
    // });
})
