$.fn.select2.defaults.set('language', {
    inputTooLong: function(args) {
        var overChars = args.input.length - args.maximum;

        var message = 'Vui lòng xóa bớt ' + overChars + ' ký tự';

        return message;
    },
    inputTooShort: function(args) {
        var remainingChars = args.minimum - args.input.length;

        var message = 'Vui lòng nhập thêm từ ' + remainingChars +
            ' ký tự trở lên';

        return message;
    },
    loadingMore: function() {
        return 'Đang lấy thêm kết quả…';
    },
    maximumSelected: function(args) {
        var message = 'Chỉ có thể chọn được ' + args.maximum + ' lựa chọn';

        return message;
    },
    noResults: function() {
        return 'Không tìm thấy kết quả';
    },
    searching: function() {
        return 'Đang tìm…';
    },
    removeAllItems: function() {
        return '<PERSON><PERSON><PERSON> tất cả các mục';
    },
    errorLoading: function() {
        return 'Không thể tải được kết quả';
    },
});

$('.js-select2-ajax').each(function() {
    const $select2 = $(this);
    const selectedValues = $select2.data('ajax-selected-values');

    if (!selectedValues || $select2.val() == selectedValues) {
        return;
    }

    const ajaxUrl = $select2.data('ajax-url');

    $.ajax({
        url: ajaxUrl,
        type: 'GET',
        data: {
            selectedValues: selectedValues,
        },
    }).done(function(data) {
        if (!data || !data.results || !data.results.length) {
            return;
        }

        for (let index = 0; index < data.results.length; index++) {
            const result = data.results[index];
            const option = new Option(result.text, result.id, true, true);
            $select2.append(option);
        }

        $select2.trigger({
            type: 'select2:select',
        });
    });
});
