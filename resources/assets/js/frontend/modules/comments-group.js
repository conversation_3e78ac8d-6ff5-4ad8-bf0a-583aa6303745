$(function() {
    $('.js-comments-group')
        .on('focusin', '.js-input', function () {
            const $ta = $(this);
            if ($ta.val().trim() === '') {
                this.setAttribute('style', 'min-height:70px;overflow-y:hidden;padding-bottom:40px;');
                $(this).closest('.form-comment').find('.js-upload-btn').show();
            }
        })
        .on('focusout', '.js-input', function (e) {
            const $ta = $(this);
            const next = e.relatedTarget;
            const isUploadBtnFocused = next && ($(next).is('.js-upload-btn') || $(next).closest('.js-upload-btn').length);
            if ($ta.val().trim() === '' && !isUploadBtnFocused) {
              this.setAttribute('style', 'height:auto;');
              $ta.closest('.form-comment').find('.js-upload-btn').hide();
            }
        }).on('input', '.js-input', function () {
            this.style.height = (this.scrollHeight) + 'px';
        });

    $('.js-comments-group').on('click', '.js-upload-btn', function() {
        const $wrapper = $(this);
        const $form = $wrapper.closest('form');
        const $inputFile = $form.find('.js-input-choose-image');

        $inputFile.click();
    })

    $('.js-comments-group').on('change', '.js-input-choose-image', function() {
        const $wrapper = $(this);
        const $form = $wrapper.closest('form');
        const inputFile = this;

        if (inputFile.files.length) {
            uploadImage(inputFile.files[0], $form);
        }
    })

    $('.js-comments-group').on('click', '.js-images .js-remove-img', function() {
        const $wrapper = $(this);
        const $form = $wrapper.closest('form');
        const $gridImages = $form.find('.js-images');
        const $inputImages = $form.find('.js-file-path');
        const $inputFile = $form.find('.js-input-choose-image');

        const $oldPath = $inputImages.val();

        removeImage($oldPath);
        $gridImages.html('');
        $inputImages.val('');
        $inputFile.val('');
    })

    $('.js-comments-group').on('click', '.js-submit-btn', function(event) {
        event.preventDefault();

        const $wrapper = $(this);
        const $form = $wrapper.closest('form');

        submitComment($form);
    })

    $('.js-comments-showmore').on('click', function(event) {
        event.preventDefault();
        const _that = $(this);

        axios.get(_that.attr('data-action')).then(function(response) {
            const { data } = response;

            if (data.hasMore) {
                _that.attr('data-action', data.action);
            } else {
                _that.css('display', 'none');
            }

            $('.js-comments-list').append(data.html);
        })
    })
})

function uploadImage(file, $form) {
    if (file) {
        const $showMediaWrapper = $form.find('.show-media');
        const $helpBlock = $form.find('.js-help-block');
        const $inputImages = $form.find('.js-file-path');
        const $gridImages = $form.find('.js-images');
        const formData = new FormData();

        formData.append('image', file);
        $showMediaWrapper.removeClass('has-error');
        $helpBlock.text('');

        axios.post('/comments/upload-image', formData).then(function(response) {
            const { data } = response;

            if (data.uploaded) {
                $inputImages.val(data.path);
                $gridImages.html(renderImage(data));
            } else {
                $showMediaWrapper.addClass('has-error');
                $helpBlock.text(data.error.message);
            }
        })
        .catch(function(error) {
            console.error(error);
        });
    }
}

function removeImage(path) {
    axios.post('/comments/remove-image', { path }).catch(function(error) {
        console.error(error);
    });
}

function renderImage(image) {
    return `<div>
                <img src="${image.url}" data-path="${image.path}" alt="Picture" class="img-thumbnail">
                <i class="far fa-trash-alt remove-img js-remove-img"></i>
            </div>`;
}

function submitComment($form) {
    const $gridImages = $form.find('.js-images');
    const $showMediaWrapper = $form.find('.show-media');
    const $helpBlock = $form.find('.js-help-block');

    $showMediaWrapper.removeClass('has-error');
    $helpBlock.text('');

    var formData = new FormData($form[0]);

    axios({
        method: $form.attr('method'),
        url: $form.attr('action'),
        data: formData,
    })
    .then(function(res) {
        $form.find('input[type=text], textarea').val('');
        $gridImages.html('');

        if (res.data.action == 'question') {
            $('.js-comments-list').prepend(res.data.html);
        } else if (res.data.action == 'answer') {
            const $replyWrapper = $form.closest('.panel-body').find('.js-replies-list');
            $replyWrapper.append(res.data.html);
        } else {
            window.location.reload();
        }
    })
    .catch(function(error) {
        if (error.response && error.response.status === 422) {
            const { errors } = error.response.data;
            for (let [key, value] of Object.entries(errors)) {
                $showMediaWrapper.addClass('has-error');
                $helpBlock.text(value[0]);
            }
        } else if (error.response && error.response.data && error.response.data.message) {
            var text = error.response.status === 419 ?
            'Phiên làm việc trên form đã hết hạn. Vui lòng tải lại trang!' :
            error.response.data.message;

            $('.js-msg .js-msg-content').text(text);
            $('.js-msg').removeClass('hide');
        } else {
            // window.location.reload();
        }
    });
}
