const configPositionToc = [];

// add table of content when
document.addEventListener('DOMContentLoaded', function() {
    removeContentEditableInCustomTemplate();
    tableOfContents();
});

//onScroll active event
window.addEventListener('scroll', throttle(activeTocByConfig, 100));

function tableOfContents(floatRight = true) {
    //Desktop
    var children = $('#post-content').find('.toc-heading');
    var countItem = children.length;

    if (countItem == 0) {
        return;
    }

    var htmlToc = '';
    let text = '',
        anchor = '',
        lvl = 1;

    for (var i = 0; i < countItem; i++) {
        text = $(children[i])[0].innerText;
        lvl = $(children[i]).attr('data-toc-lv')

        anchor = 'anchor-item-' + i;

        //add config position ToC item
        configPositionToc.push({ 'start': $(children[i]).offset().top, 'end': 0 });
        if (i > 0 && i < countItem) {
            configPositionToc[i - 1].end = $(children[i]).offset().top - 1;
        }
        if (i == countItem - 1) {
            configPositionToc[i].end = $('footer').offset().top - 500;
        }

        //set id for target
        $(children[i]).prop('id', anchor);

        htmlToc += `<li class="toc-nav-item lv-item-${lvl}"><a class="${anchor}" href="#${anchor}">${text}</a></li>`;

        anchor = '';
        text = '';
    }

    document.getElementById('toc').insertAdjacentHTML('beforeend', htmlToc);
    activeTocByConfig();
}

function activeLinkToC() {
    //not use
    var tocElement = document.getElementById("toc");
    var contentPost = document.getElementById('post-content');
    var windowTop = (document.documentElement.scrollTop || document.body.scrollTop) + 300;
    //get element heading when user scroll to
    var currentElement = $('#post-content').find('.toc-heading').filter(function() {
        return windowTop > $(this).offset().top && windowTop < ($(this).offset().top + 320 + $(this).outerHeight());
    });

    if (currentElement.length > 0) {
        //get current anchor element scrollTo and add link active in ToC
        let anchor = currentElement[0].id;
        let activeLink = $('#toc').find('a').filter(function() {
            return $(this).attr("href").includes(anchor)
        });

        if (activeLink.length > 0) {
            $('#toc').find('a').attr('class', '');
            $(activeLink).attr('class', 'active');
        }
    }
}

function activeTocByConfig() {
    if (configPositionToc.length == 0) {
        return;
    }

    let windowTop = (document.documentElement.scrollTop || document.body.scrollTop) + 250;
    let itemActive = configPositionToc.findIndex(item => {
        return item.start < windowTop && item.end > windowTop;
    });

    $('#toc a').removeClass('active');

    if (itemActive >= 0) {
        $('#toc a.anchor-item-' + itemActive).addClass('active');
    }
}

$(document).ready(function() {
    $(document).on('click', '.toc a[href^="#"]', function(event) {
        event.preventDefault();

        let anchor = $.attr(this, 'href');
        $('html, body').animate({
            scrollTop: $(anchor).offset().top - 150,
        }, 500);

        //add class highlight and  remove after 2s
        $(anchor).addClass('highlight-target').delay(2000).queue(function(next) {
            $(this).removeClass('highlight-target');
            next();
        });
    });

    //toggle btn expanded toc
    $(document).on('click', '.btn-show-toc', function(event) {
        event.preventDefault();
        $(this).attr('class', 'btn-hide-toc');
        $(this).find('span').text(' Thu gọn');
        $(this).find('i').attr('class', 'fa fa-angle-up');
        $('#toc li').css('display', 'block');
    });

    $(document).on('click', '.btn-hide-toc', function(event) {
        event.preventDefault();
        $(this).attr('class', 'btn-show-toc');
        $(this).find('span').text(' Xem thêm');
        $(this).find('i').attr('class', 'fa fa-angle-down');
        $('#toc li:nth-child(n+7)').css('display', 'none');
    });

    $(".show-more a").each(function() {
        var $link = $(this);
        var $content = $link.parent().prev("div.section-content");

        var visibleHeight = $content[0].clientHeight;
        var actualHide = $content[0].scrollHeight - 1;

        if (actualHide > visibleHeight) {
            $link.show();
        } else {
            $link.hide();
        }
    });

    $(".show-more a").on("click", function() {
        var $link = $(this);
        var $content = $link.parent().prev("div.section-content");
        // var linkText = $link.text();
        var btnClass = $link.attr('class');
        var btnHref = $link.attr('href');

        if (btnClass === "btn-show-more") {
            $link.removeClass('btn-show-more').addClass('btn-show-less').text('Thu gọn');
        } else if (btnClass === "btn-show-less") {
            $link.removeClass('btn-show-less').addClass('btn-show-more').text('Xem thêm');
            $('html, body').animate({
                scrollTop: $(btnHref).offset().top - 50
            }, 0);
        }

        $content.toggleClass("short-text");

        return false;
    });
});

//set contenteditable = false
function removeContentEditableInCustomTemplate() {
    if ($('#post-content')) {
        $('#post-content').find('[contenteditable]').attr('contenteditable', 'false');
    }
}

// func throttle (scroll)
// run event after time set
function throttle(fn, wait) {
    var time = Date.now();
    return function() {
        if ((time + wait - Date.now()) < 0) {
            fn();
            time = Date.now();
        }
    }
};
// func throttle limit event (keyup, keydown)
// run event after use finish the operation (time)
function debounce(fn, delay) {
    return args => {
        clearTimeout(fn.id)

        fn.id = setTimeout(() => {
            fn.call(this, args)
        }, delay)
    }
};
