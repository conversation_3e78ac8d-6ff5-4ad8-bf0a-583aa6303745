import videojs from 'video.js';

window.videojs = videojs;

$(function() {
    // start the slider
    $('.js-video-slider').slick({
        infinite: false,
        slidesToShow: 3.25,
        slidesToScroll: 1,
        autoplay: false,
        autoplaySpeed: 4000,
        lazyLoad: 'progressive',
        speed: 600,
        arrows: false,
        dots: false,
        responsive: [
            {
                breakpoint: 1024,
                settings: {
                    slidesToShow: 3.25,
                    slidesToScroll: 3,
                    infinite: true,
                    dots: false
                }
            },
            {
                breakpoint: 600,
                settings: {
                    slidesToShow: 2.25,
                    slidesToScroll: 2
                }
            },
            {
                breakpoint: 480,
                settings: {
                    slidesToShow: 1.25,
                    slidesToScroll: 1
                }
            }
        ]
    });

    $('.js-livestream-slider').slick({
        infinite: false,
        slidesToShow: 2.2,
        slidesToScroll: 1,
        autoplay: false,
        autoplaySpeed: 4000,
        lazyLoad: 'progressive',
        speed: 600,
        arrows: false,
        dots: false,
        responsive: [
            {
                breakpoint: 1025,
                settings: {
                    slidesToShow: 2.1,
                    slidesToScroll: 2,
                    infinite: true,
                    dots: false
                }
            },
            {
                breakpoint: 769,
                settings: {
                    slidesToShow: 1.5,
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 480,
                settings: {
                    slidesToShow: 1.5,
                    slidesToScroll: 1
                }
            }
        ]
    });

    $('.vjs-autoplay-icon').each(function() {
        const $wrapper = $(this);
        const $video = $wrapper.siblings('.video-js');

        $wrapper.on('click', function(event) {
            playFeeVideo($video.attr('id'), $wrapper);
        });
    });

    $('.video-slider').on('click', '.vjs-has-started', function(e) {
        const href = $(this).parent().find('.caption-link').attr('href');

        if (href) {
            window.location = href;
        }

        return false;
    })

    // pause and replay video
    $('.video-slider').on('click', '.video-action-btn', function(e) {
        const $videoEl = $(this).parent().find('.video-js');
        const icon = $(this).find('i');
        const player = videojs($videoEl.attr('id'));

        if (icon.hasClass('fa-pause')) {
            player.pause();
            icon.removeClass('fa-pause').addClass('fa-undo-alt');
        } else {
            pauseAllVideos();
            player.play();
            icon.removeClass('fa-undo-alt').addClass('fa-pause');
        }
    })
})

// pause all video playing
function pauseAllVideos() {
    $('.video-js').each(function () {
        if ($(this).hasClass('vjs-has-started')) {
            const videoId = $(this).attr('id');
            const icon = $(this).parent().find('.video-action-btn i');

            videojs(videoId).pause();
            icon.removeClass('fa-pause').addClass('fa-undo-alt');
        }
    });
}

function playFeeVideo(id, icon) {
    pauseAllVideos();

    // init videojs instance
    const options = {
        html5: {
            hls: {
                overrideNative: true
            },
            nativeAudioTracks: false,
            nativeVideoTracks: false
        },
        controls: false,
    };
    const player = videojs(id, options);

    player.src({
        src: $('#' + id).data('videourl'),
        type: 'application/x-mpegURL'
    });

    player.ready(function() {
        videojs.log('Your player is ready!');
        // In this context, `this` is the player that was created by Video.js.
        const playPromise = player.play();

        icon.hide();

        if (playPromise) {
            playPromise.catch(function(error) {
                videojs.log('video.play() failed with error:', error);
                // Autoplay was prevented.
                if (error.name === 'NotAllowedError') {
                    videojs.log('Attempting to play with video muted');
                    player.autoplay('muted');

                    return player.play();
                }
            });
        }
    });

    player.on('playing', function() {
        icon.parent().append(replayVideo());
    })

    player.on('ended', function() {
        videojs.log('Awww...over so soon?!');
        $('#' + id).parent().find('.video-action-btn i').removeClass('fa-pause').addClass('fa-undo-alt');
    });
};

function replayVideo() {
    return `
        <div class="video-action-btn">
            <i class="fas fa-pause"></i>
        </div>
    `;
}
