import debounce from 'lodash/debounce';
import helperFunc from '../modules/helpers';
import { vjScrollSlider } from '../modules/vj-scrollslider';

window.helperFunc = helperFunc;

$(document).ready(function () {
    mediumZoom('.medium-zoom-image');

    vjScrollSlider()

    const $header = $('#header');

    $('.bg-overflow').on('click', function () {
        if (!$(this).hasClass('active')) {
            return;
        }
        $('.bg-overflow').removeClass('active');
        // header v1
        // $('.menu-mobile').removeClass('show-menu-mobile');
        // header v2
        $('#tg-navigation').collapse('hide');
    });

    // menu header v2
    $('.tg-themetabnav > li > a').hover(function() {
        $(this).tab('show');
    });

    function toggleMenuMobile() {
        // setHeaderHeight();;

        if (window.matchMedia('(max-width: 1020px)').matches) {
            $('#tg-navigation').addClass('tg-menu-mobile');
        } else {
            $('#tg-navigation').removeClass('tg-menu-mobile');
        }
    }

    // function setHeaderHeight() {
    //     const getActualHeaderRecentHeight = $header.height();
    //     const getActualHeaderFullHeight = $header.css({ 'max-height': 'auto', 'height': 'auto' }).height();

    //     $header.css({ 'height': getActualHeaderRecentHeight }).animate({ 'height': getActualHeaderFullHeight }, 200);
    // }

    toggleMenuMobile();
    $(window).resize(debounce(toggleMenuMobile, 300));

    $('#tg-navigation').on('shown.bs.collapse', function () {
        $('.bg-overflow').addClass('active');
    });

    $('#tg-navigation').on('hidden.bs.collapse', function () {
        $('.bg-overflow').removeClass('active');
    });

    /* -------------------------------------
            COLLAPSE MENU SMALL DEVICES
    -------------------------------------- */
    function collapseMenu(){
        jQuery('.menu-item-has-children, .menu-item-has-mega-menu').prepend('<span class="tg-dropdowarrow"><i class="fa  fa-angle-right"></i></span>');

        if (helperFunc.isMobile()) {
            jQuery('.menu-item-has-children > a, .menu-item-has-mega-menu > a').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                jQuery(this).next().slideToggle(300);
                jQuery(this).parent().toggleClass('tg-open');
            });
        }

        jQuery('.menu-item-has-children > span, .menu-item-has-mega-menu > span').on('click', function() {
            jQuery(this).next().next().slideToggle(300);
            jQuery(this).parent('.menu-item-has-children, .menu-item-has-mega-menu').toggleClass('tg-open');
        });  
    }
    collapseMenu();

    const navigationareaHeight = $header.find('.tg-navigationarea').height();
    /* Set Sticky Header */
    $(window).on('scroll', function() {
        if ($(window).scrollTop() > ($header.height() - navigationareaHeight)) {
            $header.addClass('sticky-header');
        } else {
            $header.removeClass('sticky-header');
        }
    });
    // end

    $(document).one('mouseenter', '.js-my-course', function (e) {
        const $wrapper = $(this);
        axios.get($wrapper.find('a.menu-limit-text').attr('href'))
            .then(function (response) {
                const { data } = response;

                if (data.html) {
                    $wrapper.find('.my-course-dropdown').html(data.html);
                }
            });
    })

    $(document).one('click', '#notifications', function () {
        const $wrapper = $(this);
        const $box = $wrapper.parent();
        axios.post($wrapper.data('action')) 
            .then(function (response) {
                const { data } = response;
                if (data.html) {
                    $box.find('.list-notification').html(data.html);
                }
                $box.find('#notifications-count').addClass('hidden');
                $box.find('#notifications-count').html('0');
            });
    });

    $(document).one('click', '#vj-cart', function () {
        const $wrapper = $(this);
        const $box = $wrapper.parent();
        axios.get($wrapper.data('action') + window.location.search)
            .then(function (response) {
                const { data } = response;
                if (data.html) {
                    $box.find('.list-cart').html(data.html);
                }
            });
    });

    $('#forgot_password').on('submit', function(e){
        e.preventDefault();
        const $form = $(this);
        $form.find('[type=submit]').prop('disabled', true);
        var formData = new FormData($form[0]);
        axios({
            method: $form.attr('method'),
            url: $form.attr('action'),
            data: formData,
        })
        .then(function(response) {
            const { data } = response;
            if (data.message) {
                $form.find('.txt-msg').text(data.message);
            }
        }).finally(function() {
            $form.find('[type=text]').prop('disabled', true);
            $form.find('[type=submit]').prop('disabled', false);
        });
    });

    // hanlde click menu item
    $header.on('click', '.js-dropdown-toggle', function (e) {
        $(this).siblings('.js-dropdown-toggle').attr('data-showed', '');
        $(this).siblings('.js-dropdown-toggle').find('.box-dropdown').css('display', 'none');
        if ($(this).attr('data-showed')) {
            $(this).find('.box-dropdown').css('display', 'none');
            $(this).attr('data-showed', '');
        } else {
            $(this).find('.box-dropdown').css('display', 'block');
            $(this).attr('data-showed', true);
        }
    })
    $(document).mouseup(function(e) {
        var container = $header.find('.js-dropdown-toggle');
        // if the target of the click isn't the container nor a descendant of the container
        if (!container.is(e.target) && container.has(e.target).length === 0) {
            hideDropdownHeader();
        }
    });
    function hideDropdownHeader() {
        $header.find('.js-dropdown-toggle').attr('data-showed', '');
        $header.find('.js-dropdown-toggle .box-dropdown').css('display', 'none');
    }
    // end

    // search icon
    $('#searchIcon').on('click', function() {
        $('#shide').css({'top': '-2px', 'display': 'block'});
    });

    $('.remove').on('click', function() {
        $('#shide').fadeOut().css('top', '-80px');
    });

    const fetchDataSearch = (keyword, $resultsEl, hasHistory = false) => {
        const cleanKeyword = keyword.trim();

        if (cleanKeyword) {
            axios.get('/search/query', {
                params: {
                    q: cleanKeyword,
                    type: 'all',
                    limit: 3,
                }
            }).then(function(response){
                if (response?.data?.html) {
                    let html = response.data.html;

                    if (hasHistory) {
                        html = `
                            <ul class="slistbox">
                                <li>
                                    <a href="/search/query?q=${ keyword }&type=all" style="background: #eee;">
                                        <i class="fas fa-history"></i>
                                        <span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">${ keyword }</span>
                                    </a>
                                </li>
                            </ul>
                            ${html}
                        `;
                    }
                    $resultsEl.html(html);
                }
            }).catch(function(error) {
                console.error('Lỗi khi lấy data:', error);
            });
        }
    }

    const searchDebound = debounce(fetchDataSearch, 700)

    $('.vj-search_input').on('click', function() {
        const $input = $(this);
        const $wrapper = $input.closest('.vj-search-box');
        const $results = $wrapper.next('.vj-search_results');
        const $close = $wrapper.find('.vj-search-close');

        $('.vj-search_results').not($results).fadeOut(200);

        if ($results.children().length == 0) {
            $results.html(`
                <p class="text-center mb-0 text-muted">
                    <i class="fa fa-spinner fa-pulse js-loading-icon"></i> Vui lòng nhập nội dung bạn muốn tìm kiếm
                </p>
            `);

            const s_keyword = $input.val()?.trim() || localStorage.getItem('s_keyword')?.trim();

            if (s_keyword) {
                fetchDataSearch(s_keyword, $results, true);
            }
        }

        $results.fadeIn(200);
        $close.fadeIn(200);
    });

    $('.vj-search_input').on("keyup", function(ev) {
        const $wrapper = $(this).closest('.vj-search-box');
        const $results = $wrapper.next('.vj-search_results');

        searchDebound(ev.target.value, $results, false)
    });

    $('.vj-search-close').on('click', function () {
        const $wrapper = $(this).closest('.vj-search-box');
        const $input = $wrapper.find('.vj-search_input');
        const $results = $wrapper.next('.vj-search_results');
    
        $input.val('');
        // Xóa luôn nội dung đã tìm kiếm (nếu có)
        $results.empty();
    
        // Ẩn kết quả và nút close
        $results.fadeOut(200);
        $(this).fadeOut(200);
    });

    const fetchAdmissionSearch = (keyword = '', type = null, $resultsEl) => {
        const cleanKeyword = keyword.trim();

        axios.get('/admission/search/query', {
            params: {
                q: cleanKeyword,
                type,
                limit: 7,
            }
        }).then(function(response){
            if (response?.data?.html) {
                $resultsEl.html(response.data.html);
            }
        }).catch(function(error) {
            console.error('Lỗi khi lấy data:', error);
        });
    }

    const admissionSearchDebound = debounce(fetchAdmissionSearch, 700);

    $('.js-dsearch_input').on('click', function() {
        const $input = $(this);
        const $wrapper = $input.closest('.dsearch-form');
        const $results = $wrapper.next('.dsearch-results');
        const type = $wrapper.find('.js-dsearch_select')?.val() || null;
        const keyword = $input.val() || '';

        if ($results.children().length == 0) {
            $results.html(`
                <p class="text-center mb-0 text-muted">
                    <i class="fa fa-spinner fa-pulse js-loading-icon"></i> Vui lòng nhập nội dung bạn muốn tìm kiếm
                </p>
            `);

            fetchAdmissionSearch(keyword, type, $results);
        }

        $results.fadeIn(200);
    });

    $('.js-btn-dsearch').on('click', function() {
        const $wrapper = $(this).closest('.dsearch-form');
        const $input = $wrapper.find('.js-dsearch_input');

        if ($input) {
            $input.trigger('click');
        }
    });

    $('.js-dsearch_input').on("keyup", function(ev) {
        const $wrapper = $(this).closest('.dsearch-form');
        const $results = $wrapper.next('.dsearch-results');
        const type = $wrapper.find('.js-dsearch_select')?.val() || null;

        admissionSearchDebound(ev.target.value, type, $results)
    });

    $('.js-dsearch_select').on("change", function(ev) {
        const $wrapper = $(this).closest('.dsearch-form');
        const $results = $wrapper.next('.dsearch-results');
        const keyword = $wrapper.find('.js-dsearch_input')?.val() || '';
        const type = $(this).val() || null;

        admissionSearchDebound(keyword, type, $results);
    });

    $(document).on('click', e => {
        if (!$(e.target).closest('.vj-search_input, .vj-search_results').length) {
            $('.vj-search_results, .vj-search-close').fadeOut(200);
        }
        if (!$(e.target).closest('.js-dsearch_input, .dsearch-results').length) {
            $('.dsearch-results').fadeOut(200);
        }
    });
    // end

    $('.js-hot-exam').on('click', function (e) {
        e.preventDefault();

        const classId = $(this).data('id');

        axios.get('/get-hotexam', {
            params: { classId }
        })
        .then(function (response) {
            if (response?.data?.html) {
                $('#hotexam-list').html(response.data.html);
            }
        });
    });

    $('body').on('click', '.btn-popup',function() {
        var loginBox = $(this).attr('href');
        $(loginBox).fadeIn(300);

        return false;
    });

    $('body').on('click', '.close-popup', function () {
        $('.box-popup').hide();
    });

    $('.input-form').on('focus', function () {
        $('.list-select').hide();
    });

    $('.list .check').click(function () {
        $(this).toggleClass('checked');
    });

    // auth popup
    $('.switch-login').on('click', function (e) {
        e.preventDefault();
        $('#register-box').hide();
        $('#login-box').show();
        $('#forget-password').hide();
        $('#validate-box').hide();
    })

    $('.switch-register').on('click', function (e) {
        e.preventDefault();
        $('#login-box').hide();
        $('#register-box').show();
        $('#forget-password').hide();
        $('#validate-box').hide();
    })

    $('#switch-forget-password').on('click', function (e) {
        e.preventDefault();
        $('#login-box').hide();
        $('#register-box').hide();
        $('#forget-password').show();
        $('#validate-box').hide();
    })

    $('.switch-validate').on('click', function (e) {
        e.preventDefault();
        $('#login-box').hide();
        $('#register-box').hide();
        $('#forget-password').hide();
        $('#validate-box').show();
    })
    // end

    $('.sidebar-dismiss, .sidebar-overlay').on('click', function() {
        $('.sidenav').removeClass('active');
        $('.sidebar-overlay').fadeOut();
    });

    $('.sidebarCollapse').on('click', function() {
        $('.sidenav a[aria-expanded=true]').attr('aria-expanded', 'false');
        $('.sidenav').addClass('active');
        $('.sidebar-overlay').fadeIn();
    });

    $('.banner-text-close').on('click', function (e) {
        $('.banner-text').slideUp(400);
    });

    if ($('.datetimepicker').length > 0) {
        $('.datetimepicker').datetimepicker();
    }

    //Flash Message
    $('body').on('click', '.flash-close', function (e) {
        var _this = $(e.currentTarget);
        $('.flash-backdrop').hide();
    })

    /*--------------------- Scroll to -------------------*/
    $(document).on('click', '[data-toggle="scroll"]', function(e) {
        if (typeof e != 'undefined' && typeof e.preventDefault == 'function') {
            e.preventDefault();
        }

        var $this = $(this),
            $target = $($this.data('target'));
        if ($target.length > 0) {
            var $point = $target.offset().top - 100,
                $duration = 800;
            if ($this.data('duration')) {
                $duration = $this.data('duration');
            }
            scrollTo($point, $duration);
        }
    });

    function scrollTo($point, $duration) {
        if (typeof $duration == 'undefined') {
            $duration = 800;
        }

        $('body,html').animate({
            scrollTop: $point
        }, $duration);
    }
    /*--------------------- End Scroll to -------------------*/

    // Back to top button
    $(window).scroll(function() {
        if ($(this).scrollTop() > 100) {
            $('.back-to-top').fadeIn('slow');
        } else {
            $('.back-to-top').fadeOut('slow');
        }
    });

    $('.back-to-top').click(function() {
        $('html, body').animate({ scrollTop: 0 }, 1500);

        return false;
    });
    // End Back to top button

    // show more item
    $('.js-load-more').each(function() {
        const $boxLoadMore = $(this);
        const $itemShow = $boxLoadMore.data('item_show') || 9;
        const children = $boxLoadMore.children('li').not('.load-more-footer');
        const countItem = children.length;

        if (countItem <= $itemShow) {
            return;
        }

        const $active = children.filter('.active').first();

        if ($active.length) {
            const idx = children.index($active);
            let start = idx - $itemShow - 1;
            let end   = idx + 1;

            if (start < 0) {
                end   = Math.min(children.length, end - start);
                start = 0;
            }

            if (end > children.length) {
                end   = children.length;
                start = Math.max(0, end - $itemShow);
            }

            const $tmp = children.slice(start, end);
            children.not($tmp).addClass('hide');
            $tmp.removeClass('hide');
        } else {
            $boxLoadMore.children(`li:nth-child(n+${ $itemShow + 1 })`).addClass('hide');
        }

        if (!$boxLoadMore.find('.load-more-footer').length) {
            $boxLoadMore.append('<li class="load-more-footer"><a class="js-load-more-btn load-more-btn">Xem thêm »</a></li>');
        }
    });

    $(document).on('click', '.js-load-more-btn', function(event) {
        event.preventDefault();
        const $btn = $(this);
        const $boxLoadMore = $btn.closest('.js-load-more');
        const $itemShow = $boxLoadMore.data('item_show') || 9;
        const $last = $boxLoadMore.children('li')
            .not('.hide, .load-more-footer')
            .last();

        const $nextItems = $last.nextAll('li.hide')
            .not('.load-more-footer');

        console.log($nextItems, $itemShow);

        $nextItems.slice(0, $itemShow)
            .removeClass('hide');

        if ($nextItems.length <= $itemShow) {
            $btn.parent('.load-more-footer').addClass('hide');
        }
    });

    $('.js-read-more').each(function() {
        const $boxLoadMore = $(this).find('.read-more-body');
        const $itemShow = $boxLoadMore.data('item_show') || 7;
        const children = $boxLoadMore.children();
        const countItem = children.length;

        if (countItem == 0 || countItem <= $itemShow) {
            return;
        }

        children.addClass('hide');
        children.slice(0, $itemShow).removeClass('hide');
        $boxLoadMore.append(`
            <p class="read-more-footer">
                <a class="js-read-more-btn">Xem thêm »</a>
            </p>
        `);
    });

    $(document).on('click', '.js-read-more-btn', function(event) {
        event.preventDefault();
        const $btn = $(this);
        const $parent = $btn.closest('.read-more-body');

        if ($parent.find('.hide').length) {
            $parent.find('.hide').removeClass('hide').addClass('show');
            $btn.text('Thu gọn »');
        } else {
            $parent.find('.show').removeClass('show').addClass('hide');
            $btn.text('Xem thêm »');
        }
    });
    // end show more item

    // active boxvip
    // const $formVip = $('.form-vip_active');
    // const $btnVip = $('.btn-vip_active');

    // $btnVip.click(e => {
    //     e.preventDefault();
    //     $formVip.toggleClass('show');
    // });

    // $(document).click(e => {
    //     if (!$(e.target).closest('.form-vip_active, .btn-vip_active').length) {
    //         $formVip.removeClass('show');
    //     }
    // });

    $(document).on('click', '.js-load-courses', function(event) {
        event.preventDefault();
        const _that = $(this);

        _that.prop('disabled', true).text('Đang tải…');

        try {
            axios.get(_that.attr('data-action')).then(function(response) {
                const { html, hasMore, action } = response.data;

                if (!html) {
                    _that.hide();
                    return;
                }

                _that.closest('.js-list-courses').find('ul').append(html);

                if (hasMore) {
                    _that.attr('data-action', action).text('Xem thêm »').prop('disabled', false);
                } else {
                    _that.hide();
                }
            });
        } catch (err) {
            console.error('Lỗi khi load courses:', err);
            _that.text('Tải lại').prop('disabled', false);
        }
    });

    $(document).on('click', '.js-loadmore', function(event) {
        event.preventDefault();

        const paginationUrl = $(this).attr('href');
        getDataByPagination(paginationUrl);
    });

    function getDataByPagination(paginationUrl){
        axios.get(paginationUrl).then(function(response) {
            const { data } = response;

            if (data.hasMore && data.nextPageUrl) {
                $('.js-loadmore').attr('href', data.nextPageUrl);
            } else {
                $('.js-loadmore').hide();
            }

            // history.pushState({ url: paginationUrl }, '', paginationUrl);
            $('#loadmore-container').append(data.html || '');
        });
    }

    $(document).on('click', '.js-btn-like', function(event) {
        event.preventDefault();
    
        const $btn = $(this);
        const trackingId = $btn.data('tracking-id');
        const type = $btn.data('type');
    
        axios.post("/users/bookmark", {
            type,
            trackingId
        }).then(function(response) {
            const liked = response.data.like;
            if (liked === 1) {
                $btn.html('<i class="fas fa-heart mr-2 heart-color"></i> Đã lưu');
            } else {
                $btn.html(`<i class="far fa-heart mr-2"></i> ${type == "question" ? "Lưu câu hỏi" : "Lưu đề thi"}`);
            }
        })
        .catch(function(error) {
            console.error('Có lỗi xảy ra:', error);
        });
    });

    $(document).on('click', '.js-btn-edown', async function(event) {
        event.preventDefault();

        const $btn = $(this);
        const $parentBtn = $btn.closest('.dropdown').find('.btn-down');

        if (!$parentBtn.length) return;
        if ($parentBtn.prop('disabled')) return;

        const type = $btn.data('type') || 'quiz';
        const title = $parentBtn.data('title');
        const examid = $parentBtn.data('eid');

        if (!examid) return;

        const origHtml = $parentBtn.html();

        $parentBtn
            .prop('disabled', true)
            .attr('aria-busy', 'true')
            .html('<i class="fa fa-spinner fa-spin mr-2"></i> Đang tải về');

        try {
          const res = await axios.get(`/exam/${examid}/download?type=${type}`, { responseType: 'blob' });
          let filename = `${helperFunc.titleToSlug(title)}-${helperFunc.getCurrentDay()}.docx`;
          const disposition = res.headers['content-disposition'];

          if (disposition?.includes('attachment')) {
            const matches = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/.exec(disposition);
            if (matches?.[1]) {
              filename = decodeURIComponent(matches[1].replace(/['"]/g, ''));
            }
          }
          // Tạo URL từ blob và kích hoạt tải xuống
          const url = window.URL.createObjectURL(new Blob([res.data]));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', filename);
          document.body.appendChild(link);
          link.click();
          link.remove();
          window.URL.revokeObjectURL(url);
        } catch (error) {
          let message = 'Đã xảy ra lỗi trong quá trình tạo file! Vui lòng thử lại sau ít phút.';
          if (error.response?.headers['content-type']?.includes('application/json')) {
            try {
              // Đọc nội dung của Blob
              const text = await error.response.data.text();
              const json = JSON.parse(text);
              if (json.message) message = json.message;
            } catch {
              console.error('Lỗi khi phân tích phản hồi JSON lỗi:', e);
            }
          }
          $.notify(
            { title: message, icon: 'fas fa-exclamation-triangle' },
            { type: "danger", delay: 3000, placement: { from: "top", align: "right" } }
          );
        } finally {
            $parentBtn
                .prop('disabled', false)
                .attr('aria-busy', 'false')
                .html(origHtml);
        }
    });

    $('.js-answer').on('click', function() {
        const $wrap = $(this).closest('.answer-check');
        const answer = ($(this).data('answer') || '').toLowerCase();
        let $correctEl = null;
        const qId = $wrap.data('question');

        if (answer == 'y') {
            $correctEl = $(this);
        } else if (answer == 'n') {
            $(this).addClass('option-incorrect')
                .append(`
                    <div class="option-answer-text">
                        <strong>Đáp án của bạn sai</strong>
                    </div>
                `);
            $correctEl = $wrap.find('[data-answer="Y"]');
        } else {
            $(this).addClass('option-selected')
                .append(`
                    <div class="option-answer-text">
                        <strong>Nâng cấp VIP để xem</strong>
                    </div>
                `);
        }

        if ($correctEl) {
            $correctEl.addClass('option-correct')
                .append(`
                    <div class="option-answer-text">
                        <strong>Đáp án chính xác</strong>
                    </div>
                `);
        }

        $wrap.find('.js-answer').addClass('option-disabled');

        const $reasonBox = $(`.reason-${qId}`);

        if ($reasonBox.length && $reasonBox.hasClass('box-hint')) {
            $reasonBox.removeClass('box-hint')
        }
    })

    $(document).on('click', '.toggle-lsidebar', async function(event) {
        $('#lsidebar').toggleClass('active', 1000);
    })

    let testSum = 0;
    let videoSum = 0;

    $('.js-rtree').each(function() {
        const $that = $(this);
        let testTotal = 0;
        let videoTotal = 0;

        $that.find('.js-exam-test, .js-exam-video').each(function() {
            const $el = $(this);
            const num = parseInt($el.data('number') || 1, 10);

            if ($el.hasClass('js-exam-test')) {
                testTotal += num;
            } else if ($el.hasClass('js-exam-video')) {
                videoTotal += num;
            }
        });

        // if (testTotal > 0 || videoTotal > 0) {
        //     const parts = [];
        //     if (testTotal > 0) parts.push(`<span class="fontM">${testTotal}</span> đề thi`);
        //     if (videoTotal > 0) parts.push(`<span class="fontM">${videoTotal}</span> bài giảng`);

        //     const $firstChildren = $that.children().first();

        //     $('<span>', { class: 'tree-stitle' })
        //       .html(`( ${parts.join(' • ')} )`)
        //       .appendTo($firstChildren);
        // }

        testSum += testTotal;
        videoSum += videoTotal;
    });

    if ($('.lsidebar-title').length && (testSum > 0 || videoSum > 0)) {
        const parts = [];

        if (testSum > 0) parts.push(`<span class="fontM">${testSum}</span> đề thi`);
        if (videoSum > 0) parts.push(`<span class="fontM">${videoSum}</span> bài giảng`);

        $('.lsidebar-title').append(`
            <p class="m-0">
                <span class="tree-stitle">( ${parts.join(' • ')} )</span>
            </p>
        `);
    }

    const treeScrollToTop = function () {
        const sidebar = $('.scroll-tree');

        if (sidebar.length) {
            const elem = sidebar.find('li.active');

            if (typeof elem.offset() !== 'undefined') {
                window.scrollTo(0, 0);
                sidebar.animate({
                    scrollTop: (elem.offset().top - (sidebar.attr('offset-top') || '250'))
                }, {
                    duration: 'medium',
                    easing: 'swing'
                });
            }
        }
    };

    $(window).on('load', treeScrollToTop);

    // set href for next-btn and prev-btn
    if ($('.js-post-title').length) {
        const currentCategoryId = $('.js-post-title').data('category_id');
        var leafNodes = new Array();

        if ($('#lsidebar').length) {
            $('#lsidebar').find('a.leaf-nav').each(function() {
                let el = {
                    id: $(this).data('category_id'),
                    href: $(this).attr('href'),
                }
                leafNodes.push(el);
            })

            for (var i = 0; i < leafNodes.length; i++) {
                if (leafNodes[i].id == currentCategoryId) {
                    if (leafNodes[i - 1]) {
                        $('.prev-btn').attr('href', leafNodes[i - 1].href);
                    } else {
                        $('.prev-btn').attr('disabled', true);
                    }

                    if (leafNodes[i + 1]) {
                        $('.next-btn').attr('href', leafNodes[i + 1].href);
                    } else {
                        $('.next-btn').attr('disabled', true);
                    }

                    break;
                }
            }

            $('.prev-btn').show();
            $('.next-btn').show();
        }
    }
    // end

    const $nav_menu = $('.nav-menu-list');

    if ($nav_menu.length) {
        const $btn     = $nav_menu.find('button');
        const $vlinks  = $nav_menu.find('.links');
        const $hlinks  = $nav_menu.find('.hidden-links');
        const breakWidths = [];
        let totalItems;

        $vlinks.children().each(function() {
            const w = this.getBoundingClientRect().width;
            breakWidths.push((breakWidths.slice(-1)[0] || 0) + w);
        });

        totalItems = breakWidths.length;

        function adjustMenu() {
            const available = $vlinks.width() - 100; // 100 là ước tính rrooj rộng của $btn
            let visibleCount = $vlinks.children().length;

            while (visibleCount > 0 && breakWidths[visibleCount - 1] > available) {
                $vlinks.children().last().prependTo($hlinks);
                visibleCount--;
            }

            while (visibleCount < totalItems && breakWidths[visibleCount] <= available) {
                $hlinks.children().first().appendTo($vlinks);
                visibleCount++;
            }

            $btn
                .attr('count', totalItems - visibleCount)
                .toggleClass('hidden', visibleCount === totalItems);
        }

        let resizeTimer;

        $(window).on('resize', function() {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(adjustMenu, 100);
        });

        $btn.on('click', () => {
            $hlinks.toggleClass('hidden');
        });

        adjustMenu();
    };
});

// handle limit content
(function() {
  function setupBoxLimit(el) {
    const style = getComputedStyle(el);
    const maxH  = parseFloat(style.getPropertyValue('--max-h') || '0');

    if (el.scrollHeight <= maxH) return;

    el.classList.add('has-limit');
    const btn = document.createElement('button');
    btn.className = 'limit-content-btn';
    btn.type = 'button';
    btn.setAttribute('aria-expanded', 'false');
    btn.textContent = 'Xem thêm »';

    el.insertAdjacentElement('afterend', btn);
  }

  // Khởi tạo khi browser rảnh hoặc load xong
  function initReadMore() {
    document.querySelectorAll('.limit-content').forEach(setupBoxLimit);
  }

  if ('requestIdleCallback' in window) {
    requestIdleCallback(initReadMore, { timeout: 200 });
  } else {
    window.addEventListener('load', initReadMore);
  }

  document.body.addEventListener('click', function(e) {
    const btn = e.target.closest('.limit-content-btn');
    if (!btn) return;

    const el = btn.previousElementSibling;
    if (!el || !el.classList.contains('limit-content')) return;

    const expanded = el.classList.toggle('expanded');
    btn.setAttribute('aria-expanded', expanded);
    btn.textContent = expanded ? '« Thu gọn' : 'Xem thêm »';
  });
})();
// end

// document.addEventListener('DOMContentLoaded', function () {
//     const updateHeights = () => {
//         const quizItems = document.querySelectorAll('.quiz-answer');

//         quizItems.forEach(item => {
//             const left = item.querySelector('.quiz-answer-left');
//             const right = item.querySelector('.quiz-answer-right');

//             if (left && right) {
//                 right.style.maxHeight = left.offsetHeight + 'px';
//                 right.style.overflowY = 'auto';
//             }
//         });
//     };

//     updateHeights();

//     window.addEventListener('resize', () => {
//         requestAnimationFrame(updateHeights);
//     });
// });

// function handleSaveURLs() {
//     // urls: [prevURL, CurrUrl]
//     let urls = localStorage.getItem('referrer-urls');
//     urls = urls ? JSON.parse(urls) : [window.location.href];
//     let poped = urls.pop();

//     if (poped == window.location.href && helperFunc.isMobile()) {
//         let timer = setInterval(modalDownloadApp, 5000);

//         function modalDownloadApp() {
//             $('#modal_download_app').css('display', 'block').modal('show');

//             clearInterval(timer);
//         }
//     }

//     localStorage.setItem('referrer-urls', JSON.stringify([poped, window.location.href]));

//     return;
// }

// window.onload = function() {
//     handleSaveURLs();
// }

document.addEventListener("DOMContentLoaded", function() {
    const menuWraper = document.getElementById("tg-navigationholder");
    const menuContainer = document.getElementById("scroll-menu");
    const menuLeftBtn = document.getElementById("lscroll-menu");
    const menuRightBtn = document.getElementById("rscroll-menu");
    const navCustomMb = document.getElementById("nav-custom-mb");

    if (!menuContainer) return;

    const rawMicon = document.querySelector('.main-page')?.dataset.micon;

    if (rawMicon) {
        const micon = rawMicon.replace(/\+$/, '');
        const meunuItem = menuContainer.querySelector(`li[menu-icon="${micon}"]`);
        meunuItem?.classList.add('current-menu-item');
    };

    function updateScrollMenuWidth() {
        if (!menuContainer || !navCustomMb || !menuWraper) return;
        const maxWidth = menuWraper.offsetWidth - navCustomMb.offsetWidth - 50;

        menuContainer.style.maxWidth = `${maxWidth}px`;
    }

    updateScrollMenuWidth();

    window.addEventListener("resize", updateScrollMenuWidth);

    let isDragging = false;
    let fromScrollPos = -1;
    let gotoTut = 1;

    // --- Xử lý kéo (drag scrolling) ---
    menuContainer.addEventListener("mousedown", (e) => {
        e.preventDefault();
        fromScrollPos = e.clientX; // Lấy vị trí X ban đầu của chuột
        isDragging = true; // Đánh dấu đang kéo
    });

    menuContainer.addEventListener("mousemove", (e) => {
        if (!isDragging) return;
        const currentPos = e.clientX;
        if (currentPos === fromScrollPos) return;
        e.preventDefault();
        if (e.buttons === 0) return;
        const delta = Math.abs(currentPos - fromScrollPos);
        gotoTut = 0; // Đánh dấu đã kéo để không kích hoạt link
        menuContainer.scrollLeft += currentPos < fromScrollPos ? delta : -delta;
        updateScrollButtons();
        fromScrollPos = currentPos;
    });

    menuContainer.addEventListener("mouseup", (e) => {
        e.preventDefault();
        isDragging = false;
        fromScrollPos = -1;
    });

    // Ngăn không cho link hoạt động khi kéo
    menuContainer.addEventListener("click", (e) => {
        if (gotoTut === 0) {
            e.preventDefault();
            gotoTut = 1;
        }
    });

    function scrollmenow(n) {
        menuContainer.scrollBy({
            left: n === 1 ? 100 : -100,
            behavior: 'smooth'
        });
        setTimeout(updateScrollButtons, 200);
    }

    menuLeftBtn.addEventListener("click", (e) => {
        e.preventDefault();
        scrollmenow(-1);
    });

    menuRightBtn.addEventListener("click", (e) => {
        e.preventDefault();
        scrollmenow(1);
    });

    function updateScrollButtons() {
        const currentScrollLeft = menuContainer.scrollLeft;
        menuLeftBtn.style.display = currentScrollLeft < 1 ? "none" : "flex";
        // Ẩn nút phải nếu đã cuộn hết chiều rộng của menuContainer
        menuRightBtn.style.display =
            currentScrollLeft + menuContainer.clientWidth >= menuContainer.scrollWidth ?
            "none" :
            "flex";
    }

    window.addEventListener("load", updateScrollButtons);

    const menuActiveLink = menuContainer.querySelector("li a.active");

    if (menuActiveLink) {
        menuActiveLink.scrollIntoView({ behavior: "auto", inline: "center", block: "nearest" });

        setTimeout(updateScrollButtons, 200);
    }
});

require('./modules/auth-validator');
require('./modules/vj-slide');
require('./modules/app-install');
require('./modules/book-picker');
require('./modules/comments-group');
