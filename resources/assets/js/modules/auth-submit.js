export function authSubmit($form, reload) {
    $form.find('.has-error').removeClass('has-error');
    $form.find('.help-block').text('');

    const $btnSubmit = $form.find('.js-btn-auth');

    $btnSubmit.prop('disabled', true); 
    $btnSubmit.find('i').show();
    $btnSubmit.find('span').hide();

    var formData = new FormData($form[0]);

    axios({
        method: $form.attr('method'),
        url: $form.attr('action'),
        data: formData,
    }).then(function(res) {
        const redirectTo = res.data.redirectTo ? res.data.redirectTo : $form.data('redirect');

        if (redirectTo) {
            window.location.href = redirectTo;
            return;
        }

        if (reload) {
            window.location.reload();
        } else {
            const html = `
                <div class="success-msg" style="display: block; text-align: center; font-size: 14px; font-weight: 700; font-stretch: normal; font-style: normal; line-height: 1.71; letter-spacing: .34px; color: red; margin-top: 70px; padding-bottom: 120px;">
                    ${$form.data('msg')}
                </div>
            `;

            $form.html(html);
            return;
        }
    }).catch(function(error) {
        $btnSubmit.prop('disabled', false); 
        $btnSubmit.find('i').hide();
        $btnSubmit.find('span').show();

        if (error.response && (error.response.status === 422 || error.response.status === 423)) {
            const { errors } = error.response.data;

            for (let [key, value] of Object.entries(errors)) {
                // array validate, e.g. places.0, places.1...
                let match = key.match(/^(\w+)\.\d+$/);

                if (match && match.length) {
                    key = match[1] + '[]';
                }

                const $inputGroup = $form
                    .find(`[name="${key}"]`)
                    .parents('.form-group')
                    .first();

                $inputGroup.addClass('has-error');
                $inputGroup.find('.help-block').text(value[0]);
            }
        } else if (error.response && error.response.data && error.response.data.message) {
            const $text = error.response.status === 419 ?
                'Phiên làm việc của bạn đã hết hạn. Vui lòng tải lại trang!' :
                error.response.data.message;
            const $firstInputGroup = $form.find('.form-group').first();

            $firstInputGroup.addClass('has-error');
            $firstInputGroup.find('.help-block').text($text);
        } else {
            window.location.reload();
        }
    }).finally(function() {

    });
}
