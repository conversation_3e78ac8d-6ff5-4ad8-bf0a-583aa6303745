const helperFunc = {
    getUrlVars: function (url = null) { // Read a page's GET URL variables and return them as an associative array.
        var vars = [], hash;
        var url = url ? url : window.location.href;

        if (location.search.length > 0) {
            var hashes = url.slice(url.indexOf('?') + 1).split('&');

            for(var i = 0; i < hashes.length; i++) {
                hash = hashes[i].split('=');
                vars.push(hash[0]);
                vars[hash[0]] = hash[1];
            }
        }

        return vars;
    },
    titleToSlug: function (str = '', limit = 70) { // convert str to slug
        str = str.replace(/^\s+|\s+$/g, ''); // trim
        str = str.toLowerCase();
        // remove accents, swap ñ for n, etc
        var from = "áàảạãăắằẳẵặâấầẩẫậäéèẻẽẹêếềểễệëíìỉĩịïóòỏõọôốồổỗộơớờởỡợöúùủũụưứừửữựüûýỳỷỹỵđñç·/_,:;";
        var to   = "aaaaaaaaaaaaaaaaaaeeeeeeeeeeeeiiiiiioooooooooooooooooouuuuuuuuuuuuuyyyyydnc------";

        for (var i=0, l=from.length ; i<l ; i++) {
            str = str.replace(new RegExp(from.charAt(i), 'g'), to.charAt(i));
        }

        str = str.replace(/[^a-z0-9 -]/g, '') // remove invalid chars
            .replace(/\s+/g, '-') // collapse whitespace and replace by -
            .replace(/-+/g, '-') // collapse dashes
            .replace(/^-/, '') // remove hyphens from first str
            .replace(/-$/, ''); // remove hyphens from last str

        return limit < 0 ? str : str.substring(0, limit);
    },
    escapeRegExp: function(str) {
        return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // $& means the whole matched str
    },
    formatTime: function(seconds) {
        const h = Math.floor(seconds / 3600);
        const m = Math.floor((seconds % 3600) / 60);
        const s = Math.round(seconds % 60);

        return [
            h,
            m > 9 ? m : (h ? '0' + m : m || '0'),
            s > 9 ? s : '0' + s
        ].filter(Boolean).join(':');
    },
    isMobile: function() {
        return /Mobi|Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Touch|Tablet|Phone/i.test(navigator.userAgent);
    },
    getMobileOperatingSystem: function() {
        var userAgent = navigator.userAgent || navigator.vendor || window.opera;

        if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
            return 'iOS';
        }

        if (/android/i.test(userAgent)) {
            return 'Android';
        }

        return 'unknown';
    },
    handleHtml: function(html) {
        var root = document.createElement('div'),
          spanItems = root.getElementsByTagName('span');

        root.innerHTML = `<div>${html}</div>`;

        Array.prototype.forEach.call(spanItems, function (elem, index) {
            elem.removeAttribute('class');
            elem.removeAttribute('id');
            elem.removeAttribute('tabindex');
            elem.removeAttribute('presentation');
            elem.removeAttribute('role');

            if (elem.hasAttribute('data-mathml')) {
                elem.innerHTML = elem.getAttribute('data-mathml');
                elem.removeAttribute('data-mathml');
                elem.removeAttribute('style');
            }
        });

        return root.innerHTML;
    },
    getCurrentDay: function(separator = '_') {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        return [year, month, day].join(separator);
    }
}

export default helperFunc;
