export function initImageUploadInput() {
    $('.js-upload-image__btn').on('click', function() {
        const $wrapper = $(this).parent('.js-upload-image');
        const $inputFile = $wrapper.find('.js-input-choose-image');

        $inputFile.click();
    })

    $('.js-input-choose-image').on('change', function() {
        const $wrapper = $(this).parent('.js-upload-image');
        const inputFile = this;

        if (inputFile.files.length) {
            uploadImage(inputFile.files[0], $wrapper);
        }
    })
}

function uploadImage(file, $wrapper) {
    if (file) {
        const $helpBlock = $wrapper.find('.js-help-block');
        const $gridImages = $wrapper.find('.js-images');
        const $typeImages = $wrapper.data('type');
        const $id = $wrapper.data('id');

        const formData = new FormData();

        formData.append('image', file);
        formData.append('id', $id);

        $helpBlock.text('');

        axios.post('/upload/image-' + $typeImages, formData).then(function(response) {
            const { data } = response;

            if (data.uploaded) {
                $gridImages.html(renderImage(data));
            } else {
                $helpBlock.text(data.error.message);
            }
        })
        .catch(function(error) {
            console.error(error);
        });
    }
}

function renderImage(image) {
    return `<img src="${image.url}" data-path="${image.path}" class="img-thumbnail img-fluid">`;
}
