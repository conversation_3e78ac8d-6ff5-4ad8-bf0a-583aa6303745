import helperFunc from "./helpers";

if (window.EDITOR_DOMAIN) {
    var js = document.createElement("script");
    js.type = "text/javascript";
    js.src = window.EDITOR_DOMAIN + "tinymce4/plugins/tiny_mce_wiris/integration/WIRISplugins.js?viewer=image";

    document.head.appendChild(js);
}

const URL_IMAGE_UPLOAD = process.env.MIX_APP_URL + 'services/course/curriculum/upload-image';
const URL_MEDIA_UPLOAD = process.env.MIX_APP_URL + 'services/course/curriculum/upload-media';

tinymce.addI18n('vi',{
    "Redo": "L\u00e0m l\u1ea1i",
    "Undo": "H\u1ee7y thao t\u00e1c",
    "Cut": "C\u1eaft",
    "Copy": "Sao ch\u00e9p",
    "Paste": "D\u00e1n",
    "Select all": "Ch\u1ecdn t\u1ea5t c\u1ea3",
    "New document": "T\u1ea1o t\u00e0i li\u1ec7u m\u1edbi",
    "Ok": "\u0110\u1ed3ng \u00dd",
    "Cancel": "Hu\u1ef7 B\u1ecf",
    "Visual aids": "M\u1edf khung so\u1ea1n th\u1ea3o",
    "Bold": "In \u0111\u1eadm",
    "Italic": "In nghi\u00eang",
    "Underline": "G\u1ea1ch d\u01b0\u1edbi",
    "Strikethrough": "G\u1ea1ch ngang",
    "Superscript": "K\u00fd t\u1ef1 m\u0169",
    "Subscript": "K\u00fd t\u1ef1 th\u1ea5p",
    "Clear formatting": "L\u01b0\u1ee3c b\u1ecf ph\u1ea7n hi\u1ec7u \u1ee9ng",
    "Align left": "Canh tr\u00e1i",
    "Align center": "Canh gi\u1eefa",
    "Align right": "Canh ph\u1ea3i",
    "Justify": "Canh \u0111\u1ec1u hai b\u00ean",
    "Bullet list": "Danh s\u00e1ch d\u1ea1ng bi\u1ec3u t\u01b0\u1ee3ng",
    "Numbered list": "Danh s\u00e1ch d\u1ea1ng s\u1ed1",
    "Decrease indent": "Th\u1ee5t l\u00f9i d\u00f2ng",
    "Increase indent": "T\u0103ng kho\u1ea3ng c\u00e1ch d\u00f2ng",
    "Close": "\u0110\u00f3ng L\u1ea1i",
    "Formats": "\u0110\u1ecbnh d\u1ea1ng",
    "Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.": "Tr\u00ecnh duy\u1ec7t c\u1ee7a b\u1ea1n kh\u00f4ng h\u1ed7 tr\u1ee3 truy c\u1eadp truy c\u1eadp b\u1ed9 nh\u1edb \u1ea3o, vui l\u00f2ng s\u1eed d\u1ee5ng c\u00e1c t\u1ed5 h\u1ee3p ph\u00edm Ctrl + X, C, V.",
    "Headers": "\u0110\u1ea7u trang",
    "Header 1": "Ti\u00eau \u0111\u1ec1 1",
    "Header 2": "Ti\u00eau \u0111\u1ec1 2",
    "Header 3": "Ti\u00eau \u0111\u1ec1 3",
    "Header 4": "Ti\u00eau \u0111\u1ec1 4",
    "Header 5": "Ti\u00eau \u0111\u1ec1 5",
    "Header 6": "Ti\u00eau \u0111\u1ec1 6",
    "Headings": "Ph\u1ea7n \u0111\u1ea7u",
    "Heading 1": "H1",
    "Heading 2": "H2",
    "Heading 3": "H3",
    "Heading 4": "H4",
    "Heading 5": "H5",
    "Heading 6": "G6",
    "Preformatted": "\u0110\u1ecbnh d\u1ea1ng s\u1eb5n",
    "Div": "Khung",
    "Pre": "\u0110\u1ecbnh d\u1ea1ng",
    "Code": "M\u00e3",
    "Paragraph": "\u0110o\u1ea1n v\u0103n",
    "Blockquote": "\u0110o\u1ea1n Tr\u00edch D\u1eabn",
    "Inline": "C\u00f9ng d\u00f2ng",
    "Blocks": "Bao",
    "Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.": "Ch\u1ee9c n\u0103ng D\u00e1n \u0111ang trong tr\u1ea1ng th\u00e1i v\u0103n b\u1ea3n \u0111\u01a1n gi\u1ea3n. N\u1ed9i dung s\u1ebd \u0111\u01b0\u1ee3c d\u00e1n d\u01b0\u1edbi d\u1ea1ng v\u0103n b\u1ea3n thu\u1ea7n, kh\u00f4ng c\u00f3 \u0111\u1ecbnh d\u1ea1ng.",
    "Font Family": "Ki\u1ec3u ch\u1eef",
    "Font Sizes": "C\u1ee1 ch\u1eef",
    "Class": "L\u1edbp",
    "Browse for an image": "Ch\u00e8n m\u1ed9t h\u00ecnh \u1ea3nh",
    "OR": "HO\u1eb6C",
    "Drop an image here": "Th\u1ea3 h\u00ecnh \u1ea3nh v\u00e0o \u0111\u00e2y",
    "Upload": "T\u1ea3i l\u00ean",
    "Block": "Kh\u1ed1i",
    "Align": "Canh l\u1ec1",
    "Default": "M\u1eb7c \u0111\u1ecbnh",
    "Circle": "H\u00ecnh tr\u00f2n",
    "Disc": "H\u00ecnh tr\u00f2n  d\u1ea1ng m\u1ecfng",
    "Square": "\u00d4 vu\u00f4ng",
    "Lower Alpha": "K\u00fd t\u1ef1 th\u01b0\u1eddng",
    "Lower Greek": "S\u1ed1 hy l\u1ea1p th\u01b0\u1eddng",
    "Lower Roman": "S\u1ed1 la m\u00e3 th\u01b0\u1eddng",
    "Upper Alpha": "K\u00fd t\u1ef1 hoa",
    "Upper Roman": "S\u1ed1 la m\u00e3 hoa",
    "Anchor": "Neo",
    "Name": "T\u00ean",
    "Id": "Id",
    "Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.": "Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.",
    "You have unsaved changes are you sure you want to navigate away?": "B\u1ea1n ch\u01b0a l\u01b0u thay \u0111\u1ed5i b\u1ea1n c\u00f3 ch\u1eafc b\u1ea1n mu\u1ed1n di chuy\u1ec3n \u0111i?",
    "Restore last draft": "Kh\u00f4i ph\u1ee5c b\u1ea3n g\u1ea7n nh\u1ea5t",
    "Special character": "K\u00fd t\u1ef1 \u0111\u1eb7c bi\u1ec7t",
    "Source code": "M\u00e3 ngu\u1ed3n",
    "Insert\/Edit code sample": "Ch\u00e8n\/S\u1eeda m\u00e3 m\u1eabu",
    "Language": "Ng\u00f4n ng\u1eef",
    "Code sample": "M\u00e3 m\u1eabu",
    "Color": "M\u00e0u s\u1eafc",
    "R": "M\u00e0u \u0111\u1ecf",
    "G": "M\u00e0u xanh l\u00e1 c\u00e2y",
    "B": "M\u00e0u xanh da tr\u1eddi",
    "Left to right": "Tr\u00e1i sang ph\u1ea3i",
    "Right to left": "Ph\u1ea3i sang tr\u00e1i",
    "Emoticons": "Bi\u1ec3u t\u01b0\u1ee3ng c\u1ea3m x\u00fac",
    "Document properties": "Thu\u1ed9c t\u00ednh t\u00e0i li\u1ec7u",
    "Title": "Ti\u00eau \u0111\u1ec1",
    "Keywords": "T\u1eeb kh\u00f3a",
    "Description": "M\u00f4 t\u1ea3",
    "Robots": "Robots",
    "Author": "T\u00e1c gi\u1ea3",
    "Encoding": "M\u00e3 h\u00f3a",
    "Fullscreen": "To\u00e0n m\u00e0n h\u00ecnh",
    "Action": "H\u00e0nh \u0111\u1ed9ng",
    "Shortcut": "L\u1ed1i t\u1eaft",
    "Help": "Tr\u1ee3 gi\u00fap",
    "Address": "\u0110\u1ecba ch\u1ec9",
    "Focus to menubar": "Focus to menubar",
    "Focus to toolbar": "Focus to toolbar",
    "Focus to element path": "Focus to element path",
    "Focus to contextual toolbar": "Focus to contextual toolbar",
    "Insert link (if link plugin activated)": "Ch\u00e8n \u0111\u01b0\u1eddng d\u1eabn",
    "Save (if save plugin activated)": "L\u01b0u",
    "Find (if searchreplace plugin activated)": "T\u00ecm ki\u1ebfm",
    "Plugins installed ({0}):": "Plugins installed ({0}):",
    "Premium plugins:": "Premium plugins:",
    "Learn more...": "Learn more...",
    "You are using {0}": "You are using {0}",
    "Plugins": "Plugins",
    "Handy Shortcuts": "Handy Shortcuts",
    "Horizontal line": "K\u1ebb ngang",
    "Insert\/edit image": "Ch\u00e8n\/s\u1eeda \u1ea3nh",
    "Image description": "M\u00f4 t\u1ea3 \u1ea3nh",
    "Source": "Ngu\u1ed3n",
    "Dimensions": "K\u00edch th\u01b0\u1edbc",
    "Constrain proportions": "T\u1ef7 l\u1ec7 h\u1ea1n ch\u1ebf",
    "General": "Chung",
    "Advanced": "N\u00e2ng cao",
    "Style": "Ki\u1ec3u",
    "Vertical space": "N\u1eb1m d\u1ecdc",
    "Horizontal space": "N\u1eb1m ngang",
    "Border": "\u0110\u01b0\u1eddng vi\u1ec1n",
    "Insert image": "Ch\u00e8n \u1ea3nh",
    "Image": "Image",
    "Image list": "Image list",
    "Rotate counterclockwise": "Xoay tr\u00e1i",
    "Rotate clockwise": "Xoay ph\u1ea3i",
    "Flip vertically": "L\u1eadt d\u1ecdc",
    "Flip horizontally": "L\u1eadt ngang",
    "Edit image": "Ch\u1ec9nh s\u1eeda \u1ea3nh",
    "Image options": "T\u00f9y ch\u1ecdn \u1ea3nh",
    "Zoom in": "Thu nh\u1ecf",
    "Zoom out": "Ph\u00f3ng to",
    "Crop": "C\u1eaft \u1ea3nh",
    "Resize": "Thay \u0111\u1ed5i k\u00edch th\u01b0\u1edbc",
    "Orientation": "\u0110\u1ecbnh h\u01b0\u1edbng",
    "Brightness": "\u0110\u1ed9 s\u00e1ng",
    "Sharpen": "L\u00e0m s\u1eafc n\u00e9t",
    "Contrast": "\u0110\u1ed9 t\u01b0\u01a1ng ph\u1ea3n",
    "Color levels": "M\u1ee9c \u0111\u1ed9 m\u00e0u",
    "Gamma": "M\u00e0u Gamma",
    "Invert": "\u0110\u1ea3o ng\u01b0\u1ee3c",
    "Apply": "\u00c1p d\u1ee5ng",
    "Back": "Quay l\u1ea1i",
    "Insert date\/time": "Ch\u00e8n ng\u00e0y\/th\u00e1ng",
    "Date\/time": "Date\/time",
    "Insert link": "Ch\u00e8n li\u00ean k\u1ebft",
    "Insert\/edit link": "Ch\u00e8n\/s\u1eeda li\u00ean k\u1ebft",
    "Text to display": "N\u1ed9i dung hi\u1ec3n th\u1ecb",
    "Url": "Url",
    "Target": "\u0110\u00edch",
    "None": "Kh\u00f4ng",
    "New window": "C\u1eeda s\u1ed5 m\u1edbi",
    "Remove link": "B\u1ecf li\u00ean k\u1ebft",
    "Anchors": "Neo",
    "Link": "Link",
    "Paste or type a link": "Paste or type a link",
    "The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?": "\u0110\u1ecba ch\u1ec9 URL b\u1ea1n v\u1eeba nh\u1eadp gi\u1ed1ng nh\u01b0 m\u1ed9t \u0111\u1ecba ch\u1ec9 email. B\u1ea1n c\u00f3 mu\u1ed1n th\u00eam ti\u1ec1n t\u1ed1 mailto: kh\u00f4ng?",
    "The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?": "\u0110\u1ecba ch\u1ec9 URL b\u1ea1n v\u1eeba nh\u1eadp gi\u1ed1ng nh\u01b0 m\u1ed9t li\u00ean k\u1ebft. B\u1ea1n c\u00f3 mu\u1ed1n th\u00eam ti\u1ec1n t\u1ed1 http:\/\/ kh\u00f4ng?",
    "Link list": "Link list",
    "Insert video": "Ch\u00e8n video",
    "Insert\/edit video": "Ch\u00e8n\/s\u1eeda video",
    "Insert\/edit media": "Insert\/edit media",
    "Alternative source": "Ngu\u1ed3n thay th\u1ebf",
    "Poster": "Ng\u01b0\u1eddi g\u1eedi",
    "Paste your embed code below:": "D\u00e1n m\u00e3 nh\u00fang c\u1ee7a b\u1ea1n d\u01b0\u1edbi \u0111\u00e2y:",
    "Embed": "Nh\u00fang",
    "Media": "Media",
    "Nonbreaking space": "Kh\u00f4ng xu\u1ed1ng h\u00e0ng",
    "Page break": "Ng\u1eaft trang",
    "Paste as text": "D\u00e1n \u0111o\u1ea1n v\u0103n b\u1ea3n",
    "Preview": "Xem th\u1eed",
    "Print": "In",
    "Save": "L\u01b0u",
    "Find": "T\u00ecm ki\u1ebfm",
    "Replace with": "Thay th\u1ebf b\u1edfi",
    "Replace": "Thay th\u1ebf",
    "Replace all": "Thay t\u1ea5t c\u1ea3",
    "Prev": "Tr\u01b0\u1edbc",
    "Next": "K\u1ebf ti\u1ebfp",
    "Find and replace": "T\u00ecm v\u00e0 thay th\u1ebf",
    "Could not find the specified string.": "Kh\u00f4ng t\u00ecm th\u1ea5y chu\u1ed7i qui \u0111\u1ecbnh",
    "Match case": "Tr\u01b0\u1eddng h\u1ee3p xem",
    "Whole words": "To\u00e0n b\u1ed9 t\u1eeb",
    "Spellcheck": "Ki\u1ec3m tra ch\u00ednh t\u1ea3",
    "Ignore": "B\u1ecf qua",
    "Ignore all": "B\u1ecf qua t\u1ea5t",
    "Finish": "Ho\u00e0n t\u1ea5t",
    "Add to Dictionary": "Th\u00eam v\u00e0o t\u1eeb \u0111i\u1ec3n",
    "Insert table": "Th\u00eam b\u1ea3ng",
    "Table properties": "Thu\u1ed9c t\u00ednh b\u1ea3ng",
    "Delete table": "Xo\u00e1 b\u1ea3ng",
    "Cell": "\u00d4",
    "Row": "D\u00f2ng",
    "Column": "C\u1ed9t",
    "Cell properties": "Thu\u1ed9c t\u00ednh \u00f4",
    "Merge cells": "Tr\u1ed9n \u00f4",
    "Split cell": "Chia c\u1eaft \u00f4",
    "Insert row before": "Th\u00eam d\u00f2ng ph\u00eda tr\u00ean",
    "Insert row after": "Th\u00eam d\u00f2ng ph\u00eda d\u01b0\u1edbi",
    "Delete row": "Xo\u00e1 d\u00f2ng",
    "Row properties": "Thu\u1ed9c t\u00ednh d\u00f2ng",
    "Cut row": "C\u1eaft d\u00f2ng",
    "Copy row": "Sao ch\u00e9p d\u00f2ng",
    "Paste row before": "D\u00e1n v\u00e0o ph\u00eda tr\u01b0\u1edbc, tr\u00ean",
    "Paste row after": "D\u00e1n v\u00e0o ph\u00eda sau, d\u01b0\u1edbi",
    "Insert column before": "Th\u00eam c\u1ed9t b\u00ean tr\u00e1i",
    "Insert column after": "Th\u00eam c\u1ed9t b\u00ean ph\u1ea3i",
    "Delete column": "Xo\u00e1 c\u1ed9t",
    "Cols": "C\u1ed9t",
    "Rows": "D\u00f2ng",
    "Width": "\u0110\u1ed9 R\u1ed9ng",
    "Height": "\u0110\u1ed9 Cao",
    "Cell spacing": "Kho\u1ea3ng c\u00e1ch \u00f4",
    "Cell padding": "Kho\u1ea3ng c\u00e1ch trong \u00f4",
    "Caption": "Ti\u00eau \u0111\u1ec1",
    "Left": "Tr\u00e1i",
    "Center": "Gi\u1eefa",
    "Right": "Ph\u1ea3i",
    "Cell type": "Lo\u1ea1i \u00f4",
    "Scope": "Quy\u1ec1n",
    "Alignment": "Canh ch\u1ec9nh",
    "H Align": "L\u1ec1 ngang",
    "V Align": "L\u1ec1 d\u1ecdc",
    "Top": "Tr\u00ean",
    "Middle": "Kho\u1ea3ng gi\u1eefa",
    "Bottom": "D\u01b0\u1edbi",
    "Header cell": "Ti\u00eau \u0111\u1ec1 \u00f4",
    "Row group": "Gom nh\u00f3m d\u00f2ng",
    "Column group": "Gom nh\u00f3m c\u1ed9t",
    "Row type": "Th\u1ec3 lo\u1ea1i d\u00f2ng",
    "Header": "Ti\u00eau \u0111\u1ec1",
    "Body": "N\u1ed9i dung",
    "Footer": "Ch\u00e2n",
    "Border color": "M\u00e0u vi\u1ec1n",
    "Insert template": "Th\u00eam m\u1eabu",
    "Templates": "M\u1eabu",
    "Template": "Template",
    "Text color": "M\u00e0u v\u0103n b\u1ea3n",
    "Background color": "M\u00e0u n\u1ec1n",
    "Custom...": "Tu\u1ef3 ch\u1ec9nh...",
    "Custom color": "Tu\u1ef3 ch\u1ec9nh m\u00e0u",
    "No color": "Kh\u00f4ng c\u00f3 m\u00e0u",
    "Table of Contents": "Table of Contents",
    "Show blocks": "Hi\u1ec3n th\u1ecb kh\u1ed1i",
    "Show invisible characters": "Hi\u1ec3n th\u1ecb k\u00fd t\u1ef1 \u1ea9n",
    "Words: {0}": "T\u1eeb: {0}",
    "{0} words": "{0} words",
    "File": "T\u1eadp tin",
    "Edit": "S\u1eeda",
    "Insert": "Ch\u00e8n",
    "View": "Xem",
    "Format": "\u0110\u1ecbnh d\u1ea1ng",
    "Table": "B\u1ea3ng",
    "Tools": "C\u00f4ng c\u1ee5",
    "Powered by {0}": "Powered by {0}",
    "Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help": "Rich Text Area. B\u1ea5m ALT-F9 m\u1edf menu. B\u1ea5m ALT-F10 m\u1edf thanh c\u00f4ng c\u1ee5. B\u1ea5m ALT-0 m\u1edf tr\u1ee3 gi\u00fap",
    "Line Height": "Dãn dòng"
});

// Creating TinyMCE demo instance.
createEditorBaseInstance({});
createEditorInputContentInstance({});

/**
 * Creates a TinyMCE instance on "example" div.
 * @param {String} wiriseditorparameters JSON containing MathType Web parameters.
 */
export function createEditorBaseInstance(wiriseditorparameters) {
    var dir = 'ltr'; // văn bản từ trái sang phải, rtl cho văn bản từ phải sang trái.

    if (typeof wiriseditorparameters == 'undefined') {
        wiriseditorparameters = {};
    }

    const tinyMCEConfiguration = {
        min_height: 200,
        auto_focus: true,
        directionality: dir,
        menubar: false,
        branding: false,
        statusbar: false,
        convert_urls: false,
        paste_as_text: true,
        paste_enable_default_filters: false,
        paste_filter_drop: false,
        contextmenu: false,
        plugins:  [
            'emoticons', 'paste', 'lists', 'table', 'textcolor', 'colorpicker', 'link', 'image', 'media', 'imagetools', 'autoresize', 'tiny_mce_wiris'
        ],
        valid_elements: '@[class],p[style],h3,h4,h5,h6,strong/b,i/em,br,table[style|border],tbody,thead,tr,td[style],ul,ol,li,img[src]',
        toolbar: 'bold italic underline | tiny_mce_wiris_formulaEditor tiny_mce_wiris_formulaEditorChemistry | table | fontselect fontsizeselect forecolor backcolor | alignleft aligncenter alignright alignjustify | numlist bullist | link image media | emoticons removeformat',
        init_instance_callback: "updateFunctionTimeOut",
        setup: function(ed) {
            ed.on('init', function() {
                this.getDoc().body.style.fontSize = '18px';
                // this.getDoc().body.style.fontFamily = 'Arial, "Helvetica Neue", Helvetica, sans-serif';
                this.getDoc().body.style.fontFamily = '"Times New Roman", Times, serif';
            });
        },
        paste_preprocess: function(plugin, args) {
            args.content = helperFunc.handleHtml(args.content);
        },
        content_style: `
            table {
                margin-left: auto;
                margin-right: auto;
                max-width: 800px;
                width: 100%;
            }
        `
    };

    document.querySelectorAll('.editor-input-base').forEach(el => {
        if (!el.id) el.id = 'mce-' + Math.random().toString(36).slice(2);

        if (tinymce?.get(el.id)) return;

        tinymce.init({
            ...tinyMCEConfiguration,
            target: el,
        });
    });
}

export function createEditorInputContentInstance(wiriseditorparameters) {
    var dir = 'ltr'; // văn bản từ trái sang phải, rtl cho văn bản từ phải sang trái.

    if (typeof wiriseditorparameters == 'undefined') {
        wiriseditorparameters = {};
    }

    const tinyMCEConfiguration = {
        min_height: 300,
        auto_focus: true,
        directionality: dir,
        menubar: false,
        branding: false,
        statusbar: false,
        convert_urls: false,
        paste_as_text: false,
        paste_data_images: false,
        paste_convert_word_fake_lists: false,
        paste_filter_drop: false,
        paste_remove_styles_if_webkit: false,
        paste_enable_default_filters: false,
        relative_urls : false,
        remove_script_host: true,
        contextmenu: false,
        plugins: [
            'paste', 'image', 'imagetools', 'emoticons', 'table', 'textcolor', 'colorpicker', 'link', 'autolink', 'lists', 'advlist', 'autoresize', 'tiny_mce_wiris', 'fullscreen', 'media', 'lineheight', 'preview', 'code', 'template'
        ],
        image_caption: true,
        image_advtab: true,
        file_picker_types: 'image media',
        file_browser_callback_types: 'image media',
        font_formats: 'Times New Roman="Times New Roman", Times, serif;',
        fontsize_formats: '8px 9px 10px 11px 12px 14px 16px 18px 24px 30px 36px 48px 60px 72px 96px',
        lineheight_formats: '20px 22px 24px 26px 36px 38px 40px 42px 44px 46px 48px 50px 52px 54px 56px 56px 58px 60px 62px 64px 66px 68px 70px',
        toolbar: 'undo redo | tiny_mce_wiris_formulaEditor tiny_mce_wiris_formulaEditorChemistry superscript subscript | bold italic underline strikethrough | forecolor backcolor | link image media table | fontselect fontsizeselect | formatselect lineheightselect | alignleft aligncenter alignright alignjustify | centerVideo | bullist numlist outdent indent | emoticons | removeformat fullscreen preview code | toccustom | templateanswer | warningTemplate noteTemplate keypointTemplate | addClassText addFanpage addMap template',
        media_live_embeds: true,
        media_poster: false,
        setup: function(editor) {
        //     editor.on('PastePreProcess', function(e) {
        //         //Add code here
        //         e.content = helperFunc.handleHtml(e.content);
        //     });

            editor.addButton('addClassText', {
                text: 'Add class',
                icon: 'plus',
                tooltip: "Add class for TOC",
                onclick: function() {
                    var content = editor.selection.getContent();
                    if (content) {
                        editor.selection.setContent('<span class="text-selected" style="background-color: #cedde5">' + content + '</span>');
                    }
                },
            });
            editor.addButton('addFanpage', {
                text: 'Fanpage',
                icon: false,
                tooltip: "Thêm fanpage",
                onclick: function () {
                    editor.windowManager.open({
                        html: '<input type="text" id="fanpage_url" placeholder="Nhập link fanpage" style="width:400px;height:30px;padding-left: 10px; margin:10px 0 0 10px;border:1px solid #F0F0F0;"/>',
                        width : 400,
                        height : 60,
                        title: 'Chèn Fanpage',
                        buttons: [{
                            text: "Add",
                            onclick: function() {
                                var fanpage_url = $("#fanpage_url").val()
                                var html = '<div class="template-section" contenteditable="false">' +
                                   '<h2 class="section-title" contenteditable="true">Fanpage trường</h2>' +
                                   '<center><iframe src="https://www.facebook.com/plugins/page.php?href=' + fanpage_url + '&locale=vi_VN&tabs=timeline&width=500&height=600" scrolling="no" frameborder="0" allowtransparency="false" style="border:none;overflow:hidden;max-width:100%;height:600px;width: 500px;align-items:center;justify-content:center;margin:auto;"></iframe></center>' +
                                   '</div>'
                                editor.insertContent(html);
                                (this).parent().parent().close()
                            },
                        }]
                    });
                }
            });
            editor.addButton('addMap', {
                text: 'Map',
                icon: false,
                tooltip: "Thêm bản đồ",
                onclick: function () {
                    editor.windowManager.open({
                        html: '<input type="text" id="map_iframe" placeholder="Nhập link iframe bản đồ" style="width:600px;height:30px;padding-left: 10px; margin:10px 0 0 10px;border:1px solid #F0F0F0;"/>',
                        width : 600,
                        height : 60,
                        title: 'Chèn Bản đồ',
                        buttons: [{
                            text: "Add",
                            onclick: function() {
                                var map_iframe = $("#map_iframe").val()
                                var html = '<div class="template-section" contenteditable="false">' +
                                   '<h2 class="section-title" contenteditable="true">Xem vị trí trên bản đồ</h2>' +
                                   '<center>' + map_iframe + '</center>' +
                                   '</div>'
                                editor.insertContent(html);
                                (this).parent().parent().close()
                            },
                        }]
                    });
                }
            });
        },
        file_picker_callback: function(callback, value, meta) {
            let input = document.createElement('input');
            input.setAttribute('type', 'file');
            input.setAttribute('accept', 'image/*, audio/*, video/*');
            input.click();

            input.onchange = function () {
                let type = meta.filetype;
                let file = this.files[0];
                let formData = new FormData();
                formData.append('file', file);

                let endpoint = URL_MEDIA_UPLOAD;
                let altText = 'Media VietJack';

                if (type == 'image') {
                    endpoint = URL_IMAGE_UPLOAD;
                }

                axios.post(endpoint, formData)
                    .then(res => {
                        const { data } = res;

                        if (type == 'image') {
                            callback(data, { alt: altText });
                        } else {
                            callback(data, {source2: data, poster: null});
                        }
                    })
                    .catch(err => {
                        console.log(err.message);
                    })
            };
        },
        images_upload_handler: function(blobInfo, success, failure) {
            let formData = new FormData();
            formData.append('file', blobInfo.blob(), blobInfo.filename());

            axios.post(URL_IMAGE_UPLOAD, formData)
                .then(res => {
                    success(res.data);
                })
                .catch(err => {
                    failure(err.message)
                })
        },
        paste_preprocess: function(plugin, args) {
            args.content = helperFunc.handleHtml(args.content);
        },
        templates: [
            {
                title: 'Template - Tuyển sinh',
                description: 'Bài viết dạng tuyển sinh ',
                content: '<section id="gioithieu" class="section-1" contenteditable="false"><div class="template-section" contenteditable="false">' +
                   '<h2 class="section-title" contenteditable="true">Giới thiệu</h2>' +
                   '<div class="section-content short-text section-content-1" contenteditable="true">Nhập nội dung - Giới thiệu</div>' +
                   '<div class="show-more"><a class="btn-show-more" href="#gioithieu">Xem thêm</a></div>' +
                   '</div></section>' +
                   '<section id="thongtintuyensinh" class="section-2" contenteditable="false"><div class="template-section" contenteditable="false">' +
                   '<h2 class="section-title" contenteditable="true">Thông tin tuyển sinh</h2>' +
                   '<div class="section-content short-text section-content-2" contenteditable="true">Nhập nội dung - Thông tin tuyển sinh</div>' +
                   '<div class="show-more"><a class="btn-show-more" href="#thongtintuyensinh">Xem thêm</a></div>' +
                   '</div></section>' +
                   '<section id="diemchuan" class="section-3" contenteditable="false"><div class="template-section" contenteditable="false">' +
                   '<h2 class="section-title" contenteditable="true">Điểm chuẩn các năm</h2>' +
                   '<div class="section-content short-text section-content-3" contenteditable="true">Nhập nội dung - Điểm chuẩn các năm</div>' +
                   '<div class="show-more"><a class="btn-show-more" href="#diemchuan">Xem thêm</a></div>' +
                   '</div></section>' +
                   '<section id="hocphi" class="section-4" contenteditable="false"><div class="template-section" contenteditable="false">' +
                   '<h2 class="section-title" contenteditable="true">Học phí</h2>' +
                   '<div class="section-content short-text section-content-4" contenteditable="true">Nhập nội dung - Học phí</div>' +
                   '<div class="show-more"><a class="btn-show-more" href="#hocphi">Xem thêm</a></div>' +
                   '</div></section>' +
                   '<section id="chuongtrinh" class="section-5" contenteditable="false"><div class="template-section" contenteditable="false">' +
                   '<h2 class="section-title" contenteditable="true">Chương trình đào tạo</h2>' +
                   '<div class="section-content short-text section-content-5" contenteditable="true">Nhập nội dung - Chương trình đào tạo</div>' +
                   '<div class="show-more"><a class="btn-show-more" href="#chuongtrinh">Xem thêm</a></div>' +
                   '</div></section>' +
                   '<section id="hinhanh" class="section-img" contenteditable="false"><div class="template-section" contenteditable="false">' +
                   '<h2 class="section-title" contenteditable="true">Một số hình ảnh</h2>' +
                   '<div class="section-content short-text section-content-img" contenteditable="true">Nhập nội dung - Một số hình ảnh</div>' +
                   '<div class="show-more"><a class="btn-show-more" href="#hinhanh">Xem thêm</a></div>' +
                   '</div></section>'
            },
            {
                title: 'Section 1 - Giới thiệu',
                description: 'Section 1',
                content: '<section id="gioithieu" class="section-1" contenteditable="false"><div class="template-section" contenteditable="false">' +
                   '<h2 class="section-title" contenteditable="true">Giới thiệu</h2>' +
                   '<div class="section-content short-text section-content-1" contenteditable="true">Nhập nội dung - Giới thiệu</div>' +
                   '<div class="show-more"><a class="btn-show-more" href="#gioithieu">Xem thêm</a></div>' +
                   '</div></section>'
            },
            {
                title: 'Section 2 - Thông tin tuyển sinh',
                description: 'Section 2',
                content: '<section id="thongtintuyensinh" class="section-2" contenteditable="false"><div class="template-section" contenteditable="false">' +
                   '<h2 class="section-title" contenteditable="true">Thông tin tuyển sinh</h2>' +
                   '<div class="section-content short-text section-content-2" contenteditable="true">Nhập nội dung - Thông tin tuyển sinh</div>' +
                   '<div class="show-more"><a class="btn-show-more" href="#thongtintuyensinh">Xem thêm</a></div>' +
                   '</div></section>'
            },
            {
                title: 'Section 3 - Điểm trúng tuyển các năm',
                description: 'Section 3',
                content: '<section id="diemchuan" class="section-3" contenteditable="false"><div class="template-section" contenteditable="false">' +
                   '<h2 class="section-title" contenteditable="true">Điểm trúng tuyển các năm</h2>' +
                   '<div class="section-content short-text section-content-3" contenteditable="true">Nhập nội dung - Điểm trúng tuyển các năm</div>' +
                   '<div class="show-more"><a class="btn-show-more" href="#diemchuan">Xem thêm</a></div>' +
                   '</div></section>'
            },
            {
                title: 'Section 4 - Học phí',
                description: 'Section 4',
                content: '<section id="hocphi" class="section-4" contenteditable="false"><div class="template-section" contenteditable="false">' +
                   '<h2 class="section-title" contenteditable="true">Học phí</h2>' +
                   '<div class="section-content short-text section-content-4" contenteditable="true">Nhập nội dung - Học phí</div>' +
                   '<div class="show-more"><a class="btn-show-more" href="#hocphi">Xem thêm</a></div>' +
                   '</div></section>'
            },
            {
                title: 'Section 5 - Chương trình đào tạo',
                description: 'Section 5',
                content: '<section id="chuongtrinh" class="section-5" contenteditable="false"><div class="template-section" contenteditable="false">' +
                   '<h2 class="section-title" contenteditable="true">Chương trình đào tạo</h2>' +
                   '<div class="section-content short-text section-content-5" contenteditable="true">Nhập nội dung - Chương trình đào tạo</div>' +
                   '<div class="show-more"><a class="btn-show-more" href="#chuongtrinh">Xem thêm</a></div>' +
                   '</div></section>'
            },
            {
                title: 'Section image - Một số hình ảnh',
                description: 'Section image',
                content: '<section id="hinhanh" class="section-img" contenteditable="false"><div class="template-section" contenteditable="false">' +
                   '<h2 class="section-title" contenteditable="true">Một số hình ảnh</h2>' +
                   '<div class="section-content short-text section-content-img" contenteditable="true">' +
                   'Nhập nội dung - Hình ảnh' +
                   '<div class="column"></div>' +
                   '<div class="column"></div>' +
                   '<div class="show-more"><a class="btn-show-more" href="#hinhanh">Xem thêm</a></div>' +
                   '</div></section>'
            },
            {
                title: 'Section mẫu',
                description: 'Section mẫu',
                content: '<section id="section-vj" class="section-vj" contenteditable="false"><div class="template-section" contenteditable="false">' +
                   '<h2 class="section-title" contenteditable="true">Tiêu đề Section mẫu</h2>' +
                   '<div class="section-content short-text section-content-vj" contenteditable="true">Nhập nội dung - Section mẫu</div>' +
                   '<div class="show-more"><a class="btn-show-more" href="#section-vj">Xem thêm</a></div>' +
                   '</div></section>'
            },
            {
                title: 'Section - Không có xem thêm',
                description: 'Section ',
                content: '<section class="section-vj" contenteditable="false"><div class="template-section" contenteditable="false">' +
                   '<h2 class="section-title" contenteditable="true">Tiêu đề Section - Không có xem thêm</h2>' +
                   '<div class="section-content section-content-vj" contenteditable="true">Nội dung Section - Không có xem thêm</div>' +
                   '</div></section>'
            },
            {
                title: 'Aside',
                description: 'Aside',
                content: '<aside class="aside-1" contenteditable="false"><div class="template-aside" contenteditable="false">' +
                   '<h2 class="aside-title" contenteditable="true">Tiêu đề Aside</h2>' +
                   '<div class="aside-content short-text aside-content-1" contenteditable="true">Aside content</div>' +
                   '<div class="show-more"><a class="btn-show-more" href="#">Xem thêm</a></div>' +
                   '</div></aside>'
            },
            { title: 'Title VietJack', description: 'A sub title for writers block', content: 'Vietjack xin giới thiệu đến các quý thầy cô, các em học sinh lớp 9 bài văn mẫu Kể lại một buổi học đáng nhớ của em hay nhất, gồm 9 trang trong đó có dàn ý phân tích chi tiết và 5 bài văn mẫu hay nhất giúp các em học sinh có thêm tài liệu tham khảo trong quá trình ôn tập, củng cố kiến thức và chuẩn bị cho bài tập làm văn sắp tới. Chúc các em học sinh ôn tập thật hiệu quả và đạt được kết quả như mong đợi.\n' +
                   '\n...' },
        ],
        forced_root_block: 'p',
        extended_valid_elements: 'span[class|style], i[class|style]',
        content_style: `
            body {
                font-family:"Times New Roman", Times, serif;
                font-size:18px;
            }

            table {
                margin-left: auto;
                margin-right: auto;
                max-width: 800px;
                width: 100%;
                border-collapse: collapse;
                border: 1px solid #BBB !important;
            }

            table td, table th {
                border: 1px solid #BBB !important;
            }

            img {
                max-width: 700px;
                height: auto;
            }

            p iframe[src*="www.youtube.com"] {
                width: 500px !important;
                height: 281px !important;
            }
            
            .template-section .show-more {
                display: none;
            }

            .template-section .section-content {
                min-height: 150px;
            }

            .template-section ul li span {
                line-height: 36px;
            }

            h2.section-title {
                color: #1350a3;
                background: url('/images/icons/readbook_icon.png') left no-repeat;
                padding: 10px 5px 10px 50px;
            }

            .vj-template-answer label span {
                display: unset;
            }
        `
    };

    document.querySelectorAll('.editor-input-content').forEach(el => {
        if (!el.id) el.id = 'mce-' + Math.random().toString(36).slice(2);

        if (tinymce?.get(el.id)) return;

        tinymce.init({
            ...tinyMCEConfiguration,
            target: el,
        });
    });
}

// remove FREE TRIAL EXPIRED foreground MathType
function loaded(selector, callback) {
    // Trigger after page load.
    $(function () {
        callback($(selector));
    });

    // Trigger after page update using MutationObserver.
    const observer = new MutationObserver(function (mutationsList) {
        mutationsList.forEach(function (mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function (node) {
                    if ($(node).is(selector)) {
                        callback($(node));
                    }
                });
            }
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

loaded('.wrs_modal_dialogContainer', function (el) {
    // some action
    el.find('.wrs_tickContainer').remove();
});

$('body').on('click', '#mceu_100', function(event) {
    if(!$(event.target).is('#mceu_102-button'))
    {
        $(this).find('#mceu_102-button').trigger('click');
    }
})
