export function addValidator() {
    $.extend(true, $.validator, {
        defaults: {
            onchange: function(element) {
                if (element.name in this.submitted) {
                    this.element(element);
                }
            }
        },
        prototype: {
            init: function() {
                this.labelContainer = $(this.settings.errorLabelContainer);
                this.errorContext = this.labelContainer.length && this.labelContainer || $(this.currentForm);
                this.containers = $(this.settings.errorContainer).add(this.settings.errorLabelContainer);
                this.submitted = {};
                this.valueCache = {};
                this.pendingRequest = 0;
                this.pending = {};
                this.invalid = {};
                this.reset();

                var groups = (this.groups = {}),
                    rules;
                $.each(this.settings.groups, function(key, value) {
                    if (typeof value === 'string') {
                        value = value.split(/\s/);
                    }
                    $.each(value, function(index, name) {
                        groups[name] = key;
                    });
                });
                rules = this.settings.rules;
                $.each(rules, function(key, value) {
                    rules[key] = $.validator.normalizeRule(value);
                });

                function delegate(event) {
                    var validator = $.data(this.form, 'validator'),
                        eventType = 'on' + event.type.replace(/^validate/, ''),
                        settings = validator.settings;
                    if (settings[eventType] && !$(this).is(settings.ignore)) {
                        settings[eventType].call(validator, this, event);
                    }
                }

                $(this.currentForm)
                    .on('focusin.validate focusout.validate keyup.validate',
                        ':text, [type="password"], [type="file"], select, textarea, [type="number"], [type="search"], ' +
                        '[type="tel"], [type="url"], [type="email"], [type="datetime"], [type="date"], [type="month"], ' +
                        '[type="week"], [type="time"], [type="datetime-local"], [type="range"], [type="color"], ' +
                        '[type="radio"], [type="checkbox"]', delegate)
                    // Support: Chrome, oldIE
                    // "select" is provided as event.target when clicking a option
                    .on('click.validate', 'select, option, [type="radio"], [type="checkbox"]', delegate)
                    .on('change.validate', 'select', delegate);

                if (this.settings.invalidHandler) {
                    $(this.currentForm).on('invalid-form.validate', this.settings.invalidHandler);
                }

                // Add aria-required to any Static/Data/Class required fields before first validation
                // Screen readers require this attribute to be present before the initial submission http://www.w3.org/TR/WCAG-TECHS/ARIA2.html
                $(this.currentForm).find('[required], [data-rule-required], .required').attr('aria-required', 'true');
            }
        }
    });

    // Kiểm tra số điện thoại
    $.validator.addMethod('phone', function(value, element) {
        if ($(element).data('checknewphone') == 1) {
            var $reg1 = /^01\d{8}$/,
                $reg2 = /^09\d{8}$/,
                $reg3 = /^0[3578]\d{8}$/;
        } else {
            var $reg1 = /^0[2-8]\d{8}$/,
                $reg2 = /^09\d{8}$/,
                $reg3 = /^0[3578]\d{8}$/;
            if ($(element).data('check11')) {
                $reg1 = /^01\d{9}$/;
            }
        }

        return this.optional(element) || $reg1.test(value) || $reg2.test(value) || $reg3.test(value);
    }, 'Số điện thoại không hợp lệ');

    // Sửa kiểm tra email
    $.validator.addMethod('email', function(value, element) {
        var $reg = /^\w+([-.]\w+)*@\w+([-.]\w+)*(\.\w{2,3})+$/;
        return this.optional(element) || $reg.test(value.trim());
    }, 'Địa chỉ email không hợp lệ');

    // password check
    $.validator.addMethod('password', function(value) {
        return /^\S*$/.test(value) // consists of only these
    }, 'Không được có khoảng trắng');
};
