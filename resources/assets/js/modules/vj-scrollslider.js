export function vjScrollSlider() {
    // JS needed ONLY for click stuff
    // Presentation/scrolling/snapping can be done using only CSS
    document.querySelectorAll('.scroll-slider').forEach((slider) => {
        const scrollBox = slider.querySelector('.scroll-on');
        const overlays = slider.querySelectorAll('.scroll-overlay');
        const scrollItem = slider.querySelector('ul');
        const activeItem = slider.querySelector('li.active');

        if (!scrollBox || overlays.length !== 2 || !scrollItem) return;

        const [leftOverlay, rightOverlay] = overlays;

        let maxScrollLeft = 0;
        let isScrolling = false;

        // Hàm hiển thị/ẩn overlay
        const setOverlayVisibility = (overlay, isVisible) => {
            overlay.style.opacity = isVisible ? '1' : '0';
            overlay.style.pointerEvents = isVisible ? 'auto' : 'none';
        };

        // Hàm cập nhật trạng thái overlay, gọi trong requestAnimationFrame
        const updateOverlays = () => {
            const atStart = scrollBox.scrollLeft < 10; // cách left 5px thì ẩn
            const atEnd = scrollBox.scrollLeft + scrollBox.clientWidth >= scrollItem.scrollWidth - 1;

            setOverlayVisibility(leftOverlay, !atStart);
            setOverlayVisibility(rightOverlay, !atEnd);

            // Đánh dấu đã cập nhật xong trong frame này
            isScrolling = false;
        };

        // Hàm được gọi khi cuộn. Chỉ đánh dấu trạng thái, không cập nhật DOM ngay lập tức
        const onScroll = () => {
            if (!isScrolling) {
                isScrolling = true;
                requestAnimationFrame(updateOverlays);
            }
        };

        // Cuộn đến item đang active nếu có
        const scrollActiveItemIntoView = () => {
            if (activeItem) {
                activeItem.scrollIntoView({
                    behavior: 'instant',
                    block: 'nearest',
                    inline: 'center',
                });
            }
        };

        const onLeftOverlayClick = () => {
            const remainingLeft = scrollBox.scrollLeft;
            scrollBox.scrollBy({
                top: 0,
                left: -Math.min(150, scrollBox.scrollLeft),
                behavior: 'smooth',
            });
        };

        const onRightOverlayClick = () => {
            const remainingRight = maxScrollLeft - scrollBox.scrollLeft;
            scrollBox.scrollBy({
                top: 0,
                left: Math.min(150, maxScrollLeft - scrollBox.scrollLeft),
                behavior: 'smooth',
            });
        };

        const initScrollSlider = () => {
            // Tính toán độ dài cuộn tối đa một lần
            maxScrollLeft = scrollItem.scrollWidth - scrollBox.clientWidth;

            // Cập nhật overlay ban đầu
            updateOverlays();

            // Cho slider hiện ra sau ms để tránh flicker
            setTimeout(() => {
                slider.style.opacity = '1';
            }, 150);

            scrollActiveItemIntoView();
        };

        scrollBox.addEventListener('scroll', onScroll, { passive: true });
        leftOverlay.addEventListener('click', onLeftOverlayClick);
        rightOverlay.addEventListener('click', onRightOverlayClick);

        initScrollSlider();
    });
}
