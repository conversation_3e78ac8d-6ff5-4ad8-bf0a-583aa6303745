import React, { Component } from 'react';
import SortableTree, {
  changeNodeAtPath,
  addNodeUnderParent,
  removeNodeAtPath,
  getVisibleNodeCount,
  getFlatDataFromTree,
  getNodeAtPath,
  getTreeFromFlatData
} from 'react-sortable-tree';
import './style/style.css'; // This only needs to be imported once in your app
// import ModalFormEdit from './components/ModalFormEdit';

const nodeTemplate = {
  name: '<PERSON><PERSON> mục mới'
};

export default class Category extends Component {
  constructor(props) {
    super(props);

    this.state = {
      treeData: [],
      lastMovePrevPath: null,
      lastMoveNextPath: null,
      lastMoveNode: null,
      addAsFirstChild: false,
      showModal: false,
      currentNode: {},
      pathCurrentNode: null,
    };
  }

  componentDidMount() {
    axios.get(`/admincp/category/${this.props.parentid}/sub-category`).then((res) => {
      if (res.status === 200 || res.status === 201) {
        this.setState({
          treeData: getTreeFromFlatData({
            flatData: res.data.subCategories.map(node => ({ ...node, expanded: true })),
            getKey: node => node.id, // resolve a node's key
            getParentKey: node => node.parent_id, // resolve a node's parent's key
            rootKey: this.props.parentid, // The value of the parent key when there is no parent (i.e., at root level)
          })
        });
      }
    })
  }

  renderContentNode = (node, path) => {
    return (
      <div style={{ display: 'contents' }}>
        <p className="m-0">- {node.name}</p>
        <p className="m-0 font-weight-normal">+ Sub name: {node.sub_name}</p>
        <p className="m-0 font-weight-normal">+ URL: <mark className="text-primary">{ node.url || 'N/A' }</mark></p>
      </div>
    );
  }

  // handleShowModal = (node, path) => {
  //   this.setState({showModal: true, currentNode: node, pathCurrentNode: path});
  // }

  // handleCloseModal = () => {
  //   this.setState({showModal: false, currentNode: {}, pathCurrentNode: null});
  // }

  addNewParentNode = (parentId) => {
    let newData = {
      name: nodeTemplate.name,
      parent_id: parentId,
    };

    axios.post(`/admincp/category/${parentId}/sub-category/store`, newData).then((res) => {
      this.setState({
        treeData: this.state.treeData.concat(res.data),
      })
    }).catch(function (error) {
      alert('Có lỗi xảy ra, vui lòng liên hệ tổ IT');
    });
  }

  addNewChildNode = (node, path) => {
    let newData = {
      name: nodeTemplate.name,
      parent_id: node.id,
      class_id: this.props.classid,
      subject_id: this.props.subjectid,
    };

    axios.post(`/admincp/category/${node.id}/sub-category/store`, newData).then((res) => {
      let newTree = addNodeUnderParent({
        treeData: this.state.treeData,
        parentKey: path[path.length - 1],
        expandParent: true,
        getNodeKey: ({ treeIndex }) => treeIndex,
        newNode: res.data,
        addAsFirstChild: this.state.addAsFirstChild,
      });

      this.setState({ treeData: newTree.treeData });
    }).catch(function (error) {
      alert('Có lỗi xảy ra, vui lòng liên hệ tổ IT');
    });
  }

  removeNode = (node, path) => {
    let r = confirm('Bạn có chắc chắn muốn xóa bản ghi này ? Mọi dữ liệu đã xóa không thể khôi phục lại.');

    if (r == true) {
      axios.get(`/admincp/category/delete/${node.id}`).then((res) => {
        if (res.data.deleted) {
          const newTree = removeNodeAtPath({
            treeData: this.state.treeData,
            path,
            getNodeKey: ({ treeIndex }) => treeIndex,
          });
          $('.loader_wrapper').show();
          setTimeout(() => {
            this.setState({ treeData: newTree });
            $('.loader_wrapper').hide();
          }, 300);
        }
      }).catch(function (error) {
        alert('Có lỗi xảy ra, vui lòng liên hệ tổ IT');
      });
    }
  }

  updateNode = (newNode) => {
    const path = this.state.pathCurrentNode;
    const newTree = changeNodeAtPath({
      treeData: this.state.treeData,
      path,
      getNodeKey: ({ treeIndex }) => treeIndex,
      newNode,
    });
    this.setState({ treeData: newTree });
    this.handleCloseModal();
  }

  recordCall = (name, args) => {
    const treeData = this.state.treeData;
    console.log(args);
  }

  onSortTree = () => {
    let r = confirm('Việc sắp xếp lại sẽ rất tốn kém nên hãy cân nhắc kỹ trước khi cập nhật!');

    if (r == true) {
      const flatDatas = getFlatDataFromTree({
        treeData: this.state.treeData,
        getNodeKey: ({ node }) => node.id,
        ignoreCollapsed: false
      }).map(({ node, path, getNodeKey }) => ({
        id: node.id,
        name: node.name,
        parent_id: path.length > 1 ? path[path.length - 2] : null,
      }));

      axios.post(`/admincp/category/${this.props.parentid}/sub-category/sort`, {'categories': flatDatas}).then((res) => {
        $('.loader_wrapper').show();
        setTimeout(function() {
          $('.loader_wrapper').hide();
        }, 300);
      }).catch(function (error) {
        alert('Có lỗi xảy ra, vui lòng liên hệ tổ IT');
      });
    }
  }

  render() {
    const getNodeKey = ({ treeIndex }) => treeIndex;
    const count = getVisibleNodeCount({treeData: this.state.treeData})
    const { lastMovePrevPath, lastMoveNextPath, lastMoveNode, showModal, currentNode } = this.state;

    return (
      <div>
        {/*<ModalFormEdit
          showModal={showModal}
          currentNode={currentNode}
          handleCloseModal={this.handleCloseModal}
          updateNode={this.updateNode}
          { ...this.props }
        />*/}
        <div style={{ height: count * 100, minHeight: 62 }}>
          { this.state.treeData.length == 0 ? (
            <p>-- Chưa có danh mục con nào --</p>
          ) : (
            <SortableTree
              treeData={this.state.treeData}
              rowHeight={100}
              onChange={treeData => this.setState({ treeData })}
              // Need to set getNodeKey to get meaningful ids in paths
              getNodeKey={getNodeKey}
              onVisibilityToggle={args => this.recordCall('onVisibilityToggle', args)}
              onMoveNode={args => {
                this.recordCall('onMoveNode', args);
                const { prevPath, nextPath, node } = args;
                this.setState({
                  lastMovePrevPath: prevPath,
                  lastMoveNextPath: nextPath,
                  lastMoveNode: node,
                });
              }}
              onDragStateChanged={args => this.recordCall('onDragStateChanged', args)}
              generateNodeProps={({ node, path }) => ({
                title: (
                  this.renderContentNode(node, path)
                ),
                buttons: [
                  <button
                    style={{ marginRight: 5 }}
                    onClick={() => { window.location.href = `/admincp/category/${ node.id }/edit` }}
                  >
                    <i className="fa fa-pencil-square-o"></i>
                  </button>,
                  <button
                    style={{ marginRight: 5 }}
                    onClick={() => this.addNewChildNode(node, path)}
                  >
                    <i className="fa fa-plus"></i>
                  </button>,
                  <button
                    onClick={() => this.removeNode(node, path)}
                  >
                    <i className="fa fa-trash-o"></i>
                  </button>,
                ],
              })}
            />
          ) }
        </div>
        <br />
        <button onClick={() => this.addNewParentNode(this.props.parentid)}>
          Thêm danh mục mới
        </button>
        <button style={{ float: 'right' }} onClick={this.onSortTree}>
          Lưu sắp xếp
        </button>
      </div>
    );
  }
}
