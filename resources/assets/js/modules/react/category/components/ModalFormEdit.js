import React, {Component} from 'react';
import {
  Button,
  Modal,
  FormGroup,
  ControlLabel,
  HelpBlock,
  FormControl
} from 'react-bootstrap';

const initialAttribute = {
  errors: {},
}

class ModalFormEdit extends Component {
  constructor(props) {
    super(props);

    this.state = {
      nodeInfo: {},
      isLeafNode: false,
    };

    this.attr = JSON.parse(JSON.stringify(initialAttribute));
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.currentNode.id) {
      const isLeafNode = nextProps.currentNode.children ? false : true;

      this.setState({ nodeInfo: nextProps.currentNode, isLeafNode });
    }
  }

  getValidationState = (name) => {
    const nodeInfo = this.state.nodeInfo;
    let length = 0;

    if (!_.isEmpty(nodeInfo)) {
      switch(name) {
        case 'name':
          length = nodeInfo.name.trim().length;

          if (length == 0) {
            this.attr.errors.name = '<PERSON><PERSON>ê<PERSON> đề là bắt buộc';
            return 'error';
          } else {
            this.attr.errors = {};
            return 'success';
          }
        default:
          return null;
      }
    }
  }

  handleInputChange = (e, name) => {
    const tmpNodeInfo = {};
    const nodeInfo = this.state.nodeInfo;

    if (!_.isEmpty(nodeInfo)) {
      switch(name) {
        case 'name':
          tmpNodeInfo.name = e.target.value;
          break;
        case 'url':
          tmpNodeInfo.url = e.target.value;
          break;
      }

      this.setState({ nodeInfo: { ...nodeInfo, ...tmpNodeInfo} });
    }
  }

  submitForm = (e) => {
    const nodeInfo = this.state.nodeInfo;

    if (_.isEmpty(this.attr.errors) && !_.isEmpty(nodeInfo)) {
      const nodeData = {
        name: nodeInfo.name,
        url: nodeInfo.url,
      }

      axios.put(`/admincp/category/${nodeInfo.id}`, nodeData).then((res) => {
        if (res.data.updated) {
          this.props.updateNode({ ...nodeInfo, ...res.data.data});
        }
      }).catch(function (error) {
        const { errors } = error.response.data;
        let text = '';

        for (let [key, value] of Object.entries(errors)) {
          text += value[0];
        }

        alert('Có lỗi xảy ra, vui lòng liên hệ tổ IT: ' + text);
      });
      return;
    } else {
      alert('Kiểm tra lại dữ liệu nhập!')
      return;
    }
  }

  render() {
    const { showModal, currentNode, handleCloseModal } = this.props;
    const { nodeInfo, isLeafNode } = this.state;

    return (
      <Modal
        show={showModal}
        onHide={handleCloseModal}
        bsSize="sm"
      >
        <Modal.Body>
          <div className="container">
            <form id="formEdit">
              <FormGroup
                controlId="name"
                validationState={this.getValidationState('name')}
              >
                <ControlLabel>Tên <span style={{ color: 'red' }}>*</span></ControlLabel>
                <FormControl
                  type="text"
                  value={nodeInfo.name ? nodeInfo.name : ''}
                  placeholder="Enter text"
                  onChange={e => this.handleInputChange(e, 'name')}
                />
                <FormControl.Feedback />
                <HelpBlock>{ this.attr.errors.name }</HelpBlock>
              </FormGroup>
              <FormGroup
                controlId="url"
                validationState={this.getValidationState('url')}
              >
                <ControlLabel>URL</ControlLabel>
                <FormControl
                  type="text"
                  value={nodeInfo.url ? nodeInfo.url : ''}
                  placeholder="Enter URL"
                  onChange={e => this.handleInputChange(e, 'url')}
                />
                <FormControl.Feedback />
                <HelpBlock>{ this.attr.errors.url }</HelpBlock>
              </FormGroup>
            </form>
          </div>
        </Modal.Body>
        <Modal.Footer style={{ marginTop: '20px' }}>
          <Button onClick={handleCloseModal}>
            Đóng
          </Button>
          <Button onClick={this.submitForm} bsStyle="primary">
            Cập nhật
          </Button>
        </Modal.Footer>
      </Modal>
    )
  }
}

export default ModalFormEdit;
