import { v4 as uuidv4 } from 'uuid';

export function htmlToJson(html = '') {
  let content = '';
  let answers = [];

  if (html.trim()) {
    try {
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');
      const spans = Array.from(doc.querySelectorAll('span.flag-el'));

      spans.forEach(span => {
          let targetId = span.id;

          if (!targetId || spans.filter(s => s.id === targetId).length > 1) {
              targetId = uuidv4();
              span.id = targetId;
          }

          const blankText = span.getAttribute('data-blank-text') || '';
          span.removeAttribute('data-blank-text');

          if (blankText && blankText !== '__') {
              const optionId  = uuidv4();

              answers.push({
                id:      optionId,
                content: blankText,
                targetId,
              });
          }

          span.innerHTML = '_'.repeat(Math.min(blankText.length + 1, 10));
      });

      content = doc.body.innerHTML;
    } catch (err) {
      console.error('htmlToJson error:', err);
    }
  }

  return {
    content,
    answers,
  };
}

export function jsonToHtml(questionJson = {}) {
  try {
    const { content = '', answers = [] } = typeof questionJson === 'string'
      ? JSON.parse(questionJson)
      : questionJson;

    const parser = new DOMParser();
    const doc = parser.parseFromString(content, 'text/html');

    answers.forEach((answer) => {
      const span = doc.getElementById(answer.answer);
      if (!span) return;

      span.setAttribute('data-blank-text', answer.content);
      span.innerHTML = '[ ' + answer.content + ' ]';
    });

    return doc.body.innerHTML.trim();
  } catch {
    console.error('Invalid JSON passed to jsonToHtml');
    return '';
  }
}
