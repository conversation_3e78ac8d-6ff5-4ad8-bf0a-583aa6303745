import React, {Component} from 'react'
import {createEditorInputContentInstance} from '../../../editor';
import { checkImageContent } from '../../helper';
import ButtonSubmit from '../../components/ButtonSubmit';

class EditQuestion extends Component {
    constructor(props) {
        super(props);

        this.state = {
            content: '',
            answers: [],
            checkAnswer: 0,
            reason: '',
            slug: '',
            title: '',
            isButtonLoading: false,
            selectText: null,
        }
    }

    componentDidMount() {
        let { question } = this.props;

        this.setState({
            content: question.content || '',
            reason: question.reason || '',
            slug: question.slug || '',
            title: question.title || '',
            selectText: question.text_select || null,
        })

        let answers = question.get_answer;

        this.setState({answers: answers})

        createEditorInputContentInstance();
    }

    onSubmit = () => {
        let countAltImg = 0;
        let {flagIndexId} = this.props;
        let {answers, slug, title} = this.state;

        if (!title) {
            alert('<PERSON>ạn chưa nhập tiêu đề SEO!');

            return;
        }

        let question = tinymce.get(`ed-question-${flagIndexId}`).getContent();
        let reason = tinymce.get(`ed-reason-${flagIndexId}`).getContent();

        if (!question) {
            alert('Bạn chưa nhập nội dung câu hỏi!');

            return;
        }

        if (!reason) {
            alert('Bạn chưa phần giải thích!');

            return;
        }

        if (checkImageContent(question)) {
            alert('Nội dung câu hỏi chứa link ảnh không hợp lệ, bạn phải upload bằng editor, không đc kéo thả hoặc copy - paste!');

            return;
        }

        question = question.replace(/T&agrave;i liệu VietJack/g, function(x) {
            countAltImg++;
            return title + ' (ảnh ' + countAltImg + ')';
        }).replace(/alt=\"\"/g, function(x) {
            countAltImg++;
            return 'alt="' + title + ' (ảnh ' + countAltImg + ')"';
        });

        if (checkImageContent(reason)) {
            alert('Nội dung giải thích chứa link ảnh không hợp lệ, bạn phải upload bằng editor, không đc kéo thả hoặc copy - paste!');

            return;
        }

        reason = reason.replace(/T&agrave;i liệu VietJack/g, function(x) {
            countAltImg++;
            return title + ' (ảnh ' + countAltImg + ')';
        }).replace(/alt=\"\"/g, function(x) {
            countAltImg++;
            return 'alt="' + title + ' (ảnh ' + countAltImg + ')"';
        });

        let newAnswers = [];
        let answerError = 0;

        answers.forEach(function(answer, index) {
            let edAnswer = tinymce.get(`answer-${flagIndexId}-${index}`).getContent();

            if (edAnswer) {
                if (checkImageContent(edAnswer)) {
                    answerError = index + 1;

                    return;
                }

                edAnswer = edAnswer.replace(/T&agrave;i liệu VietJack/g, function(x) {
                    countAltImg++;
                    return title + ' (ảnh ' + countAltImg + ')';
                }).replace(/alt=\"\"/g, function(x) {
                    countAltImg++;
                    return 'alt="' + title + ' (ảnh ' + countAltImg + ')"';
                });

                newAnswers.push({...answer, content: edAnswer});
            }
        });

        if (answerError) {
            alert('Nội dung đáp án ' + answerError + ' chứa link ảnh không hợp lệ, bạn phải upload bằng editor, không đc kéo thả hoặc copy - paste!');

            return;
        }

        if (title != this.props.question.title || slug != this.props.question.slug) {
            var r = confirm('Cảnh báo! Khi bạn thay đổi title hoặc slug sẽ ảnh hưởng đến SEO website.');

            if (r == false) {
                return;
            }
        }

        this.setState({isButtonLoading: true});

        const urlAxios = `/admincp/services/multiplechoices/question/${this.props.question.id}/update`;

        axios.post(urlAxios,{
            answers: newAnswers,
            content: question,
            reason,
            slug,
            title,
            text_select: this.state.selectText,
        }).then((res) => {
            if (res.status === 200 || res.status === 201) {
                alert(typeof res.data === 'string' ? res.data : 'Cập nhật thành công!');

                window.location.reload();
            } else {
                console.log(res);
            }
        }).catch((error) => {
            console.log(error);

            alert('Đã xảy ra lỗi!')

            this.setState({isButtonLoading: false})
        })
    }

    handleChangeQuestion = (e) => {
        let content = e.target.getContent();

        this.setState({content})
    }

    changeCheckAnswer = (checkAnswer) => {
        let {answers} = this.state;

        answers.forEach(function (answer, key) {
            if (key == checkAnswer) {
                answer.answer = 'Y';
            } else {
                answer.answer = 'N';
            }
        });

        this.setState({
            answers,
            checkAnswer
        })
    }

    changeReason(e) {
        let reason = e.target.getContent();

        this.setState({reason})
    }

    changeSlug = (event) => {
        let val = event.target.value;

        this.setState({
            slug: window.helperFunc.titleToSlug(val, 100),
        })
    }

    changeTitle = (event) => {
        let val = event.target.value;
        this.setState({
            title: val,
            slug: window.helperFunc.titleToSlug(val, 100),
        })
    }

    addNewAnswer = () => {
        const { answers } = this.state;
        if (answers.length >= 15) return;

        const blankAnswer = { content: '', answer: 'N' };

        this.setState(
            {
                answers: [...answers, blankAnswer]
            },
            () => {
                createEditorInputContentInstance();
            }
        );
    }

    deleteAnswer = (i) => {
        let {answers} = this.state;
        let length = answers.length;

        if (length > 1) {
            let newAnswer = this.state.answers.filter((s, _idx) => _idx !== i)

            this.setState({
                answers: newAnswer,
            });
        }
    }

    changeTypeInput = (e) => {
        this.setState({selectText: e.target.value})
    }

    render() {
        const {flagIndexId} = this.props;

        const answers = this.state.answers.map((answer, i) => {
            return (
                <div className="answer-content-wrapper" key={i} style={{ display: 'flex', 'marginBottom': '10px' }}>
                    <div className="answer-check">
                        <label className="custom-control custom-radio">
                            <input
                                type="radio"
                                className="custom-control-input"
                                name="answer"
                                checked={answer.answer === 'Y'}
                                value={i}
                                onChange={() => this.changeCheckAnswer(i)}
                            />
                            <span className="custom-control-indicator"></span>
                        </label>
                    </div>
                    <textarea
                        className="form-control editor-input-content"
                        rows="3"
                        id={`answer-${flagIndexId}-${i}`}
                        defaultValue={answer.content}
                    >
                    </textarea>
                    <div className="answer-toolbar">
                        <button
                            type="button"
                            onClick={() => this.deleteAnswer(i)}
                            className="btn btn-link"
                        >
                            <i className="fas fa-trash-alt mx-auto"></i>
                        </button>
                    </div>
                </div>
            )
        })

        return (
            <div className="lecture-add-more">
                <div className="add-content-wrapper">
                    <div className="quiz-add-content">
                        <div style={{overflow: 'scroll', maxHeight: '500px', padding: '7px'}}>
                            {
                                this.props.question &&
                                this.props.question.get_lecture &&
                                this.props.question.get_lecture.quiz_content &&
                                this.props.question.get_lecture.quiz_content.length && (
                                    <div>
                                        <p><code>Chọn đoạn văn</code></p>
                                        <div className="form-group form-group-sm">
                                            <select
                                                className="form-control"
                                                value={this.state.selectText || ''}
                                                data-placeholder="Chọn ..."
                                                name="type"
                                                onChange={this.changeTypeInput}
                                            >
                                                <option value="">__ Chọn __</option>
                                                {this.props.question.get_lecture.quiz_content.map((item, index) => (
                                                   <option value={index} key={index}>{'Đoạn văn ' + (index+1)}</option>
                                                ))}
                                            </select>
                                        </div>
                                    </div>
                                )
                            }
                            <p><code>Câu hỏi</code></p>
                            <textarea
                                className="form-control editor-input-content"
                                rows="3"
                                id={`ed-question-${flagIndexId}`}
                                defaultValue={this.state.content}
                            >
                            </textarea>
                            <br/>
                            <div className="form-group answers-form-group">
                                <p><code>Câu trả lời</code></p>
                            </div>
                            {answers}
                            <button type="button" className="btn btn-info" onClick={() => this.addNewAnswer()}>Thêm đáp án</button>
                            <small className="help-block">Có thể thêm lên đến 15 đáp án.</small>
                            <div className="test-explain">
                                <p><code>Giải thích</code></p>
                                <textarea
                                    className="form-control editor-input-content"
                                    rows="3"
                                    id={`ed-reason-${flagIndexId}`}
                                    defaultValue={this.state.reason}
                                >
                                </textarea>
                            </div>
                            <br />
                            <div className="form-group">
                                <p><code>Tiêu đề SEO câu hỏi</code></p>
                                <input className="form-control" onChange={this.changeTitle} value={this.state.title}/>
                            </div>
                            <div className="form-group">
                                <p><code>Đường dẫn slug câu hỏi (tối đa 100 ký tự)</code></p>
                                <input className="form-control" onChange={this.changeSlug} value={this.state.slug}/>
                            </div>
                        </div>
                        <div className="text-left form-actions mt-10">
                            <ButtonSubmit
                                type="button"
                                isLoading={this.state.isButtonLoading}
                                onClick={this.onSubmit}
                            >
                                Cập nhật
                            </ButtonSubmit>
                        </div>
                    </div>
                </div>
            </div>
        )
    }
}

export default EditQuestion;
