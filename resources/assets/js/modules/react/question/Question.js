import React, { Component } from 'react';
import EditQuestion from './components/EditQuestion';

export default class Question extends Component {
    constructor(props) {
        super(props);

        this.state = {
            question: null,
            oldQuestions: null,
            flagIndexId: Math.floor(Date.now()/1000),
        };
    }

    fetchQuestion = () => {
        if (this.state.question === null) {
            const { qid } = this.props;
            const urlAxios = `/admincp/services/multiplechoices/question/${qid}`;

            axios.get(urlAxios).then((res) => {
                if (res.status === 200 || res.status === 201) {
                    this.setState({
                        question: res.data,
                        flagIndexId: Math.floor(Date.now()/1000),
                    });
                }
            })
        }
    }

    fetchOldQuestion = () => {
        if (this.state.oldQuestions === null) {
            const { qid } = this.props;
            const urlAxios = `/admincp/services/multiplechoices/old-question/${qid}`;

            axios.get(urlAxios).then((res) => {
                if (res.status === 200 || res.status === 201) {
                    this.setState({ oldQuestions: res.data });
                }
            })
        }
    }

    restoreOldQuestion = (oldId, index) => {
        if (confirm('Ban muốn khôi phục bản: ' + index)) {
            const { qid } = this.props;
            const urlAxios = `/admincp/services/multiplechoices/old-question/${qid}/restore/${oldId}`;

            axios.post(urlAxios).then((res) => {
                if (res.status === 200 || res.status === 201) {
                    alert(res.data);

                    window.location.reload();
                }
            }).catch(function(error) {
                console.log(error);
                alert('Không thể khôi phục.')
            })
        }
    }

    render() {
        return (
            <div>
                <div className="modal fade" id={`edit-question-modal-${this.props.qid}`} role="dialog">
                    <div className="modal-dialog modal-lg">
                        <div className="modal-content">
                            <div className="modal-header">
                                <button type="button" className="close" data-dismiss="modal">&times;</button>
                                <p className="modal-title">Câu id:
                                    <a href={`/question/${this.props.qid}`} target="_blank"><strong> {this.props.qid}</strong></a>
                                </p>
                            </div>
                            <div className="modal-body">
                                {this.state.question ? (
                                    <div>
                                        <ul className="nav nav-tabs nav-pills" style={{borderBottom: 'none', background: '#eee', marginBottom: '10px'}}>
                                            <li className="active">
                                                <a data-toggle="tab" href="#home">
                                                    Edit
                                                </a>
                                            </li>
                                            <li><a data-toggle="tab" href="#menu0">Preview</a></li>
                                            {this.state.question.old_questions_count > 0 && (
                                                <li><a data-toggle="tab" href="#menu1" onClick={this.fetchOldQuestion}>Lịch sử</a></li>
                                            )}
                                        </ul>
                                        <div className="tab-content">
                                            <div id="home" className="tab-pane fade in active">
                                                <EditQuestion question={this.state.question} flagIndexId={this.state.flagIndexId} />
                                            </div>
                                            <div id="menu0" className="tab-pane fade">
                                                <p><code>Câu hỏi:</code></p>
                                                <div dangerouslySetInnerHTML={{ __html: this.state.question.content }} />
                                                {this.state.question.get_answer && this.state.question.get_answer.length > 0 && (
                                                    this.state.question.get_answer.map((answer, i) => (
                                                        <div
                                                            key={i}
                                                            dangerouslySetInnerHTML={{ __html: answer.content }}
                                                            style={{ color: answer.answer == 'Y' ? 'green' : '#000' }}
                                                        />
                                                    ))
                                                )}
                                                <p><code>Giải thích:</code></p>
                                                <div dangerouslySetInnerHTML={{ __html: this.state.question.reason }} />
                                                <p><code>Seo title:</code></p>
                                                <p>{ this.state.question.title }</p>
                                                <p><code>Slug:</code></p>
                                                <p>{ this.state.question.slug }</p>
                                            </div>
                                            <div id="menu1" className="tab-pane fade">
                                                {this.state.oldQuestions === null ? (
                                                    <div className="text-center">
                                                        <i className="fa fa-spinner fa-pulse"></i>
                                                    </div>
                                                ) : (
                                                    this.state.oldQuestions.length === 0 ? (
                                                        <p className="text-muted">( Không có lịch sử )</p>
                                                    ) : (
                                                        <div style={{ background: '#dedede', padding: '10px' }}>
                                                            {this.state.oldQuestions.map((oldQuestion) =>
                                                                <div key={oldQuestion.id} style={{ background: '#fff', padding: '10px', marginBottom: '20px' }}>
                                                                    <button
                                                                        className="btn btn-warning pull-right"
                                                                        onClick={() => this.restoreOldQuestion(oldQuestion.id, oldQuestion.index)}
                                                                    >
                                                                        Khôi phục
                                                                    </button>
                                                                    <p><code>History:</code> {oldQuestion.index}</p>
                                                                    {oldQuestion.paragraph && (
                                                                        <div className="mb-20 is-paragraph" dangerouslySetInnerHTML={{ __html: oldQuestion.paragraph }} />
                                                                    )}
                                                                    <div dangerouslySetInnerHTML={{ __html: oldQuestion.content }} />
                                                                    {oldQuestion.old_answers && oldQuestion.old_answers.length > 0 && (
                                                                        oldQuestion.old_answers.map((answer, i) => (
                                                                            <div
                                                                                key={i}
                                                                                dangerouslySetInnerHTML={{ __html: answer.content }}
                                                                                style={{ color: answer.answer == 'Y' ? 'green' : '#000' }}
                                                                            />
                                                                        ))
                                                                    )}
                                                                    <p><code>Giải thích:</code></p>
                                                                    <div dangerouslySetInnerHTML={{ __html: oldQuestion.reason }} />
                                                                    <p><code>Seo title:</code></p>
                                                                    <p>{ oldQuestion.title }</p>
                                                                    <p><code>Slug:</code></p>
                                                                    <p>{ oldQuestion.slug }</p>
                                                                </div>
                                                            )}
                                                        </div>
                                                    )
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                ) :
                                (
                                    <div className="text-center">
                                        <i className="fa fa-spinner fa-pulse"></i>
                                    </div>
                                ) }
                            </div>
                        </div>
                    </div>
                </div>
                <button style={{float: 'right', padding: '0 10px'}} onClick={this.fetchQuestion} className="btn btn-link" data-toggle="modal" data-target={`#edit-question-modal-${this.props.qid}`}>
                    <i className="fas fa-user-edit"></i>
                </button>
            </div>
        );
    }
}
