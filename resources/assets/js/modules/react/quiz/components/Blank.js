import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { decodeHtml, normalizeStr, removeKey } from '../../helper';
import HtmlContent from '../../components/HtmlContent';

const Blank = React.memo(({ question, paragraph = null, isSubmit, hasCheck = false, onSubmit, vip, buyviphtml, saveData }) => {
    const [answerInputs, setAnswerInputs] = useState({});

    const [showReason, setShowReason] = useState(false);

    useEffect(() => {
        if (saveData?.answer) {
            const next = {};
            Object.keys(saveData.answer).forEach(key => {
                next[key] = saveData.answer[key];
            });
            setAnswerInputs(next);
        } else {
            setAnswerInputs({});
        }
        setShowReason(false);
    }, [question.id]);

    const answers = question?.get_answer || [];

    const isExactlyCorrect = useCallback((answered) => {
        const result = {};
        let isCorrect = true;

        for (const answer of answers) {
            const key = String(answer?.answer);

            const expected = normalizeStr(String(answer?.content ?? '')).toLocaleLowerCase('vi');
            const given = normalizeStr(String(answered?.[key] ?? '')).toLocaleLowerCase('vi');
            const check = given && given === expected;

            result[key] = check;

            isCorrect = isCorrect && check;
        }

        return { isCorrect, result };
    }, [answers]);

    const handleInputChange = useCallback((id, value) => {
        if (isSubmit || saveData) return;

        setAnswerInputs(prev => {
            const newState = value ? { ...prev, [id]: value } : removeKey(prev, id);

            if (!hasCheck && onSubmit) onSubmit({ answer: newState }, question);

            return newState;
        });
    }, [isSubmit]);

    const handleSubmit = useCallback(() => {
        if (!onSubmit) return;

        const { isCorrect, result } = isExactlyCorrect(answerInputs);

        onSubmit({ answer: answerInputs, isCorrect, result }, question);
    }, [onSubmit, question, answerInputs, isExactlyCorrect]);

    return (
        <div className={`mcq-box ${isSubmit ? '' : 'slideLeft'}`}>
            { saveData && (
                saveData.isCorrect ? (
                    <div className="mcq-result-icon correct"><i className="fas fa-check"></i> Đúng</div>
                ) : (
                    <div className="mcq-result-icon wrong"><i className="fa fa-times"></i> Sai</div>
                )
            ) }
            <div className="mcq">
                <div className="mcq-content">
                    {paragraph}
                    {question.content && (
                        <HtmlContent
                            html={ question.content }
                            contentType={ question.type }
                            handleInputChange={handleInputChange}
                            answerInputs={answerInputs}
                            saveData={saveData}
                        />
                    )}
                </div>
                {!isSubmit && hasCheck && (
                    <div className="mt-20">
                        <button
                            className="btn btn-warning btn-lg"
                            onClick={handleSubmit}
                            disabled={Object.keys(answerInputs).length === 0}
                        >
                            Nộp bài
                        </button>
                    </div>
                )}
                {question.reason && isSubmit && (
                    <>
                        <button
                            className="btn btn-link"
                            onClick={() => setShowReason(!showReason)}
                            style={{ color: '#FF5722', outline: 'none' }}
                        >
                            <i className="fas fa-info-circle"></i> Xem lời giải
                        </button>

                        {showReason && (
                            <div className="mcq-content mcq-reason">
                                { vip ? (
                                    <>
                                        <div className="question-verified-box">
                                            <img src="/images/verified.webp" width="18" height="21" alt="verified" />
                                            <span className="question-text">Giải bởi Vietjack</span>
                                        </div>
                                        <HtmlContent html={ question.reason } />
                                    </>
                                ) : (
                                    <div dangerouslySetInnerHTML={{ __html: decodeHtml(buyviphtml || '') }} />
                                ) }
                            </div>
                        )}
                    </>
                )}
            </div>
        </div>
    );
});

export default Blank;
