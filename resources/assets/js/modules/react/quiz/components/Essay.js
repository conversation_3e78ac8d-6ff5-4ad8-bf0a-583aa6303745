import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { decodeHtml, normalizeStr } from '../../helper';
import HtmlContent from '../../components/HtmlContent';

const Essay = React.memo(({ question, paragraph = null, isSubmit, hasCheck = false, onSubmit, vip, buyviphtml, saveData }) => {
    const [text, setText] = useState('');
    const [showReason, setShowReason] = useState(false);

    useEffect(() => {
        setText(saveData?.answer?.[0] || '');
        setShowReason(false);
    }, [question.id]);

    const answers = question?.get_answer || [];

    const isExactlyCorrect = useCallback((answered) => {
        const ans = answered.toLocaleLowerCase('vi');
        const isCorrect = answers.some(answer => {
            return ans == answer.content.toLocaleLowerCase('vi');
        });
        const result = { 0: isCorrect ? 1 : 0 };

        return { isCorrect, result };
    }, [answers]);

    const answerChange = (e) => {
        const text = e.target.value;
        setText(text);

        if (!hasCheck && onSubmit) onSubmit({ answer: text ? [text] : [] }, question);
    };

    const handleSubmit = useCallback(() => {
        if (!onSubmit) return;
    
        const processed = normalizeStr(String(text));
        const { isCorrect, result } = isExactlyCorrect(processed);

        onSubmit({ answer: [ processed ], isCorrect, result }, question);

    }, [onSubmit, question, text, isExactlyCorrect]);

    return (
        <div className={`mcq-box ${isSubmit ? '' : 'slideLeft'}`}>
            { saveData && (
                saveData.isCorrect ? (
                    <div className="mcq-result-icon correct"><i className="fas fa-check"></i> Đúng</div>
                ) : (
                    <div className="mcq-result-icon wrong"><i className="fa fa-times"></i> Sai</div>
                )
            ) }
            <div className="mcq">
                <div className="mcq-content">
                    {paragraph}
                    {question.content && (
                        <HtmlContent html={ question.content } />
                    )}
                </div>
                <div className="essay-box">
                    <textarea
                        name="essay"
                        rows={3}
                        value={text}
                        onChange={answerChange}
                        disabled={!!isSubmit}
                        placeholder="Nhập câu trả lời của bạn…"
                        className={`form-control essay-textarea ${ saveData ? ( saveData.isCorrect ? 'correct' : 'wrong' ) : '' }`}
                        style={{ width: '100%' }}
                    />
                    {!isSubmit && hasCheck && (
                        <div className="mt-20">
                            <button
                                className="btn btn-warning btn-lg"
                                onClick={handleSubmit}
                                disabled={!text.trim()}
                            >
                                Nộp bài
                            </button>
                        </div>
                    )}
                </div>
                {question.reason && (isSubmit || !answers.length) && (
                    <>
                        <button
                            className="btn btn-link"
                            onClick={() => setShowReason(!showReason)}
                            style={{ color: '#FF5722', outline: 'none' }}
                        >
                            <i className="fas fa-info-circle"></i> Xem lời giải
                        </button>

                        {showReason && (
                            <div className="mcq-content mcq-reason">
                                { vip ? (
                                    <>
                                        <div className="question-verified-box">
                                            <img src="/images/verified.webp" width="18" height="21" alt="verified" />
                                            <span className="question-text">Giải bởi Vietjack</span>
                                        </div>
                                        <HtmlContent html={ question.reason } />
                                    </>
                                ) : (
                                    <div dangerouslySetInnerHTML={{ __html: decodeHtml(buyviphtml || '') }} />
                                ) }
                            </div>
                        )}
                    </>
                )}
            </div>
        </div>
    );
});

export default Essay;
