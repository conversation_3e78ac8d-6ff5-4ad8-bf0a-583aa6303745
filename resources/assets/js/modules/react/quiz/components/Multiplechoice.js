import React, { useState, useEffect, useCallback, useMemo } from 'react';
import HtmlContent from '../../components/HtmlContent';
import { decodeHtml } from '../../helper';

const MultipleChoice = React.memo(({ question, paragraph = null, isSubmit, hasCheck = false, onSubmit, vip, buyviphtml, saveData }) => {
    const [selectedOptions, setSelectedOptions] = useState([]);
    const [showReason, setShowReason] = useState(false);

    useEffect(() => {
        setSelectedOptions(saveData?.answer || []);
        setShowReason(false);
    }, [question.id]);

    const answers = question?.get_answer || [];

    const rightAnswers = useMemo(() => {
        const arr = [];

        for (let i = 0; i < answers.length; i++) {
            if (answers[i]?.answer === 'Y') arr.push(i);
        }
 
        return arr;
    }, [answers]);

    // trường hợp chỉ cần đúng 1 phần
    const isExactlyCorrect = useCallback((answered) => {
        let isCorrect = false;

        for (const selectedIdx of answered) {
            const check = rightAnswers.indexOf(selectedIdx) !== -1;

            if (check) {
                isCorrect = true;
                break;
            }
        }
        const result = { 0: isCorrect ? 1 : 0 };

        return { isCorrect, result };
    }, [rightAnswers]);

    // trường hợp đúng toàn phần
    // const isExactlyCorrect = useCallback((answered = []) => {
    //     const selectedIdxs = answered.slice().sort((a, b) => a - b);

    //     if (rightAnswers.length !== selectedIdxs.length) return {
    //         isCorrect: false, result: { 0: 0 }
    //     };

    //     for (let i = 0; i < rightAnswers.length; i++) {
    //         if (selectedIdxs[i] !== rightAnswers[i]) return { isCorrect: false, result: { 0: 0 } };
    //     }

    //     return { isCorrect: true, result: { 0: 1 } };
    // }, [rightAnswers]);

    const selectOption = useCallback((index) => {
        if (isSubmit) return;

        const pos = selectedOptions.indexOf(index);
        const newSelectedOptions = pos !== -1 ? selectedOptions.filter(i => i !== index) : [...selectedOptions, index];

        setSelectedOptions(newSelectedOptions);

        if (onSubmit) {
            let logData = { answer: newSelectedOptions};

            if (hasCheck) {
                const { isCorrect, result } = isExactlyCorrect(newSelectedOptions);

                logData.isCorrect = isCorrect;
                logData.result = result;
            }

            onSubmit(logData, question);
        }
    }, [isSubmit, selectedOptions, onSubmit, question, isExactlyCorrect]);

    return (
        <div className={`mcq-box ${isSubmit ? '' : 'slideLeft'}`}>
            { saveData && (
                saveData.isCorrect ? (
                    <div className="mcq-result-icon correct"><i className="fas fa-check"></i> Đúng</div>
                ) : (
                    <div className="mcq-result-icon wrong"><i className="fa fa-times"></i> Sai</div>
                )
            ) }
            <div className="mcq">
                <div className="mcq-content">
                    {paragraph}
                    {question.content && (
                        <HtmlContent
                            html={ question.content }
                            contentType={ question.type }
                        />
                    )}
                </div>
                <div className="mcq-answer">
                    {answers.map((option, index) => {
                        const cls = (isSubmit && hasCheck) ?
                            (saveData?.answer?.includes(index) || selectedOptions.includes(index) ?
                                (option.answer === 'Y' ? 'isCorrect' : 'isWrong') :
                                (option.answer === 'Y' ? 'correctAnswer' : '')
                            )
                            : (selectedOptions.includes(index) ? 'selected' : '');

                        return (
                            <div
                                key={index}
                                className="mcq-option"
                            >
                                <div
                                    className={`mcq-option-box ${cls}`}
                                    onClick={() => selectOption(index)}
                                    style={isSubmit ? { pointerEvents: 'none' } : undefined}
                                >
                                    <HtmlContent html={ option.content || '' } />
                                </div>
                            </div>
                        );
                    })}
                </div>
                {question.reason && (isSubmit || !answers.length) && (
                    <>
                        <button
                            className="btn btn-link"
                            onClick={() => setShowReason(!showReason)}
                            style={{ color: '#FF5722', outline: 'none' }}
                        >
                            <i className="fas fa-info-circle"></i> Xem lời giải
                        </button>

                        {showReason && (
                            <div className="mcq-content mcq-reason">
                                { vip ? (
                                    <>
                                        <div className="question-verified-box">
                                            <img src="/images/verified.webp" width="18" height="21" alt="verified" />
                                            <span className="question-text">Giải bởi Vietjack</span>
                                        </div>
                                        <HtmlContent html={ question.reason } />
                                    </>
                                ) : (
                                    <div dangerouslySetInnerHTML={{ __html: decodeHtml(buyviphtml || '') }} />
                                ) }
                            </div>
                        )}
                    </>
                )}
            </div>
        </div>
    );
});

export default MultipleChoice;
