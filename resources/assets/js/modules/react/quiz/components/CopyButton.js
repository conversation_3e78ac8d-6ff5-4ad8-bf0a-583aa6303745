import React, { useCallback } from 'react';
import debounce from 'lodash/debounce';

const CopyButton = () => {
  const copyLink = useCallback(
    debounce(() => {
      const currentUrl = window.location.href;
      navigator.clipboard.writeText(currentUrl)
        .then(() => {
          $.notify(
            { title: "<strong>Đã sao chép liên kết</strong>", icon: 'fas fa-check-circle' },
            { type: "success", delay: 2000, placement: { from: "top", align: "center" } }
          );
        })
        .catch(err => {
          console.error('Failed to copy: ', err);
          $.notify(
            { title: "<strong>Sao chép thất bại!</strong>", icon: 'fas fa-exclamation-triangle' },
            { type: "danger", delay: 3000, placement: { from: "top", align: "right" } }
          );
        });
    }, 300),
    []
  );

  return (
    <button className="btn btn-copy w-100" onClick={copyLink}>
      <i className="far fa-copy mr-2"></i> Copy link
    </button>
  );
};

export default CopyButton;
