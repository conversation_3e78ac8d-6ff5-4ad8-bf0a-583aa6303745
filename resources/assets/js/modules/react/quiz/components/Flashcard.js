import React, { useState, useEffect, useRef, useCallback } from 'react';
import { decodeHtml } from '../../helper';
import HtmlContent from '../../components/HtmlContent';

const Flashcard = ({ question, paragraph = null, direction, isSubmit, onSubmit, vip, buyviphtml }) => {
    const [isFlipped, setIsFlipped] = useState(false);
    const [animate, setAnimate] = useState(false);
    const flashcardRef = useRef(null);

    useEffect(() => {
        setAnimate(true);
        setIsFlipped(false);

        if (flashcardRef.current) {
            flashcardRef.current.focus();
        }

        const timer = setTimeout(() => {
            setAnimate(false);
        }, 500); // Thời gian phải trùng với duration của animation

        return () => clearTimeout(timer);
    }, [question.id]);

    const flipFlashCard = useCallback(() => {
        setIsFlipped(prev => !prev);

        if (isSubmit) return;

        onSubmit({ answer: [], isCorrect: true }, question);
    }, [isSubmit, onSubmit, question]);

    const handleKeyDown = useCallback((e) => {
        if (e.key === ' ' || e.key === 'Spacebar') { // 'Spacebar' cho các trình duyệt cũ
            e.preventDefault();
            flipFlashCard();
        }
    }, [flipFlashCard]);

    const animationClass = direction === 1 ? 'bigEntranceDown' : 'slideRight';

    return (
        <div
            className={`flashcard-container ${animate ? animationClass : ''}`}
            onClick={flipFlashCard}
            onKeyDown={handleKeyDown}
            tabIndex="0"
            ref={flashcardRef}
        >
            <button className="btn btn-flip">
                <i className="far fa-hand-point-up mr-5"></i>  Lật thẻ
            </button>
            <div className={`flashcard-box ${isFlipped ? 'is-flipped' : ''}`}>
                <div className="flashcard">
                    <div className="flashcard-content flashcard-front">
                        <div className="mcq">
                            <div className="mcq-content">
                                {paragraph}
                                { question.content && <HtmlContent html={ question.content } contentType={ question.type } readOnly /> }
                                { question.type == 'test' && (
                                    <ul className="list-circle">
                                        {question?.get_answer.map((option, index) => (
                                            <li key={index}>
                                                <HtmlContent className="mcq-option-content" html={  option.content || '' } />
                                            </li>
                                        ))}
                                    </ul>
                                )}
                            </div>
                        </div>
                    </div>
                    <div className="flashcard-content flashcard-back">
                        <div className="mcq">
                            <div className="mcq-content">
                                { vip ? (
                                    <HtmlContent html={  question.reason || '' } />
                                ) : (
                                    <div dangerouslySetInnerHTML={{ __html: decodeHtml(buyviphtml) }} />
                                ) }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Flashcard;
