const LikeButton = ({ auth, examid, liked }) => {
  if (!auth) {
    return (
      <a className="btn btn-outline-like btn-popup w-100" href="#login-box"><i className="far fa-heart mr-2"></i> Lưu đề thi</a>
    );
  }

  return (
    <button className="btn btn-outline-like js-btn-like w-100" data-tracking-id={examid} data-type="exam">
      { liked == 1 ? (<><i className="fas fa-heart heart-color mr-2"></i> Đ<PERSON> lưu</>) : (<><i className="far fa-heart mr-2"></i> Lưu đề thi</>)}
    </button>
  );
};

export default LikeButton;
