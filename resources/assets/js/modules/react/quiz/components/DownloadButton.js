const DownloadButton = ({ auth, examid, title }) => {
  if (!auth) {
    return (
      <a className="btn btn-down btn-popup w-100" href="#login-box"><i className="fas fa-download mr-2"></i> <span className="text-lg-none">In đề / </span>Tải về</a>
    );
  }

  return (
    <div className="dropdown">
      <button
        id="dropdownDownloadButton"
        className="btn btn-down dropdown-toggle w-100"
        data-toggle="dropdown"
        aria-haspopup="true"
        aria-expanded="false"
        data-title={ title }
        data-eid={ examid }
      >
        <i className="fas fa-download mr-2"></i> <span className="text-lg-none">In đề / </span>Tải về
      </button>
      <ul className="dropdown-menu" aria-labelledby="dropdownDownloadButton" style={{ left: 'auto', right: 0 }}>
        <li><button className="btn btn-link js-btn-edown" data-type="quiz"><i className="fas fa-file-download mr-5"></i> Tải xuống đề thi</button></li>
        <li><button className="btn btn-link js-btn-edown" data-type="answer"><i className="fas fa-file-alt mr-5"></i> Tải xuống đáp án đề thi</button></li>
        <li><button className="btn btn-link js-btn-edown" data-type="full"><i className="fas fa-file-invoice mr-5"></i> Tải xuống đề thi kèm đáp án chi tiết</button></li>
      </ul>
    </div>
  );
};

export default DownloadButton;
