import React, { useState, useCallback, useRef } from 'react';
import QRCode from 'qrcode.react';

const QrCodeButton = ({ title }) => {
  const [qrCreated, setQrCreated] = useState(false);
  const qrContainerRef = useRef(null);

  const createQr = useCallback(() => {
    if (!qrCreated) setQrCreated(true);
  }, [qrCreated]);

  const downloadQr = () => {
    const canvas = qrContainerRef.current?.querySelector('canvas');
    if (canvas) {
      const pngUrl = canvas.toDataURL("image/png");
      const link = document.createElement('a');
      link.href = pngUrl;
      link.download = 'qr-code.png';
      document.body.appendChild(link);
      link.click();
      link.remove();
    }
  };

  return (
    <div className="dropdown">
      <button
        id="dropdownQrButton"
        className="btn btn-copy w-100 dropdown-toggle"
        data-toggle="dropdown"
        aria-haspopup="true"
        aria-expanded="false"
        onClick={createQr}
      >
        <i className="fas fa-qrcode mr-2"></i> Quét QR<span className="text-lg-none">code</span>
      </button>
      <div className="dropdown-menu" aria-labelledby="dropdownQrButton" style={{ left: 'auto', right: 0 }}>
        <div className="dropdown-item text-center box-qr" ref={qrContainerRef}>
          {qrCreated
            ? <>
                <QRCode
                  value={window.location.href}
                  size={200}
                  title={title || 'Mã QR chia sẻ'}
                  includeMargin
                />
                <button className="btn btn-link" onClick={downloadQr}>
                  <i className="fas fa-save mr-2"></i> Tải về máy
                </button>
              </>
            : <i className="fa fa-spinner fa-spin"></i>
          }
        </div>
      </div>
    </div>
  );
};

export default QrCodeButton;
