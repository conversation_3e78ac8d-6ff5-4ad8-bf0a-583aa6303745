import React, { useEffect, useState, useCallback, useRef, useMemo } from "react";

import CubeLoading from '../components/CubeLoading';
import Timer from '../components/Timer';
import Blank from './components/Blank';
import Essay from './components/Essay';
import Multiplechoice from './components/Multiplechoice';

import size from 'lodash/size';
import debounce from 'lodash/debounce';

const Exam = ({ lectureid, avatar, back }) => {
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [exam, setExam] = useState(null);
    const [questions, setQuestions] = useState([]);
    const [currentTest, setCurrentTest] = useState(null);
    const [showModal, setShowModal] = useState(false);
    const [dataLog, setDataLog] = useState({});
    const [submiting, setSubmiting] = useState(false);
    const timer = window.TIMER || 0;

    const timerRef = useRef();

    const fetchQuestions = useCallback(async () => {
        try {
            setLoading(true);
            const res = await axios.post(`/exam/${lectureid}/play`);

            if (res?.data?.questions) {
                setQuestions(res.data.questions);
                setExam(res.data.exam || null);
                setCurrentTest(res.data.currentTest || null);
                setLoading(false);
            } else {
                setError('Không lấy được danh sách câu hỏi! Vui lòng tải lại trang.');
            }
        } catch (error) {
            console.error("Error fetching questions:", error);

            setError(error?.response?.data?.error || 'Đã xảy ra lỗi! Vui lòng tải lại trang.');
        }
    }, [lectureid]);

    useEffect(() => {
        fetchQuestions();
    }, [fetchQuestions]);

    useEffect(() => {
        const handleBeforeUnload = (event) => {
            if (!submiting) {
                event.preventDefault();
                event.returnValue = '';
            }
        };

        window.addEventListener('beforeunload', handleBeforeUnload);

        return () => {
            window.removeEventListener('beforeunload', handleBeforeUnload);
        };
    }, [submiting]);

    const debouncedOnAnswer = useMemo(() => {
        if (submiting) return;

        return debounce((answerData, question) => {
            setDataLog(prev => {
                const ans = answerData?.answer;
                const isEmpty =
                !answerData ||
                size(answerData) === 0 ||
                ans == null ||
                (Array.isArray(ans) ? ans.length === 0 : typeof ans === "object" && Object.keys(ans).length === 0);

                // Xoá log nếu rỗng
                if (isEmpty) {
                    const { [question.id]: _, ...rest } = prev;

                    return rest;
                }
                // Cập nhật / ghi đè
                if (prev[question.id] === answerData) return prev; // tránh set lại cùng ref

                return { ...prev, [question.id]: answerData };
            });
        }, 500);
    }, [submiting]);

    useEffect(() => {
        return () => {
            debouncedOnAnswer?.cancel?.();
        };
    }, [debouncedOnAnswer]);

    const handleConfirm = () => {
        setShowModal(false);
        handleSubmitExam();
    };

    const handleCancel = () => {
        setSubmiting(false);
        setShowModal(false);
    };

    const submitExam = () => {
        if (Object.keys(dataLog).length == 0) {
            alert('Bạn chưa trả lời câu hỏi nào.')

            return;
        }

        setSubmiting(true);
        setShowModal(true);
    }

    const handleSubmitExam = () => {
        const currentTimer = timerRef.current ? timerRef.current.getTime() : null;

        axios({
            method: 'POST',
            url: `/exam/${currentTest.id}/submit`,
            data: {
                data_log: dataLog,
                time: timer,
                timer: currentTimer,
            }
        })
        .then(function (res) {
            window.location.href = res?.data?.backUrl || back;
        })
        .catch(function (error) {
            console.log(error);
            let errorMessage = 'Đã xảy ra lỗi! Vui lòng thử lại.';

            if (error.response && error.response.status === 422) {
                const { errors = {} } = error.response.data;

                errorMessage = '<ul>' +
                    Object.keys(errors).map(key => {
                        // Map từng key và lấy phần tử đầu tiên của mỗi mảng lỗi
                        return `<li>${errors[key][0]}</li>`;
                    }).join('') +
                '</ul>';
            } else if (error.response && error.response.data && error.response.data.message) {
                errorMessage = error.response.data.message;
            }

            $.notify({ message: errorMessage },{
                element: 'body',
                type: 'danger',
                placement: { from: 'top', align: 'center' },
                delay: 2000,
                allow_dismiss: false
            });

            setSubmiting(false);
        })
    }

    const scrollToQuestion = (id) => {
        const el = document.getElementById(`ique-${id}`);
        if (!el) return;
        el.scrollIntoView({ behavior: 'smooth', block: 'start' });
        el.classList.add('highlight');
        setTimeout(() => el.classList.remove('highlight'), 1000);
    };

    const renderQuestion = (question, stt) => {
        const paragraphHint = (question.text_select !== null && Array.isArray(exam.quiz_content) && exam?.quiz_content?.[Number(question.text_select) + 1]) ? (
            <>
                <p style={{ fontSize: '16px', color: 'red' }}>
                    <i className="far fa-hand-point-right mr-5"></i>
                    Bạn hãy đọc <strong style={{ color: "#337ab7" }}>[Đoạn văn]</strong> sau và trả lời câu hỏi:
                </p>
                <div className="panel-group">
                    <div className="panel panel-default">
                        <div className="panel-heading">
                            <h4 className="panel-title">
                                <a className="d-block" data-toggle="collapse" href={`#paragraph-${question.id}`}>
                                    Bài đọc
                                </a>
                            </h4>
                        </div>
                        <div id={`paragraph-${question.id}`} className="panel-collapse collapse">
                            <div className="panel-body">
                                <div
                                    className="is-paragraph border-0 fs-18 p-0"
                                    dangerouslySetInnerHTML={{ __html: exam.quiz_content[Number(question.text_select) + 1].content || '' }}
                                ></div>
                            </div>
                        </div>
                    </div>
                </div>
            </>
        ) : null;

        switch (question.type) {
            case 'blank':
                return (
                    <>
                        <div className="mcq-number">{stt}</div>
                        <Blank
                            key={question.id}
                            question={question}
                            paragraph={paragraphHint}
                            isSubmit={submiting}
                            onSubmit={debouncedOnAnswer}
                            vip={false}
                            buyviphtml=""
                        />
                    </>
                );
            case 'test':
                return (
                    <>
                        <div className="mcq-number">{stt}</div>
                        <Multiplechoice
                            key={question.id}
                            question={question}
                            paragraph={paragraphHint}
                            isSubmit={submiting}
                            onSubmit={debouncedOnAnswer}
                            vip={false}
                            buyviphtml=""
                        />
                    </>
                );
            case 'essay':
                return (
                    <>
                        <div className="mcq-number">{stt}</div>
                        <Essay
                            key={question.id}
                            question={question}
                            paragraph={paragraphHint}
                            isSubmit={submiting}
                            onSubmit={debouncedOnAnswer}
                            vip={false}
                            buyviphtml=""
                        />
                    </>
                );
            default:
                return null;
        }
    };

    return (
        <div className="exam-wrapper">
            <div className="main-page">
                <div className="box-wrapper">
                    <div className="box-wrapper-header">
                        <a className="btn btn-default leave-site" href={back}>
                            <i className="fas fa-arrow-left"></i>
                        </a>
                        <Timer
                            ref={timerRef}
                            initTimer={timer}
                            isPaused={submiting}
                            onPause={() => setSubmiting(true)}
                            onSubmitExam={handleSubmitExam}
                        />
                        <div className="img-user">
                            <img src={avatar} alt="Avatar" />
                        </div>
                    </div>
                    <div className="box-wrapper-body container">
                        <div className="exam-wrapper">
                            { loading ? (
                                <div  className="pt-30">
                                    <CubeLoading loadingIs={true} message={error} />
                                </div>
                            ) : (
                                <div className="exam-box">
                                    {questions.map((question, index) => (
                                        <div
                                            key={question.id}
                                            className="que-card"
                                            id={`ique-${question.id}`}
                                        >
                                            { renderQuestion(question, index + 1) }
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>
                    {questions.length > 0 && (
                        <div className="navigation-mb">
                            <span>•</span>
                            <button type="button" className="btn btn-warning submit-test btn-lg" onClick={submitExam} disabled={submiting}>
                                <i className="far fa-paper-plane"></i>
                                <span>NỘP BÀI</span>
                            </button>
                            <button type="button" className="btn btn-link toggle-lsidebar">
                                <i className="fas fa-list-ul mr-5"></i>
                            </button>
                        </div>
                    )}
                </div>
                <aside className="lsidebar" id="lsidebar">
                    <div className="lsidebar-main">
                        <div className="lsidebar-wrapper">
                            <button className="btn btn-default btn-lsidebar toggle-lsidebar btn-rsidebar" type="button">
                                <i className="fas fa-indent"></i>
                            </button>
                            <div className="lsidebar-body">
                                <div className="lsidebar-title pl-20">
                                    Danh sách câu hỏi
                                </div>
                                <div className="lsidebar-nav scroll-tree">
                                    <div className="exam-stt">
                                        {questions.map((question, index) => {
                                            const isAnswered = !!dataLog[question.id];

                                            return (
                                                <span
                                                    key={question.id}
                                                    className={`stt ${isAnswered ? 'done' : ''}`}
                                                    onClick={() => scrollToQuestion(question.id)}
                                                >
                                                    {index + 1}
                                                </span>
                                            );
                                        })}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </aside>
            </div>
            { showModal && (
                <>
                    <div className="modal fade in" style={{ display: "block" }}>
                        <div className="modal-dialog" style={{ maxWidth: 500 }}>
                            <div className="modal-content">
                            <div className="modal-header">
                                <button type="button" className="close" onClick={handleCancel}>
                                &times;
                                </button>
                                <h4 className="modal-title">Xác nhận nộp bài</h4>
                            </div>
                            <div className="modal-body">
                                { (questions.length > 0 && Object.keys(dataLog).length == questions.length) ?
                                    <p className="text-info">Rất tốt! Bạn đã hoàn thành tất cả các câu hỏi.</p> :
                                    <p className="red">Bạn còn <strong>{questions.length - Object.keys(dataLog).length}</strong> câu hỏi chưa hoàn thành. Hãy chắc chắn với quyết định của mình!</p>
                                }
                            </div>
                            <div className="modal-footer">
                                <button className="btn btn-default pull-left" onClick={handleCancel}>
                                Tiếp tục làm
                                </button>
                                <button className="btn btn-success" onClick={handleConfirm}>
                                Nộp bài
                                </button>
                            </div>
                            </div>
                        </div>
                    </div>
                    <div className="modal-backdrop fade in"></div>
                </>
            ) }
        </div>
    );
};

export default Exam;
