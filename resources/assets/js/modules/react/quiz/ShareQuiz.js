import CopyButton from './components/CopyButton';
import QrCodeButton from './components/QrCodeButton';
import DownloadButton from './components/DownloadButton';
import LikeButton from './components/LikeButton';

const ShareQuiz = ({ auth, examid, title = null, liked = 0, candownload = 1 }) => {
    return (
        <div className="box-shareq">
            <p className="q-separator mt-5 mb-5 ml-10 mr-10 w-100">
                <span style={{ color: '#666f74',  }}>
                   <i className="far fa-share-square"></i> Chia sẻ { candownload == 1 ? 'đề thi' : '' }
                </span>
            </p>
            <div className="box-shareq-body">
                <div className="bgroup-action">
                    <CopyButton />
                    <QrCodeButton title={title} />
                </div>
                { candownload == 1 && (
                    <>
                        <p className="q-separator mt-5 mb-5 ml-10 mr-10 text-lg-none w-100">
                          <span style={{ color: '#666f74' }}>hoặc tải đề</span>
                        </p>
                        <div className="bgroup-action">
                            <DownloadButton auth={auth} examid={examid} title={title} />
                            <LikeButton auth={auth} examid={examid} liked={liked} />
                        </div>
                    </>
                ) }
            </div>
        </div>
    );
};

export default ShareQuiz;
