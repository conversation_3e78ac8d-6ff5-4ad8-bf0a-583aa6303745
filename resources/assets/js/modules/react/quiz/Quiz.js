import React, { useState, useEffect, useLayoutEffect, useCallback, useRef, useMemo } from 'react';
import Flashcard from './components/Flashcard';
import Blank from './components/Blank';
import Essay from './components/Essay';
import Multiplechoice from './components/Multiplechoice';
import size from 'lodash/size';
import debounce from 'lodash/debounce';
import { vjScrollSlider } from '../../vj-scrollslider';

const QLIMIT = 5;

const Quiz = ({ vip, buyviphtml = '', auth, action, lectureaction = null }) => {
    const [actionType, setActionType] = useState('flashcard');
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isSubmit, setIsSubmit] = useState(false);
    const [direction, setDirection] = useState(1);
    const [done, setDone] = useState(false);
    const [activeParagraph, setActiveParagraph] = useState(null);
    const [dataLog, setDataLog] = useState({});

    const quizRef = useRef(null);
    const touchStartXRef = useRef(null);
    const touchStartYRef = useRef(null);

    useEffect(() => {
        const rootElement = document.getElementById('initQuiz');

        if (rootElement) {
            rootElement.classList.add('quiz-initialied');
        }

        if (quizRef.current) {
            quizRef.current.focus();
        }

        vjScrollSlider();
    }, []);

    useEffect(() => {
        const handleKeyDown = debounce((e) => {
            if (document.activeElement.tagName === 'INPUT' ||
                document.activeElement.tagName === 'TEXTAREA' ||
                actionType === 'practice') {
                return;
            }

            switch (e.key) {
                case 'ArrowLeft':
                case 'ArrowDown':
                    e.preventDefault();
                    handleNext(-1);
                    break;
                case 'ArrowRight':
                case 'ArrowUp':
                    e.preventDefault();
                    handleNext(1);
                    break;
                default:
                    break;
            }
        }, 300);

        window.addEventListener('keydown', handleKeyDown);

        return () => {
            window.removeEventListener('keydown', handleKeyDown);
            handleKeyDown.cancel();
        };
    }, [handleNext, actionType]);

    const questionsRef = useRef(
        Array.isArray(window.INIT_QUES) ? window.INIT_QUES : []
    );
    const paragraphsRef = useRef(
        Array.isArray(window.INIT_PARAGRAPHS) ? window.INIT_PARAGRAPHS : []
    );
    const questions = questionsRef.current;
    const paragraphs = paragraphsRef.current;

    if (questions.length === 0) {
        return <div>No questions available.</div>;
    }

    useLayoutEffect(() => {
        if (quizRef.current && currentIndex > 0) {
            quizRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }

        const currentQuestion = questions[currentIndex];

        setActiveParagraph(currentQuestion?.text_select || null);
    }, [currentIndex]);

    const handleNext = useCallback((dir) => {
        setDirection(dir);
        setIsSubmit(false);

        setCurrentIndex(prevIndex => {
            const newIndex = prevIndex + dir;
            if (newIndex >= questions.length) {
                setDone(true);
                return prevIndex;
            }

            if (newIndex >= 0 && newIndex < questions.length) {
                return newIndex;
            }

            return prevIndex;
        });
    }, [questions.length]);

    const playAgain = useCallback(() => {
        if (done) {
            resetQuiz();
        }
    }, [done]);

    const changeActionType = (type) => {
        setActionType(type);
        resetQuiz();
    };

    const resetQuiz = () => {
        setDone(false);
        setCurrentIndex(0);
        setIsSubmit(false);
        setDataLog({});
    }

    const handleSubmit = useCallback((answerData, question) => {
        if (isSubmit) return;

        setDataLog(prev => {
            // Xoá log nếu rỗng
            if (!answerData || size(answerData) === 0) {
                if (prev[question.id]) {
                const { [question.id]: _, ...rest } = prev; // remove key nhanh, không mutate
                return rest;
                }
                return prev;
            }
            // Cập nhật / ghi đè
            if (prev[question.id] === answerData) return prev; // tránh set lại cùng ref
            return { ...prev, [question.id]: answerData };
        });

        setIsSubmit(true);
    }, [isSubmit]);

    const handleTouchStart = (e) => {
        touchStartXRef.current = e.changedTouches[0].clientX;
        touchStartYRef.current = e.changedTouches[0].clientY;
    };

    const handleTouchEnd = (e) => {
        const touchEndX = e.changedTouches[0].clientX;
        const touchEndY = e.changedTouches[0].clientY;

        const deltaX = touchEndX - touchStartXRef.current;
        const deltaY = touchEndY - touchStartYRef.current;

        const absDeltaX = Math.abs(deltaX);
        const absDeltaY = Math.abs(deltaY);

        const threshold = 50; // Minimum distance required to consider it a swipe

        if (absDeltaX > absDeltaY && absDeltaX > threshold) {
            // Horizontal swipe detected
            if (deltaX > 0) {
                // Swiped right
                handleNext(-1);
            } else {
                // Swiped left
                handleNext(1);
            }
        }
    };

    const { correctCount, wrongCount } = useMemo(() => {
        let correct = 0, wrong = 0;

        for (const id in dataLog) {
            const log = dataLog[id];

            if (log?.isCorrect === true) correct++;
            else if (log?.isCorrect === false) wrong++;
        }

        return { correctCount: correct, wrongCount: wrong };
    }, [dataLog]);

    const renderQuiz = () => {
        const currentQuestion = questions[currentIndex];
        const isVip = vip ? true : currentIndex < QLIMIT;

        if (currentQuestion) {
            const paragraphHint = currentQuestion.text_select !== null ? (
                <p style={{ fontSize: '16px', color: 'red' }}>
                    <i className="far fa-hand-point-right mr-5"></i>
                    Bạn hãy đọc <strong style={{ color: "#337ab7" }}>[Đoạn văn {Number(currentQuestion.text_select) + 1}]</strong> và trả lời câu hỏi:
                </p>
            ) : null;

            switch (actionType) {
                case 'flashcard':
                    const percent = ((currentIndex + 1) / questions.length) * 100;

                    return (
                        <div
                            onTouchStart={handleTouchStart}
                            onTouchEnd={handleTouchEnd}
                        >
                            <Flashcard
                                key={currentQuestion.id}
                                question={currentQuestion}
                                paragraph={paragraphHint}
                                direction={direction}
                                isSubmit={isSubmit}
                                onSubmit={handleSubmit}
                                vip={isVip}
                                buyviphtml={buyviphtml}
                            />
                            <div className="quiz-footer">
                                <div className="quiz-controll">
                                    <button className="btn btn-default" onClick={() => handleNext(-1)} disabled={currentIndex === 0}>
                                        <i className="fas fa-arrow-left"></i>
                                    </button>
                                    <span className="text-muted">
                                        {currentIndex + 1} / {questions.length}
                                    </span>
                                    <button className="btn btn-default" onClick={() => handleNext(1)}>
                                        <i className="fas fa-arrow-right"></i>
                                    </button>
                                </div>
                                <div className="q-progress q-progress-bar px-3">
                                    <div className="q-progress-main">
                                      <div
                                        className="q-progress-bar q-progress-active"
                                        data-percent={percent}
                                        style={{ width: `${percent}%`, transition: 'none 0s ease 0s', height: '3px' }}
                                      ></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    );
                    break;
                case 'practice':
                    const correctPercent = questions.length ? (correctCount / questions.length) * 100 : 0;

                    return (
                        <>
                            <div className="quiz-heard" style={{ alignItems: 'end' }}>
                                <span className="q-number correct-count">{correctCount}</span>
                                <div className="q-progress q-progress-bar mt-0">
                                    <div className="q-progress-label2 text-white">
                                        <span style={{ width: `${correctPercent}%`, color: '#FF5722', display: 'block', textAlign: 'right', fontSize: '22px' }}>
                                            <i className="fas fa-running"></i>
                                        </span>
                                    </div>
                                    <div className="q-progress-main">
                                      <div
                                        className="q-progress-bar q-progress-active"
                                        data-percent={correctPercent}
                                        style={{ width: `${correctPercent}%`, transition: 'none 0s ease 0s' }}
                                      >
                                      </div>
                                    </div>
                                </div>
                                <span className="q-number">{questions.length}</span>
                            </div>
                            {(() => {
                                switch (currentQuestion.type) {
                                    case 'blank':
                                        return <Blank
                                            key={currentQuestion.id}
                                            question={currentQuestion}
                                            paragraph={paragraphHint}
                                            isSubmit={isSubmit}
                                            hasCheck={true}
                                            onSubmit={handleSubmit}
                                            vip={isVip}
                                            buyviphtml={buyviphtml}
                                            saveData={dataLog[currentQuestion.id]}
                                        />;
                                    case 'test':
                                        return <Multiplechoice
                                            key={currentQuestion.id}
                                            question={currentQuestion}
                                            paragraph={paragraphHint}
                                            isSubmit={isSubmit}
                                            hasCheck={true}
                                            onSubmit={handleSubmit}
                                            vip={isVip}
                                            buyviphtml={buyviphtml}
                                            saveData={dataLog[currentQuestion.id]}
                                        />;
                                    case 'essay':
                                        return <Essay
                                            key={currentQuestion.id}
                                            question={currentQuestion}
                                            paragraph={paragraphHint}
                                            isSubmit={isSubmit}
                                            hasCheck={true}
                                            onSubmit={handleSubmit}
                                            vip={isVip}
                                            buyviphtml={buyviphtml}
                                            saveData={dataLog[currentQuestion.id]}
                                        />;
                                    default:
                                        return null;
                                }
                            })()}
                            { currentIndex < questions.length && isSubmit && (
                                <button className="btn btn-info btn-nextq ani" onClick={() => handleNext(1)}>
                                    Tiếp theo »
                                </button>
                            ) }
                        </>
                    );
                    break;
            }
        }

        return <p className="text-center">-- Câu hỏi không xác định. Vui lòng thử lại --</p>;
    };

    return (
        <div className="quiz-card">
            <div className="quiz-heard">
                <div className="quizmode">
                    <button className={`btn btn-default quizmode-btn ${actionType === 'flashcard' ? 'active' : ''}`} onClick={() => changeActionType('flashcard')}>
                        <svg className="quizmode-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 32 32" role="presentation">
                            <path d="M8.155 12.824C8.155 11.264 9.4 10 10.939 10h16.704c1.538 0 2.784 1.264 2.784 2.824v12.351c0 1.56-1.246 2.825-2.784 2.825H10.94c-1.538 0-2.784-1.265-2.784-2.825v-12.35Z" fill="#51CFFF"></path>
                            <path d="M2.428 6.825C2.428 5.265 3.68 4 5.228 4h16.8c1.546 0 2.8 1.265 2.8 2.825v12.35c0 1.56-1.254 2.825-2.8 2.825h-16.8c-1.547 0-2.8-1.265-2.8-2.825V6.825Z" fill="#4255FF"></path>
                        </svg>
                        Thẻ ghi nhớ
                    </button>
                    <button className={`btn btn-default quizmode-btn ${actionType === 'practice' ? 'active' : ''}`} onClick={() => changeActionType('practice')}>
                        <svg className="quizmode-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 32 32" role="presentation">
                            <path fillRule="evenodd" clipRule="evenodd" d="M13.804 5.364c-1.148-1.483-3.346-1.801-4.911-.734-1.159.79-1.666 2.132-1.391 3.387-3.734 3.528-4.67 9.113-1.892 13.63 2.745 4.464 8.253 6.485 13.307 5.298 1.198 1.214 3.19 1.414 4.64.425 1.58-1.078 1.945-3.178.784-4.677-1.148-1.483-3.346-1.801-4.91-.734a3.3 3.3 0 0 0-1.44 2.314c-3.785.793-7.831-.76-9.852-4.046-2.008-3.264-1.428-7.285 1.154-9.948a3.703 3.703 0 0 0 3.727-.238c1.58-1.078 1.944-3.178.784-4.677ZM10.697 7.04c.2-.136.48-.086.612.**************.38-.093.507a.464.464 0 0 1-.612-.084c-.12-.155-.092-.381.093-.507Zm10.537 17.329c.2-.136.48-.086.612.**************.381-.093.507a.464.464 0 0 1-.612-.084c-.12-.155-.092-.381.093-.507Z" fill="#4255FF"></path>
                            <path d="M17.068 4.68c-.76-.056-1.455.478-1.518 1.234-.063.759.539 1.391 1.3 1.447.49.036.976.109 1.454.218.74.168 1.512-.255 1.701-.987.191-.74-.297-1.459-1.043-1.629a12.753 12.753 0 0 0-1.894-.283ZM23.595 7.087c-.605-.452-1.485-.361-1.972.214a1.302 1.302 0 0 0 .228 1.902c.382.285.74.6 1.069.94a1.464 1.464 0 0 0 1.982.092A1.3 1.3 0 0 0 25 8.323a11.59 11.59 0 0 0-1.405-1.236ZM27.567 12.55c-.236-.717-1.031-1.094-1.76-.883-.735.213-1.175.962-.936 1.688.144.438.25.886.317 1.338.11.75.835 1.244 1.59 1.146.757-.098 1.319-.765 1.208-1.519a10.564 10.564 0 0 0-.419-1.77ZM26.635 17.449c-.73-.207-1.523.175-1.755.894a8.32 8.32 0 0 1-.534 1.28c-.353.681-.043 1.489.655 1.808.688.315 1.531.053 1.878-.617.282-.542.517-1.105.703-1.683.234-.728-.21-1.473-.947-1.682Z" fill="#51CFFF"></path>
                        </svg>
                        Luyện tập
                    </button>
                    { auth ? (
                        <a className="btn btn-default quizmode-btn" href={ action || '#' }>
                            <svg className="quizmode-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 32 32" role="presentation">
                                <path d="M5.333 5a2.333 2.333 0 0 1 2.334-2.333h16.666A2.333 2.333 0 0 1 26.668 5v22a2.333 2.333 0 0 1-2.334 2.333H7.668A2.333 2.333 0 0 1 5.334 27V5Z" fill="#FF983A"></path>
                                <path d="m9.505 10.151-.695-.727a.4.4 0 0 0-.582 0 .426.426 0 0 0 0 .587l.987 1.032a.4.4 0 0 0 .582 0l2.498-2.611a.426.426 0 0 0 0-.587.4.4 0 0 0-.58 0l-2.21 2.306Z" fill="#fff"></path>
                                <path fillRule="evenodd" clipRule="evenodd" d="M12.494 7.654a.703.703 0 0 1 0 .968l-2.498 2.611a.675.675 0 0 1-.979 0l-.987-1.031a.703.703 0 0 1 0-.969c.268-.28.71-.28.979 0l.496.52 2.01-2.1c.269-.28.711-.28.98 0Zm-.78.19a.4.4 0 0 1 .582 0 .426.426 0 0 1 0 .588l-2.499 2.61a.4.4 0 0 1-.581 0l-.987-1.031a.426.426 0 0 1 0-.587.4.4 0 0 1 .581 0l.695.727 2.21-2.306Z" fill="#fff"></path>
                                <path d="m9.505 16.733-.695-.727a.4.4 0 0 0-.582 0 .426.426 0 0 0 0 .587l.987 1.032a.4.4 0 0 0 .582 0l2.498-2.611a.426.426 0 0 0 0-.587.4.4 0 0 0-.58 0l-2.21 2.306Z" fill="#fff"></path>
                                <path fillRule="evenodd" clipRule="evenodd" d="M12.494 14.236a.703.703 0 0 1 0 .969l-2.498 2.61a.675.675 0 0 1-.979 0l-.987-1.03a.703.703 0 0 1 0-.97c.268-.28.71-.28.979 0l.496.52 2.01-2.099c.269-.28.711-.28.98 0Zm-.78.19a.4.4 0 0 1 .582.001.426.426 0 0 1 0 .587l-2.499 2.61a.4.4 0 0 1-.581 0l-.987-1.03a.426.426 0 0 1 0-.588.4.4 0 0 1 .581 0l.695.727 2.21-2.306Z" fill="#fff"></path>
                                <path d="M7.836 22.667c0-.737.597-1.334 1.334-1.334h5.333c.736 0 1.333.597 1.333 1.334v2.666c0 .737-.597 1.334-1.333 1.334H9.17a1.333 1.333 0 0 1-1.334-1.334v-2.666Z" fill="#fff"></path>
                                <rect x="14.991" y="8.11" width="9.333" height="2.667" rx="1.333" fill="#fff"></rect>
                                <rect x="14.991" y="14.693" width="9.333" height="2.667" rx="1.333" fill="#fff"></rect>
                                <path d="m27.663 23.803-2.611 1a.468.468 0 0 0-.28.28l-1.015 2.6a.493.493 0 0 1-.922 0l-1.002-2.607a.491.491 0 0 0-.28-.286l-2.57-1.013a.495.495 0 0 1-.23-.74.494.494 0 0 1 .23-.18l2.61-.994a.506.506 0 0 0 .287-.286l1.009-2.6a.499.499 0 0 1 .743-.227.5.5 0 0 1 .185.227l.995 2.613a.494.494 0 0 0 .287.28l2.604 1.013a.494.494 0 0 1-.04.92Z" fill="#4255FF"></path>
                            </svg>
                            Thi thử
                        </a>
                    ) : (
                        <a className="btn btn-default quizmode-btn btn-popup js-auth" href="#login-box">
                            <svg className="quizmode-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 32 32" role="presentation">
                                <path d="M5.333 5a2.333 2.333 0 0 1 2.334-2.333h16.666A2.333 2.333 0 0 1 26.668 5v22a2.333 2.333 0 0 1-2.334 2.333H7.668A2.333 2.333 0 0 1 5.334 27V5Z" fill="#FF983A"></path>
                                <path d="m9.505 10.151-.695-.727a.4.4 0 0 0-.582 0 .426.426 0 0 0 0 .587l.987 1.032a.4.4 0 0 0 .582 0l2.498-2.611a.426.426 0 0 0 0-.587.4.4 0 0 0-.58 0l-2.21 2.306Z" fill="#fff"></path>
                                <path fillRule="evenodd" clipRule="evenodd" d="M12.494 7.654a.703.703 0 0 1 0 .968l-2.498 2.611a.675.675 0 0 1-.979 0l-.987-1.031a.703.703 0 0 1 0-.969c.268-.28.71-.28.979 0l.496.52 2.01-2.1c.269-.28.711-.28.98 0Zm-.78.19a.4.4 0 0 1 .582 0 .426.426 0 0 1 0 .588l-2.499 2.61a.4.4 0 0 1-.581 0l-.987-1.031a.426.426 0 0 1 0-.587.4.4 0 0 1 .581 0l.695.727 2.21-2.306Z" fill="#fff"></path>
                                <path d="m9.505 16.733-.695-.727a.4.4 0 0 0-.582 0 .426.426 0 0 0 0 .587l.987 1.032a.4.4 0 0 0 .582 0l2.498-2.611a.426.426 0 0 0 0-.587.4.4 0 0 0-.58 0l-2.21 2.306Z" fill="#fff"></path>
                                <path fillRule="evenodd" clipRule="evenodd" d="M12.494 14.236a.703.703 0 0 1 0 .969l-2.498 2.61a.675.675 0 0 1-.979 0l-.987-1.03a.703.703 0 0 1 0-.97c.268-.28.71-.28.979 0l.496.52 2.01-2.099c.269-.28.711-.28.98 0Zm-.78.19a.4.4 0 0 1 .582.001.426.426 0 0 1 0 .587l-2.499 2.61a.4.4 0 0 1-.581 0l-.987-1.03a.426.426 0 0 1 0-.588.4.4 0 0 1 .581 0l.695.727 2.21-2.306Z" fill="#fff"></path>
                                <path d="M7.836 22.667c0-.737.597-1.334 1.334-1.334h5.333c.736 0 1.333.597 1.333 1.334v2.666c0 .737-.597 1.334-1.333 1.334H9.17a1.333 1.333 0 0 1-1.334-1.334v-2.666Z" fill="#fff"></path>
                                <rect x="14.991" y="8.11" width="9.333" height="2.667" rx="1.333" fill="#fff"></rect>
                                <rect x="14.991" y="14.693" width="9.333" height="2.667" rx="1.333" fill="#fff"></rect>
                                <path d="m27.663 23.803-2.611 1a.468.468 0 0 0-.28.28l-1.015 2.6a.493.493 0 0 1-.922 0l-1.002-2.607a.491.491 0 0 0-.28-.286l-2.57-1.013a.495.495 0 0 1-.23-.74.494.494 0 0 1 .23-.18l2.61-.994a.506.506 0 0 0 .287-.286l1.009-2.6a.499.499 0 0 1 .743-.227.5.5 0 0 1 .185.227l.995 2.613a.494.494 0 0 0 .287.28l2.604 1.013a.494.494 0 0 1-.04.92Z" fill="#4255FF"></path>
                            </svg>
                            Thi thử
                        </a>
                    ) }
                    { lectureaction && (
                        <a className="btn btn-default quizmode-btn"  href={ lectureaction || '#' }>
                            <svg className="quizmode-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" role="presentation">
                                <path fillRule="evenodd" clipRule="evenodd" d="M21.8 8.02s-.196-1.372-.795-1.976c-.76-.793-1.613-.796-2.004-.842C16.202 5 12.004 5 12.004 5h-.008s-4.199 0-6.997.202c-.391.046-1.243.05-2.005.842C2.395 6.648 2.2 8.02 2.2 8.02S2 9.63 2 11.24v1.51c0 1.612.2 3.222.2 3.222s.195 1.372.794 1.976c.762.793 1.761.768 2.206.85C6.8 18.954 12 19 12 19s4.203-.006 7.001-.208c.39-.046 1.243-.05 2.004-.843.6-.604.795-1.976.795-1.976s.2-1.61.2-3.221v-1.51c0-1.611-.2-3.222-.2-3.222Z" fill="red"></path>
                                <path fillRule="evenodd" clipRule="evenodd" d="M9.935 14.581V8.99l5.403 2.806-5.403 2.786Z" fill="#fff"></path>
                            </svg>
                            Lý thuyết
                        </a>
                    ) }
                </div>
            </div>
            <div className="quiz-body" ref={quizRef} tabIndex="0">
                { done ? (
                    <>
                        <div className="pyro">
                            <div className="before"></div>
                            <div className="after"></div>
                        </div>
                        <div className="q-done">
                            <div className="q-noti">
                                <img src="/images/q-done.svg" />
                                <h3>Chúc mừng! Bạn đã ôn tập tất cả các câu hỏi.</h3>
                            </div>
                            <div className="row w-100">
                                <div className="col-md-6">
                                    <p className="h4">Kết quả của bạn</p>
                                    <div className="q-noti">
                                        <img src="/images/q-check.svg" />
                                        <div className="text-left w-100">
                                            <div className="q-check" style={{ background: '#00b3ff61', color: '#086c96' }}><span>Hoàn thành </span><span>{correctCount + wrongCount}</span></div>
                                            <div className="q-check"><span>Còn lại </span><span>{questions.length - correctCount - wrongCount}</span></div>
                                            { actionType == 'practice' && (
                                                <>
                                                    <div className="q-check" style={{ background: '#4cad5461', color: '#00860b' }}><span>Đúng </span><span>{correctCount}</span></div>
                                                    <div className="q-check" style={{ background: '#ff000047', color: '#c40a0a' }}><span>Sai </span><span>{wrongCount}</span></div>
                                                </>
                                            )}
                                        </div>
                                    </div>
                                </div>
                                <div className="col-md-6">
                                    <p className="h4">Bước tiếp theo</p>
                                    <button className="btn btn-info btn-nextq w-100" onClick={playAgain}>
                                        LÀM LẠI
                                    </button>
                                </div>
                            </div>
                        </div>
                    </>
                ) : (
                    <>
                        { paragraphs && paragraphs.length > 0 && (
                            <div className="paragraph-box mt-5 mb-5">
                                <div className="scroll-slider scroll-off">
                                    <span className="scroll-overlay"><i className="fas fa-angle-left"></i></span>
                                    <span className="scroll-overlay"><i className="fas fa-angle-right"></i></span>
                                    <div className="scroll-on">
                                        <ul className="nav nav-tabs">
                                            { paragraphs.map((paragraph, i) => {
                                                return (
                                                    <li key={i} className={`${ activeParagraph == i ? 'active' : '' }`}>
                                                        <a data-toggle="tab" href={`#paragraph${i}`} onClick={() => setActiveParagraph(i)}>
                                                            <span>Đoạn văn { i + 1 }</span>
                                                        </a>
                                                    </li>
                                                );
                                            }) }
                                        </ul>
                                    </div>
                                </div>
                                <div className="tab-content mt-10">
                                    { paragraphs.map((paragraph, i) => {
                                        return (
                                            <div key={i} id={`#paragraph${i}`} className={`tab-pane fade ${ activeParagraph == i ? 'in active' : '' }`}>
                                                <div
                                                    className="is-paragraph"
                                                    dangerouslySetInnerHTML={{ __html: paragraph.content || '' }}
                                                ></div>
                                            </div>
                                        );
                                    }) }
                                </div>
                            </div>
                        ) }
                        { renderQuiz() }
                    </>
                ) }
            </div>
        </div>
    );
};

export default Quiz;
