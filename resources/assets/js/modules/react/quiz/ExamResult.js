import React, { useState, useEffect, useLayoutEffect, useCallback, useRef, useMemo } from 'react';

import Blank from './components/Blank';
import Essay from './components/Essay';
import Multiplechoice from './components/Multiplechoice';

const ExamResult = ({ vip, buyviphtml = '' }) => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [direction, setDirection] = useState(1);

    useEffect(() => {
        const rootElement = document.getElementById('initExamResult');

        if (rootElement) {
            rootElement.classList.add('quiz-initialied');
        }
    }, []);

    const questionsRef = useRef(
        Array.isArray(window.INIT_QUES) ? window.INIT_QUES : []
    );
    const paragraphsRef = useRef(
        Array.isArray(window.INIT_PARAGRAPHS) ? window.INIT_PARAGRAPHS : []
    );
    const dataLogRef = useRef(
        typeof window.INIT_DATA_LOG === 'object' ? window.INIT_DATA_LOG : {}
    );

    const questions = questionsRef.current;
    const paragraphs = paragraphsRef.current;
    const dataLog = dataLogRef.current;

    if (questions.length === 0) {
        return <div>No questions available.</div>;
    }

    const handleNext = useCallback((dir) => {
        setDirection(dir);

        setCurrentIndex(prevIndex => {
            const newIndex = prevIndex + dir;

            if (newIndex >= questions.length) {
                return prevIndex;
            }

            if (newIndex >= 0 && newIndex < questions.length) {
                return newIndex;
            }

            return prevIndex;
        });
    }, [questions.length]);

    const renderQuiz = () => {
        const currentQuestion = questions[currentIndex];

        if (currentQuestion) {
            const paragraphHint = (currentQuestion.text_select !== null && paragraphs?.[Number(currentQuestion.text_select) + 1]) ? (
                <>
                    <p style={{ fontSize: '16px', color: 'red' }}>
                        <i className="far fa-hand-point-right mr-5"></i>
                        Bạn hãy đọc <strong style={{ color: "#337ab7" }}>[Đoạn văn]</strong> sau và trả lời câu hỏi:
                    </p>
                    <div className="panel-group">
                        <div className="panel panel-default">
                            <div className="panel-heading">
                                <h4 className="panel-title">
                                    <a className="d-block" data-toggle="collapse" href={`#paragraph-${currentQuestion.id}`}>
                                        Bài đọc
                                    </a>
                                </h4>
                            </div>
                            <div id={`paragraph-${currentQuestion.id}`} className="panel-collapse collapse">
                                <div className="panel-body">
                                    <div
                                        className="is-paragraph border-0 fs-18 p-0"
                                        dangerouslySetInnerHTML={{ __html: paragraphs[Number(currentQuestion.text_select) + 1].content || '' }}
                                    ></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </>
            ) : null;

            return (
                <>
                    {(() => {
                        switch (currentQuestion.type) {
                            case 'blank':
                                return <Blank
                                    key={currentQuestion.id}
                                    question={currentQuestion}
                                    paragraph={paragraphHint}
                                    isSubmit={true}
                                    hasCheck={false}
                                    vip={vip}
                                    buyviphtml={buyviphtml}
                                    saveData={dataLog[currentQuestion.id]}
                                />;
                            case 'test':
                                return <Multiplechoice
                                    key={currentQuestion.id}
                                    question={currentQuestion}
                                    paragraph={paragraphHint}
                                    isSubmit={true}
                                    hasCheck={true}
                                    vip={vip}
                                    buyviphtml={buyviphtml}
                                    saveData={dataLog[currentQuestion.id]}
                                />;
                            case 'essay':
                                return <Essay
                                    key={currentQuestion.id}
                                    question={currentQuestion}
                                    paragraph={paragraphHint}
                                    isSubmit={true}
                                    hasCheck={false}
                                    vip={vip}
                                    buyviphtml={buyviphtml}
                                    saveData={dataLog[currentQuestion.id]}
                                />;
                            default:
                                return null;
                        }
                    })()}
                    <div className="quiz-footer">
                        <div className="quiz-controll">
                            <button className="btn btn-default" onClick={() => handleNext(-1)} disabled={currentIndex === 0}>
                                <i className="fas fa-arrow-left"></i>
                            </button>
                            <span className="text-muted">
                                {currentIndex + 1} / {questions.length}
                            </span>
                            <button className="btn btn-default" onClick={() => handleNext(1)}>
                                <i className="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>
                </>
            );
        }

        return <p className="text-center">-- Câu hỏi không xác định. Vui lòng thử lại --</p>;
    };

    return (
        <>
            <h4 className="mt-20">
                Chi tiết bài làm:
            </h4>
            <div className="exam-stt">
                {questions.map((question, index) => {
                    const cls = dataLog[question.id] ? (dataLog[question.id].isCorrect ? 'correct' : 'wrong') : '';

                    return (
                        <span
                            key={question.id}
                            className={`stt ${cls}`}
                            onClick={() => setCurrentIndex(index)}
                        >
                            {index + 1}
                        </span>
                    );
                })}
            </div>
            { renderQuiz() }
        </>
    );
};

export default ExamResult;
