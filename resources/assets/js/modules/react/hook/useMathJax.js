import { useEffect } from 'react';

// const renderMathJax = function () {
//   if (window.MathJax) {
//     setTimeout(() => {
//       window.MathJax.Hub.Queue(['Typeset', window.MathJax.Hub]);
//       console.log("MathJax rendered");
//     }, 0);
//   }
// }

export default function useMathJax(containerRef, deps = []) {
  useEffect(() => {
    const Hub = window.MathJax && window.MathJax.Hub;
    const el = containerRef?.current;
    if (!Hub || !el) return;

    let cancelled = false;

    // dọn jax cũ trong container (nếu có)
    try { Hub.getAllJax(el).forEach(j => j.<PERSON>move()); } catch {}

    // chỉ typeset container hiện tại
    Hub.Queue(() => {
      if (cancelled) return;
      if (!document.body.contains(el)) return;
      Hub.Typeset(el);
    });

    return () => {
      cancelled = true;
      try { Hub.getAllJax(el).forEach(j => j.<PERSON><PERSON><PERSON>()); } catch {}
    };
  }, deps);
}
