import React, {Component} from 'react';
import * as api from '../../api';
import ButtonSubmit from '../../components/ButtonSubmit';
import RelatedLecture from './RelatedLecture';
import { checkImageContent } from '../../helper';
import {createEditorInputContentInstance} from '../../../editor';
import { v4 as uuidv4 } from 'uuid';

class QuizQuestionCreate extends Component {
    constructor(props) {
        super(props);
        this.state = {
            id: 0,
            content: '',
            answers: [
                {content: '', answer: 'N', id: uuidv4()},
                {content: '', answer: 'N', id: uuidv4()},
                {content: '', answer: 'N', id: uuidv4()},
                {content: '', answer: 'N', id: uuidv4()},
            ],
            relatedLecture: 0,
            reason: '',
            slug: '',
            title: '',
            selectText: null,
            dataSelect: [],
            adsUrl: '',
            isButtonLoading: false,
        }

        this.questionRef = React.createRef();
    }

    componentWillMount() {
        let {value, question} = this.props;

        if (value.quiz_content) {
            let quiz_content;

            if (Array.isArray(value.quiz_content)) {
                quiz_content = value.quiz_content;
            } else {
                try {
                    quiz_content = JSON.parse(value.quiz_content);
                } catch (error) {
                    console.error('Invalid JSON string:', error);
                    quiz_content = []; // Hoặc giá trị mặc định khác
                }
            }
            this.setState({ dataSelect: quiz_content });
        }

        if (question?.id) {
            this.setState({
                id: question.id,
                content: question.content || '',
                reason: question.reason || '',
                slug: question.slug || '',
                title: question.title || '',
                selectText: question.text_select,
                adsUrl: question.ads_url || '',
                answers: question.answers || []
            })
        }
    }

    componentDidMount() {
        createEditorInputContentInstance();
        // nếu editor ko autofocus thì thêm đoạn này cho tăng UX
        // if (this.questionRef.current) {
        //     this.questionRef.current.scrollIntoView({
        //         behavior: 'auto',
        //         block: 'center',
        //     });
        // }
    }

    selectRelated = (relatedLecture) => {
        this.setState({relatedLecture})
    }

    addNewAnswer = () => {
        const { answers } = this.state;
        if (answers.length >= 15) return;

        this.setState(
            {
                answers: [...answers, {content: '', answer: 'N', id: uuidv4()}]
            },
            () => {
                requestAnimationFrame(() => createEditorInputContentInstance());
            }
        );
    }

    deleteAnswer = (uid) => {
        const { flagIndexId } = this.props;
        const elId = `answer-${flagIndexId}-${uid}`;

        if (window.tinymce) {
            const ed = tinymce.get(elId);
            if (ed) ed.remove(); // gỡ đúng instance cần xoá
        }

        this.setState(prev => ({
            answers: prev.answers.filter(a => a.id !== uid)
        }), () => {
            requestAnimationFrame(() => createEditorInputContentInstance());
        });
    }

    toggleCorrect = (uid) => {
        this.setState(prev => {
            const answers = prev.answers.map((a) =>
                a.id == uid ? { ...a, answer: a.answer === 'Y' ? 'N' : 'Y' } : a
            );
            return { answers };
        });
    }

    changeSlug = (event) => {
        let val = event.target.value;
        this.setState({
            slug: window.helperFunc.titleToSlug(val, 100),
        })
    }

    changeAdsUrl = (event) => {
        let val = event.target.value;
        this.setState({
            adsUrl: val
        })
    }

    changeTitle = (event) => {
        let val = event.target.value;
        this.setState({
            title: val,
            slug: window.helperFunc.titleToSlug(val, 100),
        })
    }

    changeTypeInput = (e) => {
        let typeInput = e.target.value;
        this.setState({selectText: typeInput})
    }

    submitQuiz = () => {
        let countAltImg = 0;
        const {value, flagIndexId} = this.props;
        const {id, answers, relatedLecture, slug, title, adsUrl, selectText} = this.state;

        if (!title) {
            alert('Bạn chưa nhập tiêu đề SEO!');

            return;
        }

        let content = tinymce.get(`ed-content-${flagIndexId}`).getContent();
        let reason = tinymce.get(`ed-reason-${flagIndexId}`).getContent();

        if (!content) {
            alert('Bạn chưa nhập nội dung câu hỏi!');

            return;
        }

        if (!reason) {
            alert('Bạn chưa nhập phần giải thích!');

            return;
        }

        if (checkImageContent(content)) {
            alert('Nội dung câu hỏi chứa link ảnh không hợp lệ, bạn phải upload bằng editor, không đc kéo thả hoặc copy - paste!');

            return;
        }

        content = content.replace(/T&agrave;i liệu VietJack/g, function(x) {
            countAltImg++;
            return title + ' (ảnh ' + countAltImg + ')';
        }).replace(/alt=\"\"/g, function(x) {
            countAltImg++;
            return 'alt="' + title + ' (ảnh ' + countAltImg + ')"';
        });

        if (checkImageContent(reason)) {
            alert('Nội dung giải thích chứa link ảnh không hợp lệ, bạn phải upload bằng editor, không đc kéo thả hoặc copy - paste!');

            return;
        }

        reason = reason.replace(/T&agrave;i liệu VietJack/g, function(x) {
            countAltImg++;
            return title + ' (ảnh ' + countAltImg + ')';
        }).replace(/alt=\"\"/g, function(x) {
            countAltImg++;
            return 'alt="' + title + ' (ảnh ' + countAltImg + ')"';
        });

        if (!answers.length) {
            alert('Bạn chưa nhập đáp án!');

            return;
        }

        let newAnswers = [];
        let answerError = 0;
        let hasCorrect = false;

        answers.forEach(function(answer, index) {
            let edAnswer = tinymce.get(`answer-${flagIndexId}-${answer.id}`).getContent();

            if (edAnswer) {
                if (checkImageContent(edAnswer)) {
                    answerError = index + 1;

                    return;
                }

                edAnswer = edAnswer.replace(/T&agrave;i liệu VietJack/g, function(x) {
                    countAltImg++;
                    return title + ' (ảnh ' + countAltImg + ')';
                }).replace(/alt=\"\"/g, function(x) {
                    countAltImg++;
                    return 'alt="' + title + ' (ảnh ' + countAltImg + ')"';
                });

                newAnswers.push({...answer, content: edAnswer});
            }

            if (!hasCorrect && answer.answer =='Y') {
                hasCorrect = true;
            }
        });

        if (answerError) {
            alert('Nội dung đáp án ' + answerError + ' chứa link ảnh không hợp lệ, bạn phải upload bằng editor, không đc kéo thả hoặc copy - paste!');

            return;
        }

        if (!hasCorrect) {
            alert('Bạn phải chọn ít nhất 1 đáp án đúng!');

            return;
        }

        const data = {
            content,
            curriculum_item: value.id,
            related_lecture: relatedLecture,
            owner: this.props.userid,
            type: 'test',
            reason,
            slug,
            title,
            adsUrl,
            text_select: selectText,
            answers: newAnswers,
        };
        let enpoint = api.ADD_QUESTION;

        if (id > 0) {
            data.question_id = id;
            enpoint = api.UPDATE_QUESTION;
        }

        this.setState({isButtonLoading: true});

        axios.post(enpoint, data).then((res) => {
            if ((res.status == 200 || res.status == 201) && res?.data?.data) {
                this.props.onSubmit(value, res.data.data, id > 0);

                // ✅ Scroll về vị trí câu hỏi
                const target = document.getElementById(`equestion-${id}`);

                if (target) {
                    target.scrollIntoView({
                        behavior: 'auto',
                        block: 'center'
                    });

                    target.classList.add('bg-warning');

                    setTimeout(() => {
                        target.classList.remove('bg-warning');
                    }, 2000);
                }
            } else {
                alert('Thất bại! Dữ liệu không được trả về .');
            }
        }).catch((error) => {
            let text = '';

            if (error.response && error.response.status === 422) {
                const { errors } = error.response.data;

                for (let [key, value] of Object.entries(errors)) {
                    text += (value[0] + '<br>');
                }
            } else if (error.response && error.response.data && error.response.data.message) {
                text = error.response.status === 419 ?
                    'Phiên làm việc trên form đã hết hạn. Vui lòng tải lại trang!' :
                    error.response.data.message;
            } else {
                text = error.response.data.errors.content ? error.response.data.errors.content : 'Đã xảy ra lỗi!';
            }

            $.notify({
                message: text,
            },{
                element: 'body',
                position: null,
                type: 'danger',
                placement: {
                    from: 'top',
                    align: 'center'
                }
            });
        }).finally(() => {
            this.setState({isButtonLoading: false});
        });
    }

    render() {
        const {value, flagIndexId} = this.props;
        const {answers, dataSelect} = this.state;

        let answersElem = answers.map((v, i) => {
            return (
                <div className="answer-content-wrapper" key={v.id}>
                    <div className="answer-check">
                        <label className="custom-control custom-checkbox">
                            <input
                                type="checkbox"
                                className="custom-control-input"
                                name="answer"
                                checked={v.answer === 'Y'}
                                onChange={() => this.toggleCorrect(v.id)}
                                aria-checked={v.answer === 'Y'}
                            />
                            <span className="custom-control-indicator"></span>
                        </label>
                    </div>
                    <textarea
                        className="form-control editor-input-content"
                        rows="3"
                        id={`answer-${flagIndexId}-${v.id}`}
                        defaultValue={v.content}
                    >
                    </textarea>
                    <div className="answer-toolbar">
                        <button
                            onClick={() => this.deleteAnswer(v.id)}
                            type="button"
                        >
                            <i className="fa-trash-o fa"></i>
                        </button>
                    </div>
                </div>
            )
        })

        return (
            <div className="lecture-add-more">
                <div className="content-type-close">
                    <button
                        onClick={() => this.props.onClose(value, false)}
                    >
                        Đóng <i className="fa fa-times" aria-hidden="true"></i>
                    </button>
                </div>
                <div className="add-content-wrapper">
                    <div className="quiz-add-content">
                        {value.quiz_content &&
                            <div className="form-group">
                                <label>Chọn đoạn văn</label>
                                <div className="form-group form-group-sm">
                                    <select className="form-control" value={this.state.selectText ? this.state.selectText : '' } data-placeholder="Chọn ..." name="type" onChange={this.changeTypeInput}>
                                        <option value="">__ Chọn __</option>
                                        {dataSelect.map((item, index) => (
                                           <option value={index} key={index}>{'Đoạn văn ' + (index+1)}</option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                        }
                        <p>Câu hỏi: <strong className="text-danger">Trắc nghiệm</strong></p>
                        <textarea
                            ref={this.questionRef}
                            className="form-control editor-input-content"
                            rows="3"
                            id={`ed-content-${flagIndexId}`}
                            defaultValue={this.state.content}
                        >
                        </textarea>
                        <br/>
                        <div className="form-group answers-form-group">
                            <p>Câu trả lời</p>
                        </div>
                        {answersElem}
                        <button type="button" className="btn btn-info" onClick={this.addNewAnswer}>Thêm đáp án</button>
                        <span className="help-block">Có thể thêm lên đến 15 đáp án.</span>
                        <div className="test-explain">
                            <label>Giải thích</label>
                            <textarea
                                className="form-control editor-input-content"
                                rows="3"
                                id={`ed-reason-${flagIndexId}`}
                                defaultValue={this.state.reason}
                            >
                            </textarea>
                        </div>
                        <br />
                        <div className="form-group">
                            <p>Tiêu đề SEO câu hỏi</p>
                            <input id="title" className="form-control" onChange={this.changeTitle} value={this.state.title}/>
                        </div>
                        <div className="form-group">
                            <p>Đường dẫn slug câu hỏi (tối đa 100 ký tự)</p>
                            <input id="slug" className="form-control" onChange={this.changeSlug} value={this.state.slug}/>
                        </div>
                        <div className="form-group">
                            <p>URL quảng cáo</p>
                            <input id="adsUrl" className="form-control" onChange={this.changeAdsUrl} value={this.state.adsUrl}/>
                        </div>
                        <div className="form-group answer-related-relatedLecture">
                            <p>Bài học liên quan</p>
                            <RelatedLecture
                                cId={this.props.cId}
                                selectRelated={this.selectRelated}
                            />
                            <span className="help-block">Chọn một bài giảng video có liên quan để giúp sinh viên trả lời câu hỏi này.</span>
                        </div>
                        <div className="text-right form-actions">
                            <ButtonSubmit
                                type="button"
                                isLoading={this.state.isButtonLoading}
                                onClick={this.submitQuiz}
                            >
                                Lưu lại
                            </ButtonSubmit>
                        </div>
                    </div>
                </div>
            </div>
        )
    }
}

export default QuizQuestionCreate;
