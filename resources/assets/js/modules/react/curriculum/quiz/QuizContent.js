import React, {Component} from 'react';
import {SortableContainer, SortableElement, arrayMove} from 'react-sortable-hoc';
import * as api from '../../api';
import {Modal} from 'react-bootstrap';
import axios from 'axios';
import ImportQuestionModal from './ImportQuestionModal';

class QuizContent extends Component {
    constructor(props) {
        super(props);
        this.state = {
            showModal: false,
            showModalImport: false,
            questionSelected: '',
            quesIds: [],
            flagIndexId: Math.floor(Date.now()/1000),
        }
    }

    /**
     * Handle while close modal
     * simply change the state
     */
    handleCloseModal = () => {
        this.setState({
            showModal: false
        });
    }

    /**
     * Handle while open modal
     * Pass and set id to curriculum state
     * @param {*} id
     */
    handleShowModal = (question) => {
        this.setState({
            showModal: true,
            questionSelected: question
        });
    }

    handleShowModalImport = () => {
        this.setState({showModalImport: true});
    }

    handleCloseModalImport = () => {
        this.setState({showModalImport: false});
    }

    onSortEnd = ({oldIndex, newIndex}) => {
        let items = arrayMove(this.props.question, oldIndex, newIndex);
        this.props.onSortEndQuestion(items, this.props.curriculum);

        axios.post(api.UPDATE_QUESTION_ON_SORT_END, {
            items
        })
        .then((response) => {
            //
        })
        .catch((error) => {
            console.log(error)
        })
    };

    deleteQuestion = (value) => {
        const ids = value === 'all' ? this.state.quesIds : [value.id];

        axios.post(api.DELETE_QUESTION, {
            ids,
            curriculum_item: this.props.curriculum.id,
        })
        .then((response) => {
            if (response.status === 200 || response.status === 201) {
                this.props.deleteQuestion(ids, this.props.curriculum, true)
            }
        })
        .catch((err) => {
            console.log(err)
        })
    }

    changeStatusLecture = (value) => {
        axios.post(api.UPDATE_STATUS, {
            value
        }).then((res) => {
            if (res.status === 200  || res.status === 201) {
                let data = {
                    lecture: value,
                    section: {
                        id: res.data.section.id,
                        status: res.data.section.status
                    }
                };
                this.props.changeStatusLecture(data);
            }
        })
        .catch((err) => {
            console.log(err)
        })
    }

    handleCheckboxChange = (event) => {
        const { value, checked } = event.target;

        if (checked) {
            if (value === 'all') {
                const allIds = this.props.question.map(question => question.id);
                this.setState({ quesIds: allIds });
            } else {
                this.setState(prevState => ({
                    quesIds: [...prevState.quesIds, parseInt(value)]
                }));
            }
        } else {
            if (value === 'all') {
                this.setState({ quesIds: [] });
            } else {
                this.setState(prevState => ({
                    quesIds: prevState.quesIds.filter(id => id !== parseInt(value))
                }));
            }
        }
    }

    handleMoveQuestion = () => {
        const curriculumId = document.getElementById(`curriculumId-of-${this.props.curriculum.id}`).value;

        if (!curriculumId) {
            alert('Vui lòng nhập Id của bài!');
            return;
        }

        if (this.state.quesIds.length == 0) {
            alert('Vui lòng chọn các câu hỏi muốn chuyển!');
            return;
        }

        const allSelected = this.props.question.length > 0 && this.state.quesIds.length === this.props.question.length;

        axios.post(api.MOVE_QUESTION, {
            oriCurriculumId: allSelected ? this.props.curriculum.id : null,
            curriculumId,
            quesIds: this.state.quesIds,
        })
        .then((response) => {
            const { data } = response;

            if (data.moved) {
                window.location.reload();
            } else {
                alert(data?.message || 'Cập nhật thất bại!');
            }
        })
        .catch((err) => {
            console.log(err)
        })
    }

    handleAddQuestionByID = () => {
        const questionId = document.getElementById(`questionId-of-${this.props.curriculum.id}`).value;

        if (!questionId) {
            alert('Vui lòng nhập Id question!');
            return;
        }

        axios.post(api.ADD_QUESTION_BY_ID, {
            curriculumId: this.props.curriculum.id,
            questionId
        })
        .then((response) => {
            const { data } = response;

            if (data.added) {
                window.location.reload();
            } else {
                alert('Thêm thất bại!');
            }
        })
        .catch((err) => {
            console.log(err)
        })
    }

    render() {
        const { curriculum } = this.props;
        const { quesIds } = this.state;
        const allSelected = this.props.question.length > 0 && quesIds.length === this.props.question.length;

        const SortableItem = SortableElement(({value, i}) => {
            return (
                <div id={`equestion-${value.id}`}>
                    <div className="question-item-wrapper">
                        <div className="question-item" style={{display: 'block'}}>
                            <input
                                style={{marginRight: '5px'}}
                                type="checkbox"
                                value={ value.id}
                                onChange={this.handleCheckboxChange}
                                checked={quesIds.includes(value.id)}
                            />
                            <b>{i + 1}. </b>
                            <span>{value.content.replace(/<[^>]+>/g, '')}</span>
                            <small>{value.type == 'multi' ? ' - Nhiều đáp án' : ''}</small>
                            <p>
                                - ID: <span style={{color: 'red'}}>{value.id}</span> <br/>
                                - Tiêu đề SEO: {value.title} <br/>
                                - Slug: <a href={api.APP_ENV == 'production' ? 'https://khoahoc.vietjack.com' : '' + `/question/${value.id}`} target="_blank">{value.slug}</a><br/>
                                {this.props.admin === 'administrator' ? ('- Người sửa cuối: ' + (value.owner_name || '__') ) : ('') }<br/>
                                {value.text_select ? ('- Select: Đoạn văn ' + (parseInt(value.text_select)+1) ) : ('') }
                            </p>
                        </div>
                        <div className="question-item-panel" style={{minWidth: '155px'}}>
                            <button className="btn btn-xs item-bar-button">
                                <i className="fa-bars fa"></i>
                            </button>
                            {this.props.admin === 'administrator' && (
                                <button
                                    className="btn btn-xs item-bar-button"
                                    onClick={() => this.handleShowModal(value)}
                                >
                                    <i className="fa-trash-o fa"></i>
                                </button>
                            ) }
                            <button
                                className="btn btn-xs item-bar-button"
                                onClick={() => this.props.editQuestion(value, this.props.curriculum)}
                            >
                                <i className="fa-pencil fa"></i>
                            </button>
                        </div>
                    </div>
                </div>
            )
        })

        const SortableList = SortableContainer(({items}) => {
            return (
                <div className="curriculum-list">
                    {items.map((value, index) => (
                        <SortableItem key={`item-${index}`} index={index} value={value} i={index} admin={this.props.admin}/>
                    ))}

                    <Modal show={this.state.showModal} onHide={this.handleCloseModal}>
                        <Modal.Header>
                            <Modal.Title>Hãy chắc chắn với dữ liệu bạn muốn xóa.</Modal.Title>
                            <button
                                type="button"
                                className="close"
                                onClick={() => this.handleCloseModal()}
                            >
                                <span aria-hidden="true">×</span>
                                <span className="sr-only">Close</span>
                            </button>
                        </Modal.Header>
                        <Modal.Body>
                            <strong>
                                { this.state.questionSelected === 'all' ? ('Bạn sắp xóa ' + quesIds.length + ' câu hỏi.') : ('Xóa câu hỏi: ' + this.state.questionSelected.title) }
                            </strong>
                        </Modal.Body>
                        <Modal.Footer>
                            <button
                                type="reset"
                                className="btn btn-tertiary"
                                onClick={() => this.handleCloseModal()}
                            >Đóng
                            </button>
                            <button
                                type="submit"
                                className="btn btn-secondary"
                                onClick={() => this.deleteQuestion(this.state.questionSelected)}
                            >Xóa
                            </button>
                        </Modal.Footer>
                    </Modal>
                </div>
            );
        });

        return (
            <div className="lecture-add-more">
                <div className="lecture-content-container">
                    <div className="lecture-content-wrapper">
                        <div className="quiz-content">
                            Ds câu hỏi <span>(ID bài: <mark>{ this.props.curriculum.id }</mark>)</span>
                            <button
                                onClick={() => this.props.addQuestion(this.props.curriculum)}
                            >Thêm câu hỏi mới</button>
                        </div>
                        <div>
                            <button
                                className="btn btn-warning"
                                onClick={this.handleShowModalImport}
                            >Import file</button>
                            <ImportQuestionModal
                              showModal={this.state.showModalImport}
                              handleCloseModal={this.handleCloseModalImport}
                              flagIndexId={this.state.flagIndexId}
                              { ...this.props }
                            />
                            {this.props.coursetype !== 'essay' &&
                                curriculum.status === 'disable' ? (
                                    <button
                                        onClick={() => this.changeStatusLecture(curriculum)}
                                        className="btn btn-danger"
                                    >
                                        Xuất bản
                                    </button>
                                ) : (
                                    <button
                                        onClick={() => this.changeStatusLecture(curriculum)}
                                        className="btn btn-outline-danger"
                                    >
                                        Ngừng xuất bản
                                    </button>
                                )
                            }
                        </div>
                    </div>
                    <div className="lecture-content-wrapper" style={{ alignItems: 'end' }}>
                        <div className="d-md-flex">
                            <div className="d-flex" style={{ alignItems: 'baseline' }}>
                                <input
                                    style={{marginRight: '5px'}}
                                    type="checkbox"
                                    value='all'
                                    onChange={this.handleCheckboxChange}
                                    checked={allSelected}
                                />
                                <span>Chọn tất cả</span>
                            </div>
                            {this.props.admin === 'administrator' && (
                                <button
                                    className="btn btn-link mx-2 p-0"
                                    onClick={() => this.handleShowModal('all')}
                                >
                                    <i className="fa-trash-o fa"></i>
                                </button>
                            ) }
                        </div>
                        <div className="d-md-flex">
                            <div className="d-flex">
                                <button
                                    className="btn btn-primary"
                                    onClick={this.handleMoveQuestion}
                                >
                                    Chuyển câu hỏi sang:
                                </button>
                                <input
                                    placeholder="ID bài"
                                    type="number"
                                    id={ `curriculumId-of-${this.props.curriculum.id}` }
                                    style={{width: '100px', marginLeft: '1px'}}
                                    onChange={this.handleInputChange}
                                />
                            </div>
                            <div className="d-flex">
                                <button
                                    className="btn btn-info"
                                    onClick={this.handleAddQuestionByID}
                                >
                                    Thêm câu hỏi:
                                </button>
                                <input
                                    placeholder="ID question"
                                    type="number"
                                    id={ `questionId-of-${this.props.curriculum.id}` }
                                    style={{width: '100px', marginLeft: '1px'}}
                                    onChange={this.handleInputChange}
                                />
                            </div>
                        </div>
                    </div>
                </div>
                <div className="question-list">
                    <SortableList
                        admin={this.props.admin}
                        items={this.props.question}
                        onSortEnd={this.onSortEnd}
                        shouldCancelStart={(e) => {
                            if (e.target.className != 'fa-bars fa') {
                                return true; // Return true to cancel sorting
                            }
                        }}
                    />
                </div>
            </div>
        )
    }
}

export default QuizContent;
