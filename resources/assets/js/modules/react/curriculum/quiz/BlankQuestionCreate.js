import React, {Component} from 'react';
import CKEditor from '../../components/CKEditor';
import HtmlContent from '../../components/HtmlContent';
import { jsonToHtml, htmlToJson } from '../../question/qlib/Blank.js';
import { createEditorInputContentInstance } from '../../../editor';
import { checkImageContent, normalizeStr } from '../../helper';
import * as api from '../../api.js';
import ButtonSubmit from '../../components/ButtonSubmit';

class BlankQuestionCreate extends Component {
    constructor(props) {
        super(props);
        this.state = {
            id: 0,
            content: '',
            answers: [],
            reason: '',
            slug: '',
            title: '',
            selectText: null,
            dataSelect: [],
            isButtonLoading: false,
        }

        this.editorRef = React.createRef();
    }

    componentWillMount() {
        let {value, question} = this.props;

        if (value.quiz_content) {
            let quiz_content;

            if (Array.isArray(value.quiz_content)) {
                quiz_content = value.quiz_content;
            } else {
                try {
                    quiz_content = JSON.parse(value.quiz_content);
                } catch (error) {
                    console.error('Invalid JSON string:', error);
                    quiz_content = []; // Hoặc giá trị mặc định khác
                }
            }
            this.setState({ dataSelect: quiz_content });
        }

        if (question?.id) {
            const newcontent = jsonToHtml({
                content: question.content || '',
                answers: question.answers || []
            });

            this.setState({
                id: question.id,
                content: newcontent,
                reason: question.reason || '',
                slug: question.slug || '',
                title: question.title || '',
                selectText: question.text_select,
                answers: question.answers || []
            })
        }
    }

    componentDidMount() {
        createEditorInputContentInstance();
    }

    handleEditorReady = (editor) => {
        this.editorRef.current = editor;

        editor.on('dataReady', () => {
            editor._isContentReady = true;
            console.log('[CKEditor] dataReady: content is safe to get');
        });
    };

    handleEditorChange = () => {
        const editor = this.editorRef.current;

        if (!editor) return;

        const rawContent = editor.getData();
        const { content, answers } = htmlToJson(rawContent);

        this.setState({
            content,
            answers
        })
    };

    changeTitle = (event) => {
        let val = event.target.value;
        this.setState({
            title: val,
            slug: window.helperFunc.titleToSlug(val, 100),
        })
    }

    changeSlug = (event) => {
        let val = event.target.value;

        this.setState({
            slug: window.helperFunc.titleToSlug(val, 100),
        })
    }

    changeTypeInput = (e) => {
        let typeInput = e.target.value;
        this.setState({selectText: typeInput})
    }

    submitQuiz = () => {
        let {value, flagIndexId} = this.props;
        let {id, content, answers, slug, title, selectText} = this.state;

        if (!title) {
            alert('Bạn chưa nhập tiêu đề SEO!');

            return;
        }

        if (!content) {
            alert('Bạn chưa nhập nội dung câu hỏi!');

            return;
        }

        let reason = tinymce.get(`ed-reason-${flagIndexId}`).getContent();

        if (!reason) {
            alert('Bạn chưa nhập phần giải thích!');

            return;
        }

        if (checkImageContent(content)) {
            alert('Nội dung câu hỏi chứa link ảnh không hợp lệ, bạn phải upload bằng editor, không đc kéo thả hoặc copy - paste!');

            return;
        }

        if (checkImageContent(reason)) {
            alert('Nội dung giải thích chứa link ảnh không hợp lệ, bạn phải upload bằng editor, không đc kéo thả hoặc copy - paste!');

            return;
        }

        if (answers.lengh == 0) {
            alert('Câu hỏi không có đáp án! Vui lòng tạo đáp án.');

            return;
        }

        let answerError = 0;

        answers.forEach(function(answer, index) {
            const str = normalizeStr(answer.content);

            if (!str) {
                answerError = index + 1;

                return;
            }

            answer.content = str;
            answer.answer = answer.targetId;
        });

        if (answerError) {
            alert('Vui lòng nhập nội dung đáp án ' + answerError);

            return;
        }

        const data = {
            content,
            curriculum_item: value.id,
            owner: this.props.userid,
            type: 'blank',
            reason,
            slug,
            title,
            text_select: selectText,
            answers
        };
        let enpoint = api.ADD_QUESTION;

        if (id > 0) {
            data.question_id = id;
            enpoint = api.UPDATE_QUESTION;
        }

        this.setState({isButtonLoading: true});

        axios.post(enpoint, data).then((res) => {
            if ((res.status == 200 || res.status == 201) && res?.data?.data) {
                this.props.onSubmit(value, res.data.data, id > 0);

                // ✅ Scroll về vị trí câu hỏi
                const target = document.getElementById(`equestion-${id}`);

                if (target) {
                    target.scrollIntoView({
                        behavior: 'auto',
                        block: 'center'
                    });

                    target.classList.add('bg-warning');

                    setTimeout(() => {
                        target.classList.remove('bg-warning');
                    }, 2000);
                }
            } else {
                alert('Thất bại! Dữ liệu không được trả về .');
            }
        }).catch((error) => {
            let text = '';

            if (error.response && error.response.status === 422) {
                const { errors } = error.response.data;

                for (let [key, value] of Object.entries(errors)) {
                    text += (value[0] + '<br>');
                }
            } else if (error.response && error.response.data && error.response.data.message) {
                text = error.response.status === 419 ?
                    'Phiên làm việc trên form đã hết hạn. Vui lòng tải lại trang!' :
                    error.response.data.message;
            } else {
                text = error.response.data.errors.content ? error.response.data.errors.content : 'Đã xảy ra lỗi!';
            }

            $.notify({
                message: text,
            },{
                element: 'body',
                position: null,
                type: 'danger',
                placement: {
                    from: 'top',
                    align: 'center'
                }
            });
        }).finally(() => {
            this.setState({isButtonLoading: false});
        });
    }

    render() {
        let {value, flagIndexId} = this.props;
        const {dataSelect} = this.state;

        return (
            <div className="lecture-add-more">
                <div className="content-type-close">
                    <button
                        onClick={() => this.props.onClose(value, false)}
                    >
                        Đóng <i className="fa fa-times" aria-hidden="true"></i>
                    </button>
                </div>
                <div className="add-content-wrapper">
                    <div className="quiz-add-content">
                        {value.quiz_content &&
                            <div className="form-group">
                                <label>Chọn đoạn văn</label>
                                <div className="form-group form-group-sm">
                                    <select className="form-control" value={this.state.selectText ? this.state.selectText : '' } data-placeholder="Chọn ..." name="type" onChange={this.changeTypeInput}>
                                        <option value="">__ Chọn __</option>
                                        {dataSelect.map((item, index) => (
                                           <option value={index} key={index}>{'Đoạn văn ' + (index+1)}</option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                        }
                        <p>Câu hỏi: <strong className="text-danger">Điền từ</strong></p>
                        <CKEditor
                            initData={this.state.content}
                            name="question_blank"
                            onInstanceReady={this.handleEditorReady}
                            onChange={this.handleEditorChange}
                        />
                        <br/>
                        <div className="form-group answers-form-group">
                            <p>Đáp án</p>
                        </div>
                        { this.state.answers.length ? (
                            <div style={{ fontSize: '20px', border: '1px solid #6c8bef', padding: '20px', marginBottom: '20px' }}>
                                { this.state.content && <HtmlContent html={ this.state.content } contentType="blank" /> }
                                <div className="d-flex align-items-center">
                                    { this.state.answers.map((answer, index) => (
                                        <div key={index} className="btn btn-warning mr-2 mb-2">
                                            <HtmlContent html={answer.content}/>
                                        </div>
                                    )) }
                                </div>
                            </div>
                        ) : (
                            <p className="text-danger">(--- Danh sách đáp án ---)</p>
                        ) }
                        <div className="test-explain">
                            <label>Giải thích</label>
                            <textarea
                                className="form-control editor-input-content"
                                rows="3"
                                id={`ed-reason-${flagIndexId}`}
                                defaultValue={this.state.reason}
                            >
                            </textarea>
                        </div>
                        <br />
                        <div className="form-group">
                            <p>Tiêu đề SEO câu hỏi</p>
                            <input className="form-control" onChange={this.changeTitle} value={this.state.title}/>
                        </div>
                        <div className="form-group">
                            <p>Đường dẫn slug câu hỏi (tối đa 100 ký tự)</p>
                            <input className="form-control" onChange={this.changeSlug} value={this.state.slug}/>
                        </div>
                        <div className="text-right form-actions">
                            <ButtonSubmit
                                type="button"
                                isLoading={this.state.isButtonLoading}
                                onClick={this.submitQuiz}
                            >
                                Lưu lại
                            </ButtonSubmit>
                        </div>
                    </div>
                </div>
            </div>
        )
    }
}

export default BlankQuestionCreate;
