import React, {Component} from 'react';
import * as api from '../../api';
import {
  Button,
  Modal,
  FormGroup,
  ControlLabel
} from 'react-bootstrap';
import ButtonSubmit from '../../components/ButtonSubmit';
import {createEditorBaseInstance} from '../../../editor';

class ImportQuestionModal extends Component {
  constructor(props) {
    super(props);

    this.state = {
      isLoading: false,
      question_flag: '',
      reason_flag: '',
    };
  }

  changeQuestionFlag = (e) => {
    this.setState({
      question_flag: e.target.value
    })
  }

  changeReasonFlag = (e) => {
    this.setState({
      reason_flag: e.target.value
    })
  }

  submitForm = () => {
    this.setState({isLoading: true});

    let {question_flag, reason_flag} = this.state;
    const ext = this.refs.documentInput.value.split('.').pop().toLowerCase();

    if(['docx'].indexOf(ext) == -1) {
      alert('Chỉ chọn file thuộc định dạng: .docx');
      return;
    } else {
      const {curriculum, userid, flagIndexId} = this.props;
      const formData = new FormData();

      formData.append('file', this.refs.documentInput.files[0]);
      formData.append('curriculum_item', curriculum.id);
      formData.append('owner', userid);
      formData.append('question_flag', question_flag);
      formData.append('reason_flag', reason_flag);
      formData.append('content_common', tinymce.get(`content_common-${flagIndexId}`).getContent() || '');

      axios.post(api.IMPORT_QUESTION, formData).then(response => {
        const { data } = response;

        if (data.imported) {
          window.location.reload();
        } else {
          alert(data.message);
        }
      }).catch(function(error) {
        this.setState({isLoading: false});

        $text = '';

        if (error.response && error.response.status === 422) {
          const { errors } = error.response.data;

          for (let [key, value] of Object.entries(errors)) {
            $text += (value[0] + "\n");
          }
        } else if (error.response && error.response.data && error.response.data.message) {
          const $text = error.response.status === 419 ?
            'Phiên làm việc trên form đã hết hạn. Vui lòng tải lại trang!' : error.response.data.message;
        } else {
          $text = 'Đã xảy ra lỗi.';
        }

        alert($text);
      })
    }
  }

  renderUploadDocument = () => {
    return (
      <FormGroup>
        <ControlLabel>Hãy chọn file thuộc định dạng: .docx</ControlLabel>
        <br />
        <input
          type="file"
          accept=".docx,.pdf"
          ref="documentInput"
          id="documentInput"
        />
      </FormGroup>
    );
  }

  handleModalEntered = () => {
    createEditorBaseInstance();
  };

  render() {
    const { showModal, handleCloseModal, flagIndexId } = this.props;

    return (
      <Modal
        show={showModal}
        onHide={handleCloseModal}
        bsSize="lg"
        dialogClassName="custom-modal-2"
        onEntered={this.handleModalEntered}
      >
        <Modal.Body>
          <div className="container">
            { this.renderUploadDocument() }
            <FormGroup>
              <ControlLabel>Text đánh dấu chia câu: Câu</ControlLabel>
              <br />
              <input
                className="form-control"
                type="text"
                value={this.state.question_flag}
                onChange={this.changeQuestionFlag}
              />
            </FormGroup>
            <FormGroup>
              <ControlLabel>Text đánh dấu lời giải: Lời giải</ControlLabel>
              <br />
              <input
                className="form-control"
                type="text"
                value={this.state.reason_flag}
                onChange={this.changeReasonFlag}
              />
            </FormGroup>
            <FormGroup>
              <ControlLabel>Nội dung chung cho tất cả các câu hỏi</ControlLabel>
              <br />
              <textarea
                className="form-control editor-input-base"
                rows="3"
                id={`content_common-${flagIndexId}`}
              >
              </textarea>
            </FormGroup>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={handleCloseModal}>
            Đóng
          </Button>
          <ButtonSubmit
            isLoading={this.state.isLoading}
            onClick={this.submitForm}
          >
            Import
          </ButtonSubmit>
        </Modal.Footer>
      </Modal>
    )
  }
}

export default ImportQuestionModal;
