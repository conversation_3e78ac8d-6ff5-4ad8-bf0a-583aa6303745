import React, {Component} from 'react';
import {SortableContainer, SortableElement, arrayMove} from 'react-sortable-hoc';
import {Modal} from 'react-bootstrap';
import * as api from '../api';
import CurriculumItemEdit from './CurriculumItemEdit';
import CurriculumEditor from './CurriculumEditor';
import VideoUploadTabs from './VideoUploadTabs';
import YoutubeUploadTabs from './YoutubeUploadTabs';
import LectureItemContent from './LectureItemContent';
import QuizQuestionCreate from './quiz/QuizQuestionCreate';
import BlankQuestionCreate from './quiz/BlankQuestionCreate';
import EssayQuestionCreate from './quiz/EssayQuestionCreate';
import QuizContent from './quiz/QuizContent';
import ResourceUpload from './ResourceUpload';
import ExamForm from "./exam/ExamForm";
import ExamOpenContent from "./exam/ExamOpenContent";
import LectureItemEmptyContent from "./LectureItemEmptyContent";

class CurriculumItem extends Component {
    constructor(props) {
        super(props);
        this.state = {
            showModal: false,
            curriculumItemId: 0,
            editTitle: '',
            editDescrition: '',
            question: null,
            moveToParent: false,
        }
    }

    /**
     * Handle while complete the sortablejs
     * Update new index of curriculum array
     * @param {*} param0
     */
    onSortEnd = ({oldIndex, newIndex}) => {
        let items = arrayMove(this.props.items, oldIndex, newIndex);
        this.props.onSortEnd(items);

        axios.post(api.UPDATE_ON_SORT_END, {
            items,
            oldIndex,
            newIndex
        })
    };

    onSortEndQuestion = (questions, cur) => {
        this.props.onSortEndQuestion(questions, cur);
    }

    /**
     * Handle while close modal
     * simply change the state
     */
    handleCloseModal = () => {
        this.setState({
            showModal: false
        });
    }

    handleChangeFree = (e, value) => {
        const val = e.target.checked;
        const ischecked =  val ? 1 : 0;
        axios.post(api.CHANGE_FREE, {
            id: value.id,
            ischecked
        }).then((response) => {
            const changeFree = { ...value, free: ischecked };
            this.props.changeFreeLecture(changeFree);
        }).catch((error) => {
            console.error('Lỗi khi lấy dữ liệu:', error);
        });
    }

    /**
     * Handle while open modal
     * Pass and set id to curriculum state
     * @param {*} id
     */
    handleShowModal = (id, moveToParent = false) => {
        this.setState({
            showModal: true,
            curriculumItemId: id,
            moveToParent: moveToParent
        });
    }

    /**
     * Handle modal press delete
     * Send id to service then close modal
     * then pass a props back to parent component to delele item from array
     * @param {*} id
     */
    handleDeleteItem = (id) => {
        axios.post(api.DELETE_ITEM, {
            id
        }).then((response) => {
            if (response.status === 200 || response.status === 201) {
                this.handleCloseModal();
                if (response.data.code_status == true) {
                    this.props.itemDeleted(id);
                } else {
                    alert(response.data.message);
                }
            }
        })
    }

    handleMoveItemToParent = (id) => {
        axios.post(api.MOVE_ITEM_TO_PARENT, {
            id: id,
            userid: this.props.userid
        }).then((response) => {
            if (response.status === 200 || response.status === 201) {
                this.handleCloseModal();

                if (response.data.code_status == true) {
                    this.props.itemDeleted(id);
                } else {
                    alert('Có lỗi xảy ra, vui lòng liên hệ tổ IT');
                }
            }
        })
    }

    handleOpenEditItem = (value) => {
        this.setState({
            editTitle: value.name,
            editDescrition: value.description
        })
        this.props.handleOpenEditItem(value);
    }

    handleCloseEditItem = (value) => {
        this.props.handleCloseEditItem(value);
    }

    handleOpenAddLecture = (value, status) => {
        this.props.handleOpenAddLecture(value, status);
    }

    openDescriptionTinyMCE = (value, status) => {
        this.props.openDescriptionTinyMCE(value, status);
    }

    closeTinyMce = (value, status) => {
        this.props.closeTinyMce(value, status);
    }

    onSubmitTinyMCE = (value, description) => {
        this.props.onSubmitTinyMCE(value, description);
    }

    handleContentStatus = (value, status) => {
        this.props.handleContentStatus(value, status);
    }

    handleOpenVideoAdd = (value, status, type) => {
        this.props.handleOpenVideoAdd(value, status, type)
    }

    updateVideo = (value, status, type) => {
        this.props.updateVideo(value, status, type);
    }

    uploadLecureContent = (media, value) => {
        this.props.uploadLecureContent(media, value)
    }

    changeLectureByMediaChecking = (value) => {
        this.props.changeLectureByMediaChecking(value);
    }

    changeStatusLecture = (value) => {
        this.props.changeStatusLecture(value);
    }

    changeFreePreview = (id, value) => {
        this.props.changeFreePreview(id, value);
    }

    handleOpenQuizAddInEdit = (value, status, question) => {
        this.props.handleOpenQuizAddInEdit(value, status, question);
    }

    changeCurriculumStatus = (value) => {
        this.props.changeCurriculumStatus(value);
    }

    onChangeMedia = (value, media) => {
        this.props.onChangeMedia(value, media);
    }

    handleOpenQuizContent = (value, status) => {
        this.props.handleOpenQuizContent(value, status)
    }

    addQuestion = (curriculum) => {
        this.setState({question: null});
        this.props.addNewQuestion(curriculum);
    }

    deleteQuestion = (question, curriculum, multipleDel = false) => {
        this.props.deleteQuestion(question, curriculum, multipleDel);
    }

    editQuestion = (question, curriculum) => {
        this.props.handleOpenQuizAdd(curriculum, true, question.type);
        this.setState({question});
    }

    questionSubmited = (curriculum, question, isEdit) => {
        this.props.questionSubmited(curriculum, question, isEdit);
        this.setState({question: null});
    }

    openResourceContent = (value, status) => {
        this.props.openResourceContent(value, status);
    }

    deleteResource = (id, curriculum) => {
        this.props.deleteResource(id, curriculum);
    }

    uploadNewResource = (resource, curriculum) => {
        this.props.uploadNewResource(resource, curriculum)
    }

    onChangeResource = (curriculum, resource) => {
        this.props.onChangeResource(curriculum, resource);
    }

    handleMultiQForm = (value, status) => {
        this.props.handleMultiQForm(value, status)
    }

    handleMultiQFormEdit = (value, status) => {
        this.props.handleMultiQFormEdit(value, status)
    }

    multipleEdited = (value, status, q) => {
        this.props.multipleEdited(value, status, q)
    }

    render() {
        let indexSection = 0;
        let indexLecture = 0;

        const SortableItem = SortableElement(({value, i}) => {
            let typeName = '';
            if (value.type === 'lecture') {
                typeName = 'Bài học ';
                indexLecture += 1
            }

            if (value.type === 'section') {
                typeName = 'Phần ';
                indexSection += 1
            }

            if (value.type === 'test') {
                typeName = 'Trắc nghiệm '
            }

            if (value.type === 'essay') {
                typeName = 'Tự luận '
            }

            let typeIcon = 'check';

            if (value.type === 'section') {
                typeIcon = 'file'
            }

            if (value.type === 'lecture') {
                typeIcon = 'video'
            }

            let hasContent = (value.content !== null && !_.isEmpty(value.content)) ? true : false;

            return (
                <div className={value.type === 'section' ? 'curriculum-item' : 'curriculum-item curriculum-lecture'}>
                    <div className="section-editor">
                        {value.onEdit === false ? (
                            <div className="item-bar">
                                <div className="item-bar-title">
                                    <span className="item-bar-status">
                                        {(value.type !== 'section' && value.status === 'disable') ? (
                                            <i className="fa fa-exclamation-triangle" style={{color: '#f59c49'}}></i>
                                        ) : (<span></span>)}

                                        {(value.type !== 'section' && value.status === 'active') ? (
                                            <i className="fa fa-check-circle" style={{color: '#007791', marginRight: '5px'}}></i>
                                        ) : (<span></span>)}

                                        {value.status === 'disable' &&
                                        <span> Lưu nháp </span>
                                        }
                                        {typeName}
                                        {value.status === 'active' ? (
                                            <span>
                                                {value.type === 'section' && indexSection}
                                                {value.type === 'lecture' && indexLecture}
                                            </span>
                                        ) : (<span></span>)}
                                        <small>({value.id})</small>:
                                    </span>
                                    <span className="item-bar-name">
                                        {typeIcon === 'file' && <span className="fa fa-file-text-o item-bar-name-icon"></span>}
                                        {typeIcon === 'video' && <span className="fa fa-play-circle-o item-bar-name-icon"></span>}
                                        {typeIcon === 'check' && <span className="fa fa-check-square-o item-bar-name-icon"></span>}
                                        <span>{value.name}</span>
                                    </span>
                                    <button
                                        className="btn btn-xs item-bar-button"
                                        onClick={() => this.handleOpenEditItem(value)}
                                    ><i className="fa-pencil fa"></i></button>

                                    {(this.props.admin == 'administrator' || this.props.admin == 'moderator') && (
                                        <button
                                            className="btn btn-xs item-bar-button"
                                            onClick={() => this.handleShowModal(value.id)}
                                        ><i className="fa-trash-o fa"></i></button>
                                    )}

                                    {(this.props.admin == 'administrator' && value.type !== 'section') && (
                                        <button
                                            className="btn btn-xs item-bar-button"
                                            onClick={() => this.handleShowModal(value.id, true)}
                                            title="Chuyển mục thành khóa học"
                                        ><i className="fas fa-long-arrow-alt-right"></i>  </button>
                                    )}
                                </div>
                                {value.type === 'lecture' ? (
                                    <div className="d-flex align-items-center">
                                        <div className="item-bar-right">
                                            {!hasContent &&
                                            <button
                                                className="btn btn-sm btn-outline-primary"
                                                onClick={() => this.handleContentStatus(value, true)}
                                            ><i className="fa fa-plus"></i> Thêm nội dung</button>
                                            }

                                            { hasContent && value.preview === 'active' &&
                                                <div className="preview-status">
                                                    (Xem miễn phí)
                                                </div>
                                            }

                                            {value.show === false ? (
                                                <button
                                                    className="btn btn-xs item-bar-button item-bar-show"
                                                    onClick={() => this.handleOpenAddLecture(value, true)}
                                                ><i className="fa-chevron-down fa"></i></button>
                                            ) : (
                                                <button
                                                    className="btn btn-xs item-bar-button item-bar-show"
                                                    onClick={() => this.handleOpenAddLecture(value, false)}
                                                ><i className="fa-chevron-up fa"></i></button>
                                            )}

                                        </div>
                                        <span className="btn btn-xs item-bar-button" onClick={()=>this.props.reload(value.id)}>Copy</span>
                                        <span className="btn btn-xs item-bar-button"><i className="fa-bars fa"></i></span>
                                    </div>
                                ) : (
                                    <div className="d-flex align-items-center">
                                        {value.type !== 'section' &&
                                        <div className="item-bar-right">
                                            <span className="switch-option" style={{ fontSize: '12px' }}>
                                                Free:
                                                <label className="switch mb-0" style={{ width: '40px' }}>
                                                    <input
                                                        type="checkbox"
                                                        checked={value.free == 1}
                                                        onChange={(e) => this.handleChangeFree(e, value)} 
                                                    />
                                                    <span className="slider-free round"></span>
                                                </label>
                                            </span>
                                            <mark style={{ fontSize: '12px' }}>
                                                {hasContent ? `${value.content.length} câu` : '0 câu'}
                                            </mark>
                                            <button
                                                className="btn btn-xs item-bar-button item-bar-show"
                                                onClick={() => this.handleOpenQuizContent(value, !value.show)}
                                            ><i className="fa-chevron-down fa"></i></button>
                                        </div>
                                        }
                                        <span className="btn btn-xs item-bar-button"><i className="fa-bars fa"></i></span>
                                    </div>
                                )}
                            </div>
                        ) : (
                            <div className="form-wrapper">
                                {value.type === 'lecture' ? (
                                    <CurriculumItemEdit
                                        value={value}
                                        handleCloseEditItem={this.handleCloseEditItem}
                                        onSubmit={this.props.itemEdited}
                                        description={value.description}
                                    />
                                ) : (
                                    <ExamForm
                                        exam={value}
                                        handleCloseEditItem={this.handleCloseEditItem}
                                        onSubmit={this.props.itemEdited}
                                        userid={this.props.userid}
                                        examType={this.props.courseType}
                                    />
                                )}
                            </div>
                        )}

                        {!_.isEmpty(value.resource) && !value.show &&
                            <div className="ml-2">Tài liệu đính kèm:<br />
                                {value.resource.map((file, index) =>
                                    <div key={index}>
                                        <span className="ml-3"><i className="fa fa-download"></i> {file.name}</span>
                                    </div>
                                )}
                            </div>
                        }
                    </div>

                    {value.displayQuizForm === true && (
                        <>
                            { value.question_type == 'blank' && (
                                <BlankQuestionCreate
                                    value={value}
                                    onClose={this.props.handleOpenQuizAdd}
                                    onSubmit={this.questionSubmited}
                                    cId={this.props.cId}
                                    userid={this.props.userid}
                                    flagIndexId={this.props.flagIndexId}
                                    question={this.state.question}
                                />
                            ) }

                            { value.question_type == 'test' && (
                                <QuizQuestionCreate
                                    value={value}
                                    onClose={this.props.handleOpenQuizAdd}
                                    onSubmit={this.questionSubmited}
                                    cId={this.props.cId}
                                    userid={this.props.userid}
                                    flagIndexId={this.props.flagIndexId}
                                    question={this.state.question}
                                />
                            ) }

                            { value.question_type == 'essay' && (
                                <EssayQuestionCreate
                                    value={value}
                                    onClose={this.props.handleOpenQuizAdd}
                                    onSubmit={this.questionSubmited}
                                    cId={this.props.cId}
                                    userid={this.props.userid}
                                    flagIndexId={this.props.flagIndexId}
                                    question={this.state.question}
                                />
                            ) }
                        </>
                    )}

                    {value.showContent === true && (
                        value.type === 'lecture' ? (
                            <div className="lecture-add-more">
                                <div className="content-type-close">
                                    <button
                                        onClick={() => this.handleContentStatus(value, false)}
                                    ><i className="fa fa-times" aria-hidden="true"></i></button>
                                </div>
                                <div className="add-content-wrapper">
                                    <p className="text-center">Chọn loại nội dung chính:</p>
                                    <ul className="add-content-wrapper-list text-center">
                                        <li className="content-type-selector">
                                            <button
                                                onClick={() => this.handleOpenVideoAdd(value, true, 'video/mp4')}
                                            >
                                                <i className="fa fa-play-circle-o content-type-icon"></i>
                                                <i className="fa fa-play-circle-o content-type-icon-hover"></i>
                                                <span className="">Video</span>
                                            </button>
                                        </li>
                                        <li className="content-type-selector">
                                            <button
                                                onClick={() => this.handleOpenVideoAdd(value, true, 'youtube')}
                                            >
                                                <i style={{ color: 'red' }} className="fa fa-youtube-play content-type-icon"></i>
                                                <i className="fa fa-youtube-play content-type-icon-hover"></i>
                                                <span className="">Youtube</span>
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        ) : (
                            <ExamOpenContent
                                value={value}
                                handleContentStatus={this.handleContentStatus}
                                handleOpenQuizAdd={this.props.handleOpenQuizAdd}
                                handleMultiQForm={this.handleMultiQForm}
                            />
                        )
                    )}

                    {value.showVideoContent === true &&
                    <div className="lecture-add-more">
                        <div className="content-type-close">
                            <button
                                onClick={() => this.handleOpenVideoAdd(value, false, 'video/mp4')}
                            ><i className="fa fa-times" aria-hidden="true"></i></button>
                        </div>
                        <div className="add-content-wrapper">
                            <VideoUploadTabs
                                userid={this.props.userid}
                                coursetype={this.props.coursetype}
                                value={value}
                                uploadLecureContent={this.uploadLecureContent}
                                changeCurriculumStatus={this.changeCurriculumStatus}
                                onChangeMedia={this.onChangeMedia}
                            />
                        </div>
                    </div>
                    }

                    {value.showYoutubeContent === true &&
                    <div className="lecture-add-more">
                        <div className="content-type-close">
                            <button
                                onClick={() => this.handleOpenVideoAdd(value, false, 'youtube')}
                            ><i className="fa fa-times" aria-hidden="true"></i></button>
                        </div>
                        <div className="add-content-wrapper">
                           <YoutubeUploadTabs
                                userid={this.props.userid}
                                value={value}
                                uploadLecureContent={this.uploadLecureContent}
                                changeCurriculumStatus={this.changeCurriculumStatus}
                                onChangeMedia={this.onChangeMedia}
                                handleOpenVideoAdd={this.handleOpenVideoAdd}
                            />
                        </div>
                    </div>
                    }

                    {value.showResourceContent === true &&
                    <div className="lecture-add-more">
                        <div className="content-type-close">
                            <button
                                onClick={() => this.openResourceContent(value, false)}
                            ><i className="fa fa-times" aria-hidden="true"></i></button>
                        </div>
                        <div className="add-content-wrapper">
                            <ResourceUpload
                                userid={this.props.userid}
                                value={value}
                                uploadLecureContent={this.uploadLecureContent}
                                changeCurriculumStatus={this.changeCurriculumStatus}
                                onChangeResource={this.onChangeResource}
                                uploadNewResource={this.uploadNewResource}
                            />
                        </div>
                    </div>
                    }

                    {value.show === true && (
                        value.type == 'lecture' ? (
                            <div className="lecture-add-more">
                                {value.showEditor === false ? (
                                    <div>
                                        {!_.isNull(value.content) && (
                                            <LectureItemContent
                                                value={value}
                                                coursetype={this.props.coursetype}
                                                updateVideo={this.updateVideo}
                                                changeStatusLecture={this.changeStatusLecture}
                                                changeFreePreview={this.changeFreePreview}
                                                deleteResource={this.deleteResource}
                                                changeLectureByMediaChecking={this.changeLectureByMediaChecking}
                                            />
                                        )}
                                        {_.isNull(value.content) && (
                                            <LectureItemEmptyContent
                                                value={value}
                                                deleteResource={this.deleteResource}
                                            />
                                        )}
                                        <button
                                            className="btn btn-outline-primary"
                                            onClick={() => this.openDescriptionTinyMCE(value, true)}
                                        >
                                            <i className="fa fa-plus"></i> { value.description ? 'Sửa' : 'Thêm' } miêu tả
                                        </button>
                                        { this.props.coursetype == 'normal' && (
                                            <button
                                                className="btn btn-outline-primary"
                                                onClick={() => this.openResourceContent(value, true)}
                                            >
                                                <i className="fa fa-plus"></i> Thêm tài liệu
                                            </button>
                                        ) }
                                        <span>(ID bài: <mark>{ value.id }</mark>)</span>
                                    </div>
                                ) : (
                                    <div>
                                        <CurriculumEditor
                                            title={'Miêu tả bài học'}
                                            value={value}
                                            closeTinyMce={this.closeTinyMce}
                                            onSubmitTinyMCE={this.onSubmitTinyMCE}
                                        />
                                        <button
                                            className="btn btn-outline-primary"
                                            onClick={() => this.openResourceContent(value, true)}
                                        >
                                            <i className="fa fa-plus"></i> Thêm tài liệu
                                        </button>
                                    </div>
                                )}
                            </div>
                        ) : (
                            <QuizContent
                                admin={this.props.admin}
                                userid={this.props.userid}
                                question={value.content}
                                curriculum={value}
                                onSortEndQuestion={this.onSortEndQuestion}
                                addQuestion={this.addQuestion}
                                deleteQuestion={this.deleteQuestion}
                                editQuestion={this.editQuestion}
                                changeStatusLecture={this.changeStatusLecture}
                            />
                        )
                    )}
                </div>
            )
        });

        const SortableList = SortableContainer(({items}) => {
            const moveToParent = this.state.moveToParent;

            return (
                <div className="curriculum-list">
                    {items.map((value, index) => (
                        <SortableItem key={`item-${index}`} index={index} value={value} i={index}/>
                    ))}

                    <Modal show={this.state.showModal} onHide={this.handleCloseModal}>
                        <Modal.Header>
                            <Modal.Title> { moveToParent ? 'Bạn có chắc chắn muốn thay đổi!' : 'Bạn có chắc chắn muốn xóa!' } </Modal.Title>
                            <button
                                type="button"
                                className="close"
                                onClick={() => this.handleCloseModal()}
                            >
                                <span aria-hidden="true">×</span>
                                <span className="sr-only">Close</span>
                            </button>
                        </Modal.Header>
                        <Modal.Body>
                            <p>
                                { moveToParent ?
                                    'Bạn có chắc muốn chuyển chương trình học này thành 1 khóa học riêng không?' :
                                    'Bạn sắp xóa một chương trình giảng dạy. Bạn có chắc chắn muốn tiếp tục không?' }
                            </p>
                        </Modal.Body>
                        <Modal.Footer>
                            <button
                                type="reset"
                                className="btn btn-tertiary"
                                onClick={() => this.handleCloseModal()}
                            >
                                Đóng
                            </button>
                            { moveToParent ? (
                                    <button
                                        type="submit"
                                        className="btn btn-info"
                                        onClick={() => this.handleMoveItemToParent(this.state.curriculumItemId)}
                                    >
                                        Thay đổi
                                    </button>
                                ) : (
                                    <button
                                        type="submit"
                                        className="btn btn-secondary"
                                        onClick={() => this.handleDeleteItem(this.state.curriculumItemId)}
                                    >
                                        Xóa
                                    </button>
                                )
                            }
                        </Modal.Footer>
                    </Modal>
                </div>
            );
        });

        return (
            <SortableList
                reload={this.props.reload}
                items={this.props.items}
                onSortEnd={this.onSortEnd}
                shouldCancelStart={(e) => {
                    if (e.target.className != 'fa-bars fa') {
                        return true; // Return true to cancel sorting
                    }
                }}
            />
        )
    }
}

export default CurriculumItem;
