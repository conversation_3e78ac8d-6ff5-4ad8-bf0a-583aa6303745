import React, {Component} from 'react';
import * as api from '../api';

class LectureItemEmptyContent extends Component {
    constructor(props) {
        super(props);
        this.deleteResource = this.deleteResource.bind(this)
    }

    deleteResource(id, curriculum) {
        axios.post(api.SET_NULL_RESOURCE, {id})
            .then((r) => {
                if (r.status === 200 || r.status === 201) {
                    this.props.deleteResource(id, curriculum);
                }
            })
            .catch((e) => {
                console.log(e);
            })
    }

    render() {
        let {value} = this.props;
        let content = value.content;

        let resourceElem = value.resource.map((file, index) => {
            return (
                <div key={index} className="resource-item">
                    <div>
                        <i className="fa fa-download"></i> {file.name}
                    </div>
                    <div className="resource-item-delete">
                        <button
                            onClick={() => this.deleteResource(file.id, value)}
                        >
                            <i className="fa fa-trash-o"></i>
                        </button>
                    </div>
                </div>
            )
        })
        return (
            <div>
                <div className="table-responsive uploaded-table-result">
                    <table className="table">
                        <thead>
                        <tr>
                            <th width={350}><b>Tên file</b></th>
                            <th><b>Kiểu file</b></th>
                            <th width={190}><b>Trạng thái</b></th>
                            <th><b>Ngày tải lên</b></th>
                            <th></th>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                    <p><b>Lưu ý</b>: Tất cả các tệp phải có kích thước tối thiểu là 720p và nhỏ hơn 4.0 GB.</p>
                    {!_.isEmpty(value.resource) &&
                    <div className="downloadable-resource">
                        <p><b>Tài liệu đính kèm</b></p>
                        {resourceElem}
                    </div>
                    }
                </div>
            </div>
        )
    }
}

export default LectureItemEmptyContent;
