import React, {Component} from 'react';
import * as api from '../api';

class RelatedCurriculum extends Component {
    constructor(props) {
        super(props);
        this.state = {
            hasMore: true,
            isLoading: false,
            items: [],
            relatedItems: [],
            page: 0,
            isGet: false,
        }
        this.timer = null;
        this.submitPopoverRef = React.createRef();
    }

    componentDidMount() {
        document.addEventListener('click', this.handleClickOutside, false);

        const cId = this.props.cId;

        axios.get(api.GET_RELATED_CURRICULUM_OF_COURSE, { params: { course_id: cId } })
            .then((res) => {
                if (res.status === 200 || res.status === 201) {
                    this.setState({
                        relatedItems: res.data
                    })
                }
            })
            .catch((e) => {
                console.log(e);
            });
    }

    componentWillUnmount() {
        document.removeEventListener('click', this.handleClickOutside, false);
    }

    handleClickOutside = e => {
        if(this.submitPopoverRef.current  && this.submitPopoverRef.current.contains(e.target)) {
            // the click happened in the component
            // code to handle the submit button
            // ex: call submit();
            return;
        } 
        // click happened outside the component
        // hide the box
        this.setState({ isGet: false, page: 0, items: [] });
    };

    fetchRelated = (searchText = '') => {
        let { items } = this.state;
        const page = this.state.page + 1;

        axios.get(api.SEARCH_RELATED_CURRICULUM_OF_COURSE, { params: {
            page,
            searchText
        } }).then((res) => {
            if (res.data.items.length > 0) {
                items = items.concat(res.data.items);
            } else {
                if (res.data.page < 2) items = [{name: 'Không tìm thấy dữ liệu tương ứng!', disable: true}]
            }

            this.setState({
                items,
                page,
                isGet: true,
                hasMore: res.data.hasMore,
                isLoading: false
            });
        }).catch((e) => {
            console.log(e);
        }).finally(function() {
            //
        });
    };

    getRelated = () => {
        const searchText = this.refs.searchText.value.trim();

        if (searchText.length > 0) {
            this.setState({
                isLoading: true
            });

            this.fetchRelated(searchText);
        }
    };

    searchRelated = () => {
        const searchText = this.refs.searchText.value.trim();

        this.setState({
            isLoading: true
        });

        clearTimeout(this.timer);
        this.setState({ items: [], page: 0, hasMore: true });
        this.timer = setTimeout( () => this.fetchRelated(searchText), 2000 );
    };

    handleScrollToBottom = e => {
        const bottom = e.target.scrollHeight - e.target.scrollTop === e.target.clientHeight;

        if (bottom && this.state.hasMore) {
            this.getRelated();
        }
    };

    addRelated = e => {
        let className = e.currentTarget.className;

        if (className == 'disabled') {
            return;
        }

        let { relatedItems } = this.state;
        const cId = this.props.cId;
        const relatedId = e.currentTarget.id;

        axios.post(className != 'selected' ? api.ADD_RELATED_CURRICULUM_OF_COURSE : api.DELETE_RELATED_CURRICULUM_OF_COURSE,
            { course_id: cId, related_id: relatedId }
        ).then((res) => {
            if (res.data.related) {
                relatedItems.push(res.data.related);

                this.setState({ relatedItems, isGet: false, page: 0, items: [] });
            }
        })
    };

    deleteRelated = relatedId => {
        const cId = this.props.cId;

        axios.post(api.DELETE_RELATED_CURRICULUM_OF_COURSE, { course_id: cId, related_id: relatedId }).then((res) => {
            if (res.data.deleted) {
                this.setState(prev => ({
                  relatedItems: prev.relatedItems.filter(item => item.id !== relatedId),
                  isGet: false,
                  page: 0,
                  items: []
                }));
            }
        })
    };

    isSelected = (relatedId) => {
        const { relatedItems } = this.state;
        const index = relatedItems.findIndex(el => el.id == relatedId);

        return (index != -1) ? 'selected' : '';
    };

    render() {
        return (
            <div className="downloadable-resource mb-0">
                <div className="d-flex mt-3" style={{position: "relative"}}>
                    <p className="m-0" style={{fontSize: '13px', width: '100px'}}>Các mục liên quan khác: </p>
                    <div className="easy-autocomplete eac-description" style={{width: '100%'}} ref={this.submitPopoverRef}>
                        <input
                            type="text"
                            className="form-control"
                            placeholder="Nhập từ khóa tìm kiếm..."
                            autoComplete="off"
                            onClick={this.getRelated}
                            onChange={this.searchRelated}
                            ref="searchText"
                        />
                        <div style={{ border: '1px solid #eee' }}>
                            { this.state.isGet && (
                                <ul className="scroller" style={{margin: '0 auto', maxHeight: '300px', overflowY: 'auto'}} onScroll={this.handleScrollToBottom}>
                                    {(
                                        this.state.items.map((item) =>
                                            <li
                                                key={item.id}
                                                id={item.id}
                                                className={item.disable ? 'disabled' : this.isSelected(item.id)}
                                                style={{margin: '7px 0', cursor: 'pointer'}}
                                                onClick={this.addRelated}
                                            >
                                                <p className="m-0">
                                                    { !item.disable && <i className="fa fa-play-circle-o mr-1 text-danger"></i> } <b> {item.name}</b> ({item.id})
                                                </p>
                                            </li>
                                        )
                                    )}

                                    { this.state.isLoading && (
                                        <li>
                                            <div className="load7">
                                                <div className="loader"></div>
                                            </div>
                                        </li>
                                    )}
                                </ul>
                            ) }
                        </div>
                    </div>
                </div>
                <div className="mt-3">
                    {
                        this.state.relatedItems.map((element) =>
                            <div key={element.id} className="resource-item">
                                <div className="font-weight-bold">
                                    <i className="fa fa-play-circle-o mr-1"></i> { element.name } ({ element.id})
                                </div>
                                <div className="resource-item-delete">
                                    <button
                                       onClick={() => this.deleteRelated( element.id )} 
                                    >
                                        <i className="fa fa-trash-o"></i>
                                    </button>
                                </div>
                            </div>
                        )
                    }
                </div>
            </div>
        )
    }
}

export default RelatedCurriculum;
