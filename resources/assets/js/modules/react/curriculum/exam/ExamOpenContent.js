import React, {Component} from 'react';

class ExamOpenContent extends Component
{
    constructor(props) {
        super(props);
    }

    render() {
        const {value} = this.props;

        return(
            <div className="lecture-add-more">
                <div className="content-type-close">
                    Ch<PERSON><PERSON> kiểu câu hỏi
                    <button
                        onClick={() => this.props.handleContentStatus(value, false)}
                    ><i className="fa fa-times" aria-hidden="true"></i></button>
                </div>
                <div className="add-content-wrapper">
                    <ul className="add-content-wrapper-list text-center">
                        <li className="content-type-selector quiz-selector">
                            <button
                                onClick={() => this.props.handleOpenQuizAdd(value, true, 'test')}
                            >
                                <i className="far fa-calendar-check content-type-icon"></i>
                                <i className="far fa-calendar-check content-type-icon-hover"></i>
                                <p className=""><PERSON><PERSON><PERSON> tr<PERSON></p>
                            </button>
                        </li>
                        <li className="content-type-selector quiz-selector">
                            <button
                                onClick={() => this.props.handleOpenQuizAdd(value, true, 'blank')}
                            >
                                <i className="fas fa-underline content-type-icon"></i>
                                <i className="fas fa-underline content-type-icon-hover"></i>
                                <p className="">Câu điền từ</p>
                            </button>
                        </li>
                        <li className="content-type-selector quiz-selector">
                            <button
                                onClick={() => this.props.handleOpenQuizAdd(value, true, 'essay')}
                            >
                                <i className="fas fa-pen content-type-icon"></i>
                                <i className="fas fa-pen content-type-icon-hover"></i>
                                <p className="">Câu tự luận</p>
                            </button>
                        </li>
                    </ul>
                </div>
            </div>
        )
    }
}

export default ExamOpenContent;
