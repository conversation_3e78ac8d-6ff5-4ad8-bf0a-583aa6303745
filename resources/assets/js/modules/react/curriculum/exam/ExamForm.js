import React, {Component} from 'react';
import Tiny<PERSON><PERSON> from 'react-tinymce';
import * as api from '../../api';
import ToggleButton from 'react-toggle-button';
import {createEditorInputContentInstance} from '../../../editor';

class ExamForm extends Component {
    constructor(props) {
        super(props);
        this.state = {
            id: 0,
            title: '',
            description: '',
            random: false,
            time: 0,
            score: 0,
            listText: [],
            maxIndex: 0,
            flagIndexId: Math.floor(Date.now()/1000),
        }
    }

    componentWillMount() {
        const {exam} = this.props;

        if (exam) {
            this.setState({
                id: exam.id,
                title: exam.name,
                description: exam.description,
                random: exam.random === 'active' ? true : false,
                time: exam.time,
                score: exam.score,
                maxIndex: exam.quiz_content ? Object.keys(this.props.exam.quiz_content).length : 0
            });

            let quizContent = [];

            if (exam.quiz_content) {
                if (Array.isArray(exam.quiz_content)) {
                    quizContent = exam.quiz_content;
                } else {
                    try {
                        quizContent = JSON.parse(exam.quiz_content);
                    } catch (error) {
                        console.error('Invalid JSON string:', error);
                    }
                }
            }

            this.setState({
                listText: quizContent,
            });
        }
    }

    componentDidMount() {
        createEditorInputContentInstance();
    }

    changeTitle = (e) => {
        const title = e.target.value;

        this.setState({
            title
        })
    }

    changeDesc = (e) => {
        const description = e.target.getContent();

        this.setState({
            description
        })
    }

    changeTime = (e) => {
        const time = e.target.value;
        this.setState({time})
    }

    changeScore = (e) => {
        const score = e.target.value;
        this.setState({score})
    }

    changeToggleRandom = () => {
        const {random} = this.state;

        this.setState({random: !random})
    }

    addParagraph = () => {
        let { listText, maxIndex } = this.state;
        if (listText.length >= 20) return;
    
        const newItem = { content: '', id: `${maxIndex + 1}${Math.floor(Date.now()/1000)}` };

        this.setState(
            { listText: [...listText, newItem], maxIndex: maxIndex + 1 },
            () => createEditorInputContentInstance() // ✅ init ngay sau khi DOM có textarea mới
        );
    }

    deleteParagraph = (i) => {
        let {listText} = this.state;
        const length = listText.length;
        const newListText = this.state.listText.filter((s, _idx) => _idx !== i);
        this.setState({
            listText: newListText,
        });
    }

    handleClose = () => {
        if (typeof this.props.onClose === 'function') {
            this.props.onClose(false);
        } else if (typeof this.props.handleCloseEditItem === 'function') {
            this.props.handleCloseEditItem(this.props.exam);
        }
    }

    handleSubmit = (event) => {
        event.preventDefault();

        let {
            id,
            description,
            title,
            time,
            score,
            random,
            flagIndexId,
            listText
        } = this.state;

        if (!title) {
            alert('Bạn chưa nhập tiêu đề !');

            return;
        }

        let quizContent = [];

        listText.forEach(function (text, index) {
            const edText = tinymce.get(`text-${flagIndexId}-${text.id}`).getContent();

            if (edText) {
                quizContent.push({...text, content: edText});
            }
        });

        const data = {
            name: title,
            description: description,
            type: this.props.examType,
            time,
            score,
            quiz_content: quizContent.length > 0 ? JSON.stringify(quizContent) : null,
            random: (random === true) ? 'active' : 'disable',
            course_id: this.props.cId,
        };
        let enpoint = api.ADD_ITEM;

        if (id > 0) {
            data.id = id;
            enpoint = api.UPDATE_ITEM;
        }

        axios.post(enpoint, data)
            .then((response) => {
                if (response.status === 200 || response.status === 201) {
                    this.setState({
                        title: '',
                        description: '',
                    })
                    this.props.onSubmit(response.data);
                    this.props.onClose?.(false);
                }
            })
            .catch((error) => {
                console.log(error);
            })
    }

    render() {
        const {flagIndexId} = this.state;

        const showParagraph = this.state.listText.map((v, i) => {
            return (
               <div className="paragraph-content-wrapper" key={i}>
                   <label className="mt-2">Đoạn văn {i+1}</label>
                   <div className="answer-content-wrapper">
                       <textarea
                          className="form-control editor-input-content"
                          rows="3"
                          id={`text-${flagIndexId}-${v.id}`}
                          defaultValue={v.content}
                       >
                        </textarea>
                       <div className="answer-toolbar">
                           <button
                              onClick={() => this.deleteParagraph(i)}
                              type="button"
                           >
                               <i className="fa-trash-o fa"></i>
                           </button>
                       </div>
                   </div>
               </div>
            )
        });

        return (
            <div className="form-wrapper">
                <form className="form-section" onSubmit={this.handleSubmit}>
                    <div style={{display: 'flex'}}>
                        <div className="form-title">
                            Bài tập
                        </div>
                        <div className="form-content">
                            <label>Tiêu đề</label>
                            <div className="form-group form-group-sm">
                                <input
                                    placeholder="Nhập tiêu đề"
                                    className="form-control"
                                    type="text"
                                    value={this.state.title}
                                    onChange={this.changeTitle}
                                    required
                                />
                            </div>

                            <label>Miêu tả</label>
                            <TinyMCE
                                content={this.state.description}
                                config={{
                                    toolbar: 'bold italic',
                                    menubar: false,
                                    statusbar: false,
                                    theme: 'modern'
                                }}
                                onChange={this.changeDesc}
                            />
                            <br />
                            <label>Thời gian (phút)</label>
                            <div className="form-group form-group-sm">
                                <input
                                    className="form-control"
                                    type="number"
                                    value={this.state.time}
                                    onChange={this.changeTime}
                                />
                            </div>

                            <label>Điểm tối đa cần đạt (%)</label>
                            <div className="form-group form-group-sm">
                                <input
                                    className="form-control"
                                    type="number"
                                    value={this.state.score}
                                    onChange={this.changeScore}
                                />
                            </div>

                            <label>Trộn thứ tự câu hỏi & câu trả lời</label>
                            <div className="form-group form-group-sm">
                                <div className="toggle-item">
                                    <ToggleButton
                                        value={this.state.random}
                                        onToggle={this.changeToggleRandom}
                                    />
                                </div>
                            </div>

                            {showParagraph}
                            <button type="button" className="btn btn-info mt-2" onClick={this.addParagraph}>Thêm đoạn văn</button>
                            <span className="help-block">Có thể thêm tối đa 20 đoạn văn.</span>
                        </div>
                    </div>
                    <div className="text-right form-actions">
                        <button
                            className="btn btn-link"
                            type="button"
                            onClick={this.handleClose}
                        > Đóng
                        </button>
                        <button className="btn btn-primary" type="submit"> Gửi</button>
                    </div>
                </form>
            </div>
        )
    }
}

export default ExamForm;
