import React, {Component} from 'react';
import {Tabs, Tab} from 'react-bootstrap';
import ButtonSubmit from '../components/ButtonSubmit';
import * as api from '../api';

class YoutubeUploadTabs extends Component {
    constructor(props) {
        super(props);
        this.state = {
            key: 1,
            youtubeUrl: '',
            isButtonLoading: false,
        };
    }

    handleSelect = (key) => {
        this.setState({key});
    }

    handleInputChange = (e) => {
        this.setState({ youtubeUrl: e.target.value });
    };

    isValidYoutubeUrl = (url) => {
        // Biểu thức chính quy kiểm tra các dạng URL của YouTube
        const regex = /^(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:watch\?v=|embed\/|v\/)|youtu\.be\/)([\w-]{11})(?:\S+)?$/;
        return regex.test(url);
    };

    handleSubmit = async () => {
        const { youtubeUrl } = this.state;

        if (!youtubeUrl) {
            alert('Vui lòng nhập URL!');
            return;
        }

        if (!this.isValidYoutubeUrl(youtubeUrl)) {
            alert('URL không hợp lệ, vui lòng nhập đúng URL YouTube!');
            return;
        }

        try {
            this.setState({isButtonLoading: true});

            const response = await axios.post(api.UPLOAD_YOUTUBE, {
                url: youtubeUrl,
                userid: this.props.userid,
                curriculum: this.props.value.id,
            });

            console.log('Response:', response);

            this.setState({youtubeUrl: ''});
            this.setState({isButtonLoading: false})

            this.props.uploadLecureContent(response.data.youtube, this.props.value);
            this.props.handleOpenVideoAdd(this.props.value, false, 'youtube')
        } catch (error) {
            this.setState({isButtonLoading: false})
            console.error('Error saving URL:', error);
            alert('Lưu URL thất bại, vui lòng thử lại!');
        }
    };

    render() {
        return (
            <Tabs
                activeKey={this.state.key}
                onSelect={this.handleSelect}
                id="controlled-tab-example"
            >
                <Tab eventKey={1} title="Youtube">
                    <div className="d-flex">
                        <input
                            type="text"
                            className="form-control mr-2"
                            placeholder="Nhập URL"
                            value={this.state.youtubeUrl}
                            onChange={this.handleInputChange}
                        />
                        <ButtonSubmit
                            isLoading={this.state.isButtonLoading}
                            onClick={this.handleSubmit}
                        >
                            Lưu
                        </ButtonSubmit>
                    </div>
                </Tab>
            </Tabs>
        )
    }
}

export default YoutubeUploadTabs;
