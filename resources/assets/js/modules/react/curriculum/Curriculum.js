import React, {Component} from 'react';
import Control from './Control';
import CurriculumItem from './CurriculumItem';
import RelatedCurriculum from './RelatedCurriculum';
import * as api from '../api';
import {convertDuration} from '../helper';

class Curriculum extends Component {
    constructor(props) {
        super(props);
        this.state = {
            displaySectionAddForm: false,
            displayLectureAddForm: false,
            displayQuizAddForm: false,
            displayExamAddForm: false,
            cId: null,
            items: [],
            userid: this.props.userid,
            courseType: this.props.coursetype,
            admin: this.props.admin,
            flagIndexId: Math.floor(Date.now()/1000),
        }
    }

    /**
     * Set course_id while component mount
     * and fetch curriculum item data from service
     */
    componentDidMount() {
        let url = window.location.pathname;
        let cId = parseInt(_.split(url, '/')[3]);
        this.setState({cId});

        axios.get(api.GET_ALL_ITEMS, {
            params: {
                cId
            }
        }).then((response) => {
            if ((response.status === 200 || response.status === 201) && response?.data?.data) {
                this.setState({
                    items: response.data.data
                })
            } else {
                alert('Thất bại! Dữ liệu không được trả về .');
            }
        })
    }

    /**
     * Change state to toggle section add form
     * @param {*} value
     */
    sectionFormStatusListening = (value) => {
        this.setState({
            displaySectionAddForm: value
        })
    }

    /**
     * Change state to toggle lecture add form
     * @param {*} value
     */
    lectureFormStatusListening = (value) => {
        this.setState({
            displayLectureAddForm: value
        })
    }

    /**
     * Remove delelted item from array
     * @param {*} id
     */
    itemDeleted = (id) => {
        let {items} = this.state;
        _.remove(items, function (n) {
            return n.id === id;
        });
        this.setState({items})
    }

    itemAdded = (val) => {
        let {items} = this.state;
        items.push(val);
        this.setState({items})
    }

    itemEdited = (value) => {
        let {items} = this.state;
        let index = _.findIndex(items, ['id', value.id]);
        const newItem = { ...items[index], ...value };
        items[index] = newItem;
        this.setState({
            items,
            flagIndexId: Math.floor(Date.now()/1000)
        })
    }

    /**
     * Close all item first (set onEdit value in all item in array = false)
     * Then open selected item
     * @param {*} value
     */
    handleOpenEditItem = (value) => {
        let {items} = this.state;
        items.map((v) => {
            v.onEdit = false
        })
        let index = _.indexOf(items, value);
        value.onEdit = true;
        value.show = false;
        items[index] = value;
        this.setState({items});
    }

    handleCloseEditItem = (value) => {
        let {items} = this.state;
        let index = _.indexOf(items, value);
        value.onEdit = false;
        items[index] = value;
        this.setState({items});
    }

    onSortEnd = (items) => {
        this.setState({items})
    }

    onSortEndQuestion = (questions, cur) => {
        let {items} = this.state;
        let index = _.findIndex(items, ['id', cur.id]);
        items[index].content = questions;
        this.setState({items})
    }

    handleOpenAddLecture = (value, status) => {
        let {items} = this.state;
        let index = _.findIndex(items, ['id', value.id]);
        value.show = status;
        items[index] = value;
        this.setState({items})
    }

    openDescriptionTinyMCE = (value, status) => {
        let {items} = this.state;
        let index = _.findIndex(items, ['id', value.id]);
        value.showEditor = status;
        items[index] = value;
        this.setState({items})
    }

    onSubmitTinyMCE = (value, description) => {
        let {items} = this.state;
        let index = _.findIndex(items, ['id', value.id]);
        value.description = description;
        value.showEditor = false;
        items[index] = value;
        this.setState({items})
    }

    handleContentStatus = (value, status) => {
        let {items} = this.state;
        let index = _.findIndex(items, ['id', value.id]);
        value.showContent = status;
        value.show = false;
        items[index] = value;
        this.setState({items})
    }

    handleOpenVideoAdd = (value, status, type) => {
        let {items} = this.state;
        const index = _.findIndex(items, ['id', value.id]);

        switch(type) {
            case 'youtube':
                value.showYoutubeContent = status;
                break;
            default:
                value.showVideoContent = status;
        }

        value.showContent = false;
        items[index] = value;
        this.setState({items})
    }

    updateVideo = (value, status, type) => {
        let {items} = this.state;
        const index = _.findIndex(items, ['id', value.id]);

        switch(type) {
            case 'youtube':
                value.showYoutubeContent = status;
                break;
            default:
                value.showVideoContent = status;
        }

        value.show = false;
        this.setState({items})
    }

    uploadLecureContent = (media, value) => {
        let {items} = this.state;
        const index = _.findIndex(items, ['id', value.id]);
        value.content = media;
        value.showVideoContent = false;
        value.show = true;
        this.setState({items})
    }

    changeLectureByMediaChecking = (value) => {
        let {items} = this.state;
        let index = _.findIndex(items, ['id', value.id]);
        items[index] = value;
        this.setState({items});
    }

    changeFreeLecture = (value) => {
        let {items} = this.state;
        let index = _.findIndex(items, ['id', value.id]);
        items[index] = { ...items[index], free: value.free };
        this.setState({items})
    }

    changeStatusLecture = (value) => {
        let lecture = value.lecture;
        let section = value.section;
        let {items} = this.state;
        let index = _.findIndex(items, ['id', lecture.id]);
        let status = lecture.status;
        if (status === 'disable') {
            lecture.status = 'active'
        } else if (status === 'active') {
            lecture.status = 'disable'
        }

        if (section.id !== 0) {
            let sidx = _.findIndex(items, ['id', section.id]);
            items[index] = lecture;
            if (_.has(items[sidx], 'status')) {
                items[sidx].status = section.status;
            }
        }

        this.setState({items})
    }

    changeFreePreview = (id, value) => {
        let {items} = this.state;
        let index = _.findIndex(items, ['id', id]);
        items[index].preview = value;
        this.setState({items})
    }

    handleOpenQuizAdd = (value, status, question_type = null) => {
        let {items} = this.state;
        let index = _.findIndex(items, ['id', value.id])
        value.displayQuizForm = status;
        value.showContent = false;
        value.question_type = question_type;
        items[index] = value;
        this.setState({
            items,
            flagIndexId: Math.floor(Date.now()/1000)
        })
    }

    changeCurriculumStatus = (value) => {
        let {items} = this.state;
        let index = _.findIndex(items, ['id', value.id])
        items[index].status = value.status;
        this.setState({items})
    }

    onChangeMedia = (value, media) => {
        let {items} = this.state;
        let index = _.findIndex(items, ['id', value.id])
        items[index].showVideoContent = false;
        items[index].show = true;
        items[index].preview = 'disable';
        items[index].content = media
        this.setState({items})
    }

    handleOpenQuizContent = (value, status) => {
        let {items} = this.state;
        let index = _.findIndex(items, ['id', value.id])
        value.show = status;
        items[index] = value;
        this.setState({
            items,
            flagIndexId: Math.floor(Date.now()/1000)
        })
    }

    addNewQuestion = (value) => {
        let {items} = this.state;
        let index = _.findIndex(items, ['id', value.id])
        value.show = false;
        value.showContent = true;
        items[index] = value;

        this.setState({
            items,
            flagIndexId: Math.floor(Date.now()/1000)
        })
    }

    questionSubmited = (curriculum, question, isEdit = false) => {
        let {items} = this.state;
        let index = _.findIndex(items, ['id', curriculum.id]);
        let content = items[index].content;
        
        if (isEdit) {
            const idx = _.findIndex(content, ['id', question.id]);
            content[idx] = question;
        } else {
            content.push(question);
        }

        items[index].content = content;
        items[index].displayQuizForm = false;
        items[index].showContent = false;
        items[index].show = true;

        this.setState({
            items,
            flagIndexId: Math.floor(Date.now()/1000)
        })
    }

    deleteQuestion = (question, curriculum, multipleDel = false) => {
        let {items} = this.state;
        let index = _.findIndex(items, ['id', curriculum.id])
        let questions = items[index].content;
        _.remove(questions, function (n) {
            return multipleDel ? question.includes(n.id) : n.id == question.id;
        });
        items[index].content = questions;
        if (_.isEmpty(questions)) {
            items[index].show = false
        }
        this.setState({
            items,
            flagIndexId: Math.floor(Date.now()/1000)
        })
    }

    openResourceContent = (value, status) => {
        let {items} = this.state;
        let index = _.findIndex(items, ['id', value.id])
        value.showResourceContent = status;
        value.show = !status;
        items[index] = value;
        this.setState({items})
    }

    deleteResource = (id, curriculum) => {
        let {items} = this.state;
        let index = _.findIndex(items, ['id', curriculum.id])
        let resource = items[index].resource;
        _.remove(resource, function (n) {
            return n.id === id;
        });
        this.setState({items})
    }

    uploadNewResource = (r, curriculum) => {
        let {items} = this.state;
        let index = _.findIndex(items, ['id', curriculum.id])
        let resource = items[index].resource;
        resource.push(r);
        items[index].show = true;
        items[index].showResourceContent = false;
        this.setState({items});
    }

    onChangeResource = (curriculum, r) => {
        let {items} = this.state;
        let index = _.findIndex(items, ['id', curriculum.id])
        let resource = items[index].resource;
        resource.push(r);
        items[index].show = true;
        items[index].showResourceContent = false;
        this.setState({items});
    }

    examFormStatusListening = (status = false) => {
        this.setState({
            displayExamAddForm: status,
            displaySectionAddForm: false
        })
    }

    reload = (id) => {
        axios.post(api.UPDATE_ON_COPY, { id }).then((response) => {
            if (response.status === 200 || response.status === 201) {
                this.setState({
                    items: response.data
                })
            }
        })
    }

    render() {
        let sumItem = 0, countActive = 0, sumDuration = 0;

        if (this.props.coursetype == 'normal') {
            this.state.items.forEach(function(item) {
                if (item.type == 'lecture') {
                    sumItem++;

                    if (item.status == 'active') {
                        countActive++;

                        if (item.content) {
                            sumDuration = sumDuration + item.content.duration;
                        }
                    }
                }
            });
        }

        return this.state.cId ? (
            <div className="curriculum-editor">
                <div>
                    {this.props.coursetype == 'normal' && (
                        <div>
                            <p>Tổng bài: { sumItem }</p>
                            <p>Bài đã xuất bản: { countActive }</p>
                            <p>Tổng thời lượng video: { convertDuration(sumDuration) }</p>
                        </div>
                    )}
                    <CurriculumItem
                        cId={this.state.cId}
                        changeFreeLecture={this.changeFreeLecture}
                        reload={this.reload}
                        items={this.state.items}
                        itemDeleted={this.itemDeleted}
                        itemEdited={this.itemEdited}
                        handleOpenEditItem={this.handleOpenEditItem}
                        handleCloseEditItem={this.handleCloseEditItem}
                        onSortEnd={this.onSortEnd}
                        onSortEndQuestion={this.onSortEndQuestion}
                        handleOpenAddLecture={this.handleOpenAddLecture}
                        openDescriptionTinyMCE={this.openDescriptionTinyMCE}
                        closeTinyMce={this.openDescriptionTinyMCE}
                        onSubmitTinyMCE={this.onSubmitTinyMCE}
                        handleContentStatus={this.handleContentStatus}
                        handleOpenVideoAdd={this.handleOpenVideoAdd}
                        userid={this.props.userid}
                        admin={this.props.admin}
                        uploadLecureContent={this.uploadLecureContent}
                        updateVideo={this.updateVideo}
                        changeStatusLecture={this.changeStatusLecture}
                        changeLectureByMediaChecking={this.changeLectureByMediaChecking}
                        changeFreePreview={this.changeFreePreview}
                        handleOpenQuizAdd={this.handleOpenQuizAdd}
                        handleOpenQuizContent={this.handleOpenQuizContent}
                        changeCurriculumStatus={this.changeCurriculumStatus}
                        onChangeMedia={this.onChangeMedia}
                        questionSubmited={this.questionSubmited}
                        addNewQuestion={this.addNewQuestion}
                        deleteQuestion={this.deleteQuestion}
                        openResourceContent={this.openResourceContent}
                        deleteResource={this.deleteResource}
                        uploadNewResource={this.uploadNewResource}
                        onChangeResource={this.onChangeResource}
                        coursetype={this.props.coursetype}
                        flagIndexId={this.state.flagIndexId}
                    />
                    <Control
                        cId={this.state.cId}
                        displaySectionAddForm={this.state.displaySectionAddForm}
                        displayLectureAddForm={this.state.displayLectureAddForm}
                        displayQuizAddForm={this.state.displayQuizAddForm}
                        displayExamAddForm={this.state.displayExamAddForm}
                        sectionFormStatusListening={this.sectionFormStatusListening}
                        lectureFormStatusListening={this.lectureFormStatusListening}
                        examFormStatusListening={this.examFormStatusListening}
                        itemAdded={this.itemAdded}
                        userid={this.props.userid}
                        courseType={this.props.coursetype}
                    />
                    <RelatedCurriculum
                        cId={this.state.cId}
                    />
                </div>
            </div>
        ) : null;
    }
}

export default Curriculum;
