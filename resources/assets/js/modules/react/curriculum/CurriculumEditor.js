import React, {Component} from 'react';
import TinyMCE from 'react-tinymce';
import * as api from '../api';

class CurriculumEditor extends Component {
    constructor(props) {
        super(props);
        this.state = {
            description: ''
        }
        this.handleDescriptionChange = this.handleDescriptionChange.bind(this);
        this.closeTinyMce = this.closeTinyMce.bind(this);
        this.onSubmitTinyMCE = this.onSubmitTinyMCE.bind(this);
    }

    /**
     * Send onchange handle to parent component
     */
    handleDescriptionChange(e) {
        this.setState({description: e.target.getContent()});
    }

    closeTinyMce(value, status) {
        this.props.closeTinyMce(value, status);
    }

    onSubmitTinyMCE(value) {
        let {description} = this.state;

        axios.post(api.UPDATE_DESCRIPTION, {
            value,
            description
        }).then((response) => {
            if (response.status === 200 || response.status === 201) {
                this.props.onSubmitTinyMCE(value, description)
            }
        }).catch((error) => {
            console.log(error);
        })
    }

    render() {
        const { value } = this.props

        return (
            <form>
                <p>{this.props.title}</p>
                <TinyMCE
                    content={_.isNull(value.description) ? '' : value.description}
                    config={{
                        toolbar: 'bold italic',
                        menubar: false,
                        statusbar: false,
                        theme: 'modern'
                    }}
                    onChange={this.handleDescriptionChange}
                />
                <br/>
                <div className="text-right form-actions">
                    <button
                        className="btn btn-link"
                        onClick={() => this.closeTinyMce(value, false)}
                    > Đóng
                    </button>
                    <button
                        type="button"
                        className="btn btn-primary"
                        onClick={() => this.onSubmitTinyMCE(value)}
                    >
                        Lưu lại
                    </button>
                </div>
            </form>
        )
    }
}

export default CurriculumEditor;
