import React, {Component} from 'react';
import * as api from '../api';
import DateTime from 'react-datetime';
import moment from 'moment';
import TinyMCE from 'react-tinymce';

class CurriculumItemEdit extends Component {
    constructor(props) {
        super(props);
        this.state = {
            editTitle: '',
            editPublishTime: '',
            editDescrition: '',
            typeInput: this.props.value.type,
        }

        this.handleChangeEditTitle = this.handleChangeEditTitle.bind(this);
        this.handleChangeEditPublishTime = this.handleChangeEditPublishTime.bind(this);
        this.handleChangeEditDesc = this.handleChangeEditDesc.bind(this);
        this.handleCloseEditItem = this.handleCloseEditItem.bind(this);
        this.onChangeEditItem = this.onChangeEditItem.bind(this);
        this.handleChangeEditDescQuiz = this.handleChangeEditDescQuiz.bind(this);
        this.removePublishedTime = this.removePublishedTime.bind(this);
    }

    componentDidMount() {
        this.setState({
            editTitle: this.props.value.name,
            editDescrition: this.props.value.description,
            editPublishTime: this.props.value.published_at,
        })
    }

    handleChangeEditTitle(event) {
        let editTitle = event.target.value;
        if (!_.isNaN(editTitle)) {
            this.setState({editTitle})
        }
    }

    handleChangeEditPublishTime(event) {
        let editPublishTime = moment(event).format('YYYY-MM-DD HH:mm:ss');
        if (!_.isNaN(editPublishTime)) {
            this.setState({editPublishTime})
        }
    }

    removePublishedTime = (e) => {
        let editPublishTime = '';
        this.setState({editPublishTime})
    }

    handleChangeEditDesc(event) {
        let editDescrition = event.target.value;
        this.setState({editDescrition})
    }

    handleChangeEditDescQuiz(e) {
        let editDescrition = e.target.getContent();
        this.setState({editDescrition})
    }

    handleCloseEditItem(value) {
        this.props.handleCloseEditItem(value);
    }

    changeTypeInput = (e) => {
        let typeInput = e.target.value;
        this.setState({typeInput})
    }

    onChangeEditItem(value) {
        return event => {
            event.preventDefault()
            let {editDescrition, editTitle, editPublishTime, typeInput} = this.state;

            if (!_.isNaN(editTitle)) {
                axios.post(api.UPDATE_ITEM, {
                    id: value.id,
                    name: editTitle,
                    published_at: editPublishTime,
                    description: editDescrition,
                    type: typeInput,
                }).then((response) => {
                    if (response.status === 200 || response.status === 201) {
                        this.props.onSubmit(response.data)
                        this.handleCloseEditItem(value);
                    }
                }).catch((error) => {
                    console.log(error);
                })
            }
        }
    }

    renderTitle(val) {
        switch (val) {
            case 'section':
                return 'Phần';
            case 'lecture':
                return 'Bài học';
            case 'test':
                return 'Bài tập';
            case 'quiz':
                return 'Trắc nghiệm';
            default:
                return 'Phần';
        }
    }

    render() {
        let {value} = this.props;
        return (
            <div className="form-wrapper">
                <form className="form-section" onSubmit={this.onChangeEditItem(value)}>
                    <div style={{display: 'flex'}}>
                        <div className="form-title">
                            {this.renderTitle(value.type)}
                        </div>
                        <div className="form-content">
                            <div className="form-group form-group-sm">
                                <input
                                    maxLength="200"
                                    placeholder="Nhập tiêu đề"
                                    className="form-control"
                                    type="text"
                                    value={this.state.editTitle}
                                    onChange={this.handleChangeEditTitle}
                                    required
                                />
                            </div>

                            <div className="form-group form-group-sm">
                                <DateTime
                                    value={this.state.editPublishTime}
                                    onChange={this.handleChangeEditPublishTime}
                                    closeOnSelect={true}
                                    dateFormat="YYYY-MM-DD"
                                    timeFormat="HH:mm:ss"
                                    inputProps={{placeholder: 'Nhập Ngày Xuất bản'}}
                                />
                            </div>

                            {value.type === 'section' &&
                            <div className="form-group form-group-sm">
                                <label className="control-label">Học sinh sẽ có thể làm gì vào cuối phần này?</label>
                                <div className="form-group form-group-sm">
                                    <input
                                        maxLength="200"
                                        placeholder="Nhập miêu tả"
                                        className="form-control"
                                        type="text"
                                        value={_.isNull(this.state.editDescrition) ? '' : this.state.editDescrition}
                                        onChange={this.handleChangeEditDesc}
                                    />
                                </div>
                            </div>
                            }

                            {(value.type === 'test' || value.type === 'essay') && (
                                <div>
                                    <div style={{marginBottom: 15}} className="form-group form-group-sm">
                                        <TinyMCE
                                            content={this.state.editDescrition}
                                            config={{
                                                toolbar: 'bold italic',
                                                menubar: false,
                                                statusbar: false,
                                                theme: 'modern'
                                            }}
                                            onChange={this.handleChangeEditDescQuiz}
                                        />
                                    </div>
                                    <div style={{marginBottom: 15}} className="form-group form-group-sm">
                                        <label>Loại</label>
                                        <select
                                            value={this.state.typeInput}
                                            className="form-control"
                                            data-placeholder="Chọn loại"
                                            name="type"
                                            onChange={this.changeTypeInput}
                                        >
                                            <option value="test">Trắc nghiệm</option>
                                            <option value="essay">Tự luận</option>
                                        </select>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                    <div className="text-right form-actions">
                        <button
                            type="button"
                            className="btn btn-link"
                            onClick={this.removePublishedTime}
                        > Xóa ngày xuất bản
                        </button>
                        <button
                            className="btn btn-link"
                            onClick={() => this.handleCloseEditItem(value)}
                        > Đóng
                        </button>
                        <button className="btn btn-primary"> Lưu lại</button>
                    </div>
                </form>
            </div>
        )
    }
}

export default CurriculumItemEdit;
