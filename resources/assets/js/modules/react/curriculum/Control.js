import React, {Component} from 'react';
import * as api from '../api';
import DateTime from 'react-datetime';
import moment from 'moment';
import ExamForm from './exam/ExamForm';

class Control extends Component {
    constructor(props) {
        super(props);
        this.state = {
            sectionTitle: '',
            sectionPublishTime: '',
            sectionDesc: '',
            lectureTitle: '',
            lectureDesc: '',
            startDate: new Date(),
            idClone: '',
        }
    }

    /**
     * Return props to parent component listen toggle status of section add form
     * @param {*} value
     */
    sectionFormStatusListening = (value) => {
        this.props.sectionFormStatusListening(value)
    }

    /**
     * Return props to parent component listen toggle status of lecture add form
     * @param {*} value
     */
    lectureFormStatusListening = (value) => {
        this.props.lectureFormStatusListening(value)
    }

    /**
     * Listen the changing of the section add form title field
     * then set new state to sectionTitle
     * @param {*} event
     */
    changeSectionTitle = (event) => {
        const sectionTitle = event.target.value;
        if (!_.isNaN(sectionTitle)) {
            this.setState({sectionTitle})
        }
    }

    changesectionPublishTime = (event) => {
        const sectionPublishTime = moment(event).format('YYYY-MM-DD HH:mm:ss');
        if (!_.isNaN(sectionPublishTime)) {
            this.setState({sectionPublishTime})
        }
    }

    removePublishedTime = (e) => {
        this.setState({sectionPublishTime: ''})
    }

    /**
     * Listen the changing of the section add form description field
     * then set new state to sectionDesc
     * @param {*} event
     */
    changeSectionDesc = (event) => {
        const sectionDesc = event.target.value;
        if (!_.isNaN(sectionDesc)) {
            this.setState({sectionDesc})
        }
    }

    changeIDClone = (event) => {
        const idClone = event.target.value;
        if (!_.isNaN(idClone)) {
            this.setState({idClone})
        }
    }

    /**
     * Add new section
     * @param {*} event
     */
    addNewSection = (type) => {
        return event => {
            event.preventDefault();

            const {idClone} = this.state;

            if (idClone) {
                axios.post(api.CLONE_ITEM, {
                    course_id: this.props.cId,
                    id: idClone
                }).then((response) => {
                    if (response.status === 200 || response.status === 201) {
                        this.setState({
                            sectionTitle: '',
                            sectionPublishTime: '',
                            sectionDesc: '',
                            idClone: '',
                        })
                        this.sectionFormStatusListening(false)
                        this.lectureFormStatusListening(false)
                        this.props.itemAdded(response.data);
                    }
                })
            } else {
                const {sectionTitle, sectionPublishTime, sectionDesc} = this.state;

                if (sectionTitle && !_.isNaN(sectionTitle)) {
                    axios.post(api.ADD_ITEM, {
                        course_id: this.props.cId,
                        name: sectionTitle,
                        published_at: sectionPublishTime,
                        description: sectionDesc,
                        type: type,
                        userid: this.props.userid
                    }).then((response) => {
                        if (response.status === 200 || response.status === 201) {
                            this.setState({
                                sectionTitle: '',
                                sectionPublishTime: '',
                                sectionDesc: '',
                                idClone: '',
                            })
                            this.sectionFormStatusListening(false)
                            this.lectureFormStatusListening(false)
                            this.props.itemAdded(response.data);
                        }
                    })
                } else {
                    alert('Vui lòng nhập tiêu đề.');
                }
            }
        }
    }

    render() {
        return (
            <div className="curriculum-control">
                {this.props.displaySectionAddForm === true &&
                <div className="form-wrapper">
                    <form className="form-section" onSubmit={this.addNewSection('section')}>
                        <div style={{display: 'flex'}}>
                            <div className="form-title">
                                Phần mới
                            </div>
                            <div className="form-content">
                                <div className="form-group form-group-sm">
                                    <input
                                        placeholder="Nhập tiêu đề"
                                        className="form-control"
                                        type="text"
                                        value={this.state.sectionTitle}
                                        onChange={this.changeSectionTitle}
                                        required
                                    />
                                </div>
                                <label className="control-label">Học sinh sẽ có thể làm gì vào cuối phần này?</label>
                                <div className="form-group form-group-sm">
                                    <input
                                        maxLength="200"
                                        placeholder="Nhập miêu tả"
                                        className="form-control"
                                        type="text"
                                        value={this.state.sectionDesc}
                                        onChange={this.changeSectionDesc}
                                    />
                                </div>
                            </div>
                        </div>
                        <div className="text-right form-actions">
                            <button
                                className="btn btn-link"
                                onClick={() => this.sectionFormStatusListening(false)}
                            > Đóng
                            </button>
                            <button className="btn btn-primary" type="submit"> Thêm phần</button>
                        </div>
                    </form>
                </div>
                }

                {this.props.displayLectureAddForm === true &&
                <div className="form-wrapper">
                    <form className="form-section" onSubmit={this.addNewSection('lecture')}>
                        <div style={{display: 'flex'}}>
                            <div className="form-title">
                                Bài học mới
                            </div>
                            <div className="form-content">
                                <div className="row mb-2">
                                    <div className="col-12">
                                        <input
                                            placeholder="Nhập tiêu đề"
                                            className="form-control"
                                            type="text"
                                            value={this.state.sectionTitle}
                                            onChange={this.changeSectionTitle}
                                        />
                                    </div>
                                    <div className="col-4">
                                        <input
                                            placeholder="hoặc nhân bản bằng ID"
                                            type="text"
                                            className="form-control"
                                            onChange={this.changeIDClone}
                                        />
                                    </div>
                                </div>
                                <DateTime
                                    value={this.state.sectionPublishTime}
                                    onChange={this.changesectionPublishTime}
                                    closeOnSelect={true}
                                    dateFormat="YYYY-MM-DD"
                                    timeFormat="HH:mm:ss"
                                    inputProps={{placeholder: 'Nhập Ngày Xuất bản'}}
                                />
                            </div>
                        </div>
                        <div className="text-right form-actions">
                            <button
                                type="button"
                                className="btn btn-link"
                                onClick={this.removePublishedTime}
                            > Xóa ngày xuất bản
                            </button>
                            <button
                                className="btn btn-link"
                                onClick={() => this.lectureFormStatusListening(false)}
                            > Đóng
                            </button>
                            <button
                                className="btn btn-primary"
                                type="submit"
                            > Thêm bài học
                            </button>
                        </div>
                    </form>
                </div>
                }

                {this.props.displayExamAddForm === true &&
                <ExamForm
                    cId={this.props.cId}
                    onClose={this.props.examFormStatusListening}
                    onSubmit={this.props.itemAdded}
                    userid={this.props.userid}
                    examType={this.props.courseType}
                />
                }

                {(this.props.displaySectionAddForm === false) &&
                    <button
                        className="btn btn-outline-primary btn-100"
                        onClick={() => this.sectionFormStatusListening(true)}
                    >
                        <span className="fa fa-plus-square"></span> Thêm phần
                    </button>
                }

                {this.props.displayLectureAddForm === false &&
                <>
                    <button
                        className={`btn btn-outline-primary btn-100`}
                        onClick={() => this.lectureFormStatusListening(true)}
                    >
                        <span className="fa fa-plus-square"></span> Thêm bài học
                    </button>
                    <button
                        className={`btn btn-outline-primary btn-100`}
                        onClick={() => this.props.examFormStatusListening(true)}
                    >
                        <span className="fa fa-plus-square"></span>
                        { this.props.courseType == 'essay' && 'Thêm tự luận' }
                        { this.props.courseType == 'test' && 'Thêm trắc nghiệm'}
                    </button>
                </>
                }
            </div>
        )
    }
}

export default Control;
