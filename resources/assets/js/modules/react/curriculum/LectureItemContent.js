import React, {Component} from 'react';
import {convertDuration} from '../helper';
import * as api from '../api';
import ToggleButton from 'react-toggle-button'

class LectureItemContent extends Component {
    constructor(props) {
        super(props);
        this.state = {
            rejectReason: false,
            isGetTest: false,
            testForLecturePage: 0,
            tests: [],
            hasMore: true,
            isLoading: false,
            testsSelected: [],
            percent: 0,
        }
        this.updateVideo = this.updateVideo.bind(this);
        this.changeStatusLecture = this.changeStatusLecture.bind(this);
        this.changeFreePreview = this.changeFreePreview.bind(this);
        this.deleteResource = this.deleteResource.bind(this)

        this.timer = null;
        this.submitPopoverRef = React.createRef();
    }

    componentDidMount() {
        document.addEventListener('click', this.handleClickOutside, false);

        const lectureId = this.props.value.id;

        axios.get(api.GET_TEST_OF_LECTURE, { params: { lecture_id: lectureId } }).then((res) => {
            this.setState({ testsSelected: res.data.tests });
        })
    }

    componentWillUnmount() {
        document.removeEventListener('click', this.handleClickOutside, false);
    }

    updateVideo(value, status, type) {
        this.props.updateVideo(value, status, type);
    }

    handleUploadVideoSD = (data) => {
        let value = this.props.value;
        if (value.content.checking == 7) {
            var r = confirm('Đã có bản SD. Bạn có chắc muốn tạo lại!');

            if (r == false) {
                return;
            }
        }

        if (value.content.checking == 5 || value.content.checking == 6) {
            alert('Video đang convert, vui lòng thử lại sau!');

            return;
        }

        const file = data[0];
        const media_id = this.props.value.content.id;
        let formData = new FormData();

        formData.append('file', file)
        formData.append('media_id', media_id)

        const config = {
            headers: {'Content-Type': 'multipart/form-data'},
            onUploadProgress: progressEvent => {
                let percent = Math.round(progressEvent.loaded / progressEvent.total * 100);
                this.setState({percent});
            }
        }

        axios.post(api.UPLOAD_VIDEO_SD, formData, config)
            .then((res) => {
                if (res.status === 200 || res.status === 201) {
                    if (res.data.uploaded == true) {
                        value.content.checking = true;

                        this.props.changeLectureByMediaChecking(value);
                    }

                    if (res.data.message) {
                        alert(res.data.message);
                    }
                }
            })
            .catch((error) => {
                console.log(error);
            })
    }

    downloadVideo = (url) => {
        window.location = api.DOWNLOAD_VIDEO + '?' + $.param({ url });
    }

    convertVideoToSD = (media_id) => {
        let value = this.props.value;
        if (value.content.checking == 7) {
            var r = confirm('Đã có bản SD. Bạn có chắc muốn tạo lại!');

            if (r == false) {
                return;
            }
        }

        if (value.content.checking == 5 || value.content.checking == 6) {
            alert('Video đang convert, vui lòng thử lại sau!');

            return;
        }

        axios.post(api.CONVERT_VIDEO_TO_SD, { media_id }).then((res) => {
            if (res.status === 200 || res.status === 201) {
                if (res.data.convert == 'processing') {
                    value.content.checking = true;

                    this.props.changeLectureByMediaChecking(value);
                }

                if (res.data.message) {
                    alert(res.data.message);
                }
            }
        });
    }

    changeFreePreview(value) {
        let preview = (value.preview === 'active') ? 'disable' : 'active';
        axios.post(api.UPDATE_PREVIEW, {
            value
        }).then((resp) => {
            if (resp.status === 200 || resp.status === 201) {
                this.props.changeFreePreview(value.id, preview)
            }
        })
    }

    changeStatusLecture(value) {
        axios.post(api.UPDATE_STATUS, {
            value
        }).then((resp) => {
            if (resp.status === 200 || resp.status === 201) {
                let data = {
                    lecture: value,
                    section: {
                        id: resp.data.section.id,
                        status: resp.data.section.status
                    }
                }
                this.props.changeStatusLecture(data);
            }
        }).catch((err) => {
            console.log(err)
        })
    }

    deleteResource(id, curriculum) {
        axios.post(api.SET_NULL_RESOURCE, {id})
            .then((r) => {
                if (r.status === 200 || r.status === 201) {
                    this.props.deleteResource(id, curriculum);
                }
            })
            .catch((e) => {
                console.log(e);
            })
    }

    handleClickOutside = e => {
        if(this.submitPopoverRef.current  && this.submitPopoverRef.current.contains(e.target)) {
            // the click happened in the component
            // code to handle the submit button
            // ex: call submit();
            return;
        } 
        // click happened outside the component
        // hide the box
        this.setState({ isGetTest: false, testForLecturePage: 0, tests: [] });
    };

    freshDataTestForLecture = (searchText = '') => { // arrow function needn't bind function in constructor
        let { tests } = this.state;
        let { id, classlevel, subject } = this.props.value;
        let page = this.state.testForLecturePage + 1;
        let data = {id, classlevel, subject, page, searchText}

        axios.get(api.GET_TEST_FOR_LECTURE, { params: { ...data } }).then((res) => {

            if (res.data.tests.length > 0) {
                tests = tests.concat(res.data.tests);
            } else {
                if (res.data.page < 2) tests = [{name: 'Không tìm thấy phần trắc nghiệm tương ứng!', disable: true}]
            }

            this.setState({
                tests,
                testForLecturePage: page,
                isGetTest: true,
                hasMore: res.data.hasMore,
                isLoading: false
            });
        }).catch((e) => {
            console.log(e);
        }).finally(function() {

        });
    };

    getTestForLecture = () => {
        const searchText = this.refs.searchText.value.trim();

        this.setState({
            isLoading: true
        });

        this.freshDataTestForLecture(searchText);
    };

    searchTestForLecture = () => {
        const searchText = this.refs.searchText.value.trim();

        this.setState({
            isLoading: true
        });

        // if (searchText.length > 0) {
            clearTimeout(this.timer);
            this.setState({ tests: [], testForLecturePage: 0, hasMore: true });
            this.timer = setTimeout( () => this.freshDataTestForLecture(searchText), 2000 );
        // }
    };

    handleScrollToBottom = e => {
        const bottom = e.target.scrollHeight - e.target.scrollTop === e.target.clientHeight;

        if (bottom && this.state.hasMore) {
            this.getTestForLecture();
        }
    };

    addTestForLecture = e => {
        let className = e.currentTarget.className;

        if (className == 'disabled') {
            return;
        }

        let { testsSelected } = this.state;
        const lectureId = this.props.value.id;
        const testId = e.currentTarget.id;
        
        axios.post(className != 'selected' ? api.ADD_TEST_FOR_LECTURE : api.DELETE_TEST_FOR_LECTURE,
            { lecture_id: lectureId, test_id: testId }
        ).then((res) => {
           if (res.status === 200 || res.status === 201) {
                testsSelected = res.data.tests;

                this.setState({ testsSelected, isGetTest: false, testForLecturePage: 0, tests: [] });
           }
        })
    };

    deleteTestForLecture = testId => {
        const lectureId = this.props.value.id;
        let { testsSelected } = this.state;

        axios.post(api.DELETE_TEST_FOR_LECTURE, { lecture_id: lectureId, test_id: testId }).then((res) => {
           if (res.status === 200 || res.status === 201) {
                testsSelected = res.data.tests;

                this.setState({ testsSelected, isGetTest: false, testForLecturePage: 0, tests: [] });
           }
        })
    };

    isSelected = (testId) => {
        let { testsSelected } = this.state;
        let index = testsSelected.findIndex(el => el.id == testId);

        return (index != -1) ? 'selected' : '';
    };

    handleTestForLecture = () => {
        return (
            <div className="downloadable-resource">
                <div className="d-flex" style={{position: "relative"}}>
                    <b style={{fontSize: '12px'}}>Bài trắc nghiệm: </b>
                    <div className="easy-autocomplete eac-description" style={{width: '100%'}} ref={this.submitPopoverRef}>
                        <input
                            type="text"
                            className="form-control"
                            placeholder="Nhập từ khóa tìm kiếm..."
                            autoComplete="off"
                            onClick={this.getTestForLecture}
                            onChange={this.searchTestForLecture}
                            ref="searchText"
                        />
                        <div className="easy-autocomplete-container">
                        { this.state.isGetTest && (
                            <ul className="scroller" style={{margin: '0 auto', maxHeight: '300px', overflow: 'auto'}} onScroll={this.handleScrollToBottom}>
                                {(
                                    this.state.tests.map((test, key) =>
                                        <li
                                            key={key}
                                            id={test.id}
                                            className={test.disable ? 'disabled' : this.isSelected(test.id)}
                                            onClick={this.addTestForLecture}
                                        >
                                            <p style={{margin: '7px 0'}}>
                                                <i className="fa fa-check-square-o"></i> <b> {test.name}</b>
                                            </p>
                                        </li>
                                    )
                                )}

                                { this.state.isLoading && (
                                    <li>
                                        <div className="load7">
                                            <div className="loader"></div>
                                        </div>
                                    </li>
                                )}
                            </ul>
                        )} 
                        </div>
                    </div>
                </div>
                <div style={{ marginTop: '20px' }}>
                    {
                        this.state.testsSelected.map((element, key) =>
                            <div key={key} className="resource-item">
                                <div>
                                    <i className="fa fa-check-square-o"></i> { element.name }
                                </div>
                                <div className="resource-item-delete">
                                    <button
                                       onClick={() => this.deleteTestForLecture( element.id )} 
                                    >
                                        <i className="fa fa-trash-o"></i>
                                    </button>
                                </div>
                            </div>
                        )
                    }
                </div>
            </div>
        );
    };

    render() {
        let {value} = this.props;
        let content = value.content;
        let className = content.status === 'processing' ? 'success' : 'fail';
        let switchToggle = (value.preview) === 'active' ? true : false;

        let resourceElem = value.resource.map((file, index) => {
            return (
                <div key={index} className="resource-item">
                    <div>
                        <i className="fa fa-download"></i> {file.name}
                    </div>
                    <div className="resource-item-delete">
                        <button
                            onClick={() => this.deleteResource(file.id, value)}
                        >
                            <i className="fa fa-trash-o"></i>
                        </button>
                    </div>
                </div>
            )
        })

        return (
            <div>
                {content.status === 'active' ? (
                    <div>
                        <div className="lecture-content-container">
                            <div className="lecture-content-wrapper">
                                <div>
                                    { content.type == 'video/mp4' && <img className="img-fluid" src={api.API_URL + content.thumbnail} width={110}/> }
                                    { content.type == 'youtube' && <img className="img-fluid" src={`https://img.youtube.com/vi/${ content.name }/default.jpg`} width={110}/> }
                                    <div className="lecture-content-info">
                                        <p className="lecture-content-title mt-1">
                                            { content.type == 'video/mp4' && <span>[{convertDuration(content.duration)}]</span> }
                                            { content.type == 'youtube' && <span>[Youtube]</span> }
                                            <b className="ml-1">{content.name}</b>
                                        </p>
                                        <button
                                            onClick={() => this.updateVideo(value, true, content.type)}
                                        >
                                            <i className="fa fa-pencil"></i> Thay nội dung
                                        </button>
                                        { content.type == 'video/mp4' && (
                                            <>
                                                <button
                                                    onClick={() => this.downloadVideo(content.raw_url)}
                                                >
                                                    <i className="fa fa-download"></i> Download video
                                                </button>
                                                <span> | </span>&nbsp;
                                                {(() => {
                                                    switch (content.checking) {
                                                        case true:
                                                        case 5:
                                                        case 6:
                                                            return (<span style={{ color: '#ff9800' }}> Đang xử lý bản SD ... &nbsp;&nbsp;</span>);
                                                        default:
                                                            return (
                                                                <span>
                                                                    {content.checking == 7 && (<span style={{ color: 'red' }}> Đã có bản SD: &nbsp;&nbsp;</span>)}
                                                                    <label style={{ cursor: 'pointer' }}>
                                                                        <input
                                                                            name="videosd"
                                                                            type="file"
                                                                            accept=".mp4"
                                                                            className="d-none"
                                                                            onChange={(e) => this.handleUploadVideoSD(e.target.files)}
                                                                        />
                                                                        <i className="fa fa-upload"></i> Tải bản SD
                                                                        {(this.state.percent > 0 && this.state.percent < 100) && (
                                                                            <span style={{ color: 'red' }}> (
                                                                                {this.state.percent + ' %'}
                                                                            )</span>
                                                                        )}
                                                                    </label>&nbsp;&nbsp;
                                                                    <button
                                                                        onClick={() => this.convertVideoToSD(content.id)}
                                                                    >
                                                                        <i className="fa fa-edit"></i> Tạo bản SD
                                                                    </button>
                                                                </span>
                                                            );
                                                    }
                                                  })()}
                                            </>
                                        ) }
                                    </div>
                                </div>
                                <div>
                                    <div className="lecture-content-action">
                                        {value.status === 'disable' &&
                                        <button
                                            onClick={() => this.changeStatusLecture(value)}
                                            className="btn btn-danger"
                                        >Xuất bản</button>
                                        }

                                        {value.status === 'active' &&
                                        <button
                                            onClick={() => this.changeStatusLecture(value)}
                                            className="btn btn-outline-danger"
                                        >Ngừng xuất bản</button>
                                        }
                                    </div>
                                    <div className="toogle-lecture">
                                        <strong>Xem Free</strong>&nbsp;&nbsp;
                                        <div className="toggle-item">
                                            <ToggleButton
                                                value={switchToggle}
                                                onToggle={() => this.changeFreePreview(value)}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {!_.isEmpty(value.resource) &&
                            <div className="downloadable-resource">
                                <p><b>Tài liệu đính kèm</b></p>
                                {resourceElem}
                            </div>
                        }
                        { this.props.coursetype == 'normal' && this.handleTestForLecture() }
                    </div>
                ) : (
                    <div className="table-responsive uploaded-table-result">
                        <table className="table">
                            <thead>
                            <tr>
                                <th width={350}><b>Tên file</b></th>
                                <th><b>Kiểu file</b></th>
                                <th width={190}><b>Trạng thái</b></th>
                                <th><b>Ngày tải lên</b></th>
                                <th></th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>
                                    <p>- Tên: {content.name}</p>
                                    <p>- Url: {content.raw_url}</p>
                                </td>
                                <td><strong>{content.type}</strong></td>
                                <td width={190} className="upload-result-status">
                                    <p className={className}>
                                        {(content.status === 'processing') ? 'Đang chờ xử lý' : ((content.status === 'processed') ? 'Đang xử lý' : 'Từ chối')}
                                    </p>
                                </td>
                                <td>{content.created_at.date}</td>
                                <td className="text-center">
                                    <button className="upload-replace d-flex align-items-center border mb-2" onClick={() => this.updateVideo(value, true, content.type)}>
                                        <i className="fa fa-edit mr-1"></i>
                                        Change
                                    </button>
                                    <button
                                        onClick={() => {
                                            this.setState({rejectReason: !this.state.rejectReason})
                                        }}
                                        className="upload-fail-reason d-flex align-items-center border"
                                    >
                                        {this.state.rejectReason === true ? (
                                            <i className="fa fa-angle-up mr-1"></i>
                                        ) : (
                                            <i className="fa fa-angle-down mr-1"></i>
                                        )}
                                        Open
                                    </button>
                                </td>
                            </tr>
                            {this.state.rejectReason === true &&
                                <tr className="reject_reason">
                                    <td colSpan={5}>
                                        <b>Video bạn tải lên bị từ chối tự động vì lý do sau:</b>
                                        <ul>
                                            <li>{content.reject_reason}</li>
                                        </ul>
                                    </td>
                                </tr>
                            }
                            </tbody>
                        </table>
                        <p>
                            <b>Lưu ý</b>: Tất cả các tệp phải có kích thước tối thiểu là 720p và nhỏ hơn 4.0 GB.
                        </p>
                        {!_.isEmpty(value.resource) &&
                            <div className="downloadable-resource">
                                <p><b>Tài liệu đính kèm</b></p>
                                {resourceElem}
                            </div>
                        }
                    </div>
                )}
            </div>
        )
    }
}

export default LectureItemContent;
