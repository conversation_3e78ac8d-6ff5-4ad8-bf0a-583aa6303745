export const decodeHtml = (htmlString) => {
    if (!htmlString) return '';

    const txt = document.createElement("textarea");
    txt.innerHTML = htmlString;
    return txt.value;
}

export const convertDuration = (seconds) => {
    return [
            parseInt(seconds / 60 / 60),
            parseInt(seconds / 60 % 60),
            parseInt(seconds % 60)
        ]
        .join(":")
        .replace(/\b(\d)\b/g, "0$1")
}

export const afterPaint = (fn) => requestAnimationFrame(() => setTimeout(fn, 0));

export const normalizeStr = (s = '') =>
  String(s)
    .replace(/\u00A0/g, ' ')     // NBSP -> space thường
    .replace(/\s+/g, ' ')        // gộp mọi khoảng trắng liên tiếp thành 1
    .trim();

export function checkImageContent(content = '') {
    let blackList = ['.googleusercontent.', ';base64,'];

    return contains(content, blackList);
}

function contains(target, pattern){
    let value = 0;

    pattern.forEach(function(word){
        value = value + target.includes(word);
    });

    return (value === 1)
}

export function handleHtml(html) {
    if (!html) return '';

    var root = document.createElement('div'),
      spanItems = root.getElementsByTagName('span');

    root.innerHTML = html;

    Array.prototype.forEach.call(spanItems, function (e) {
        e.removeAttribute('style');
        e.removeAttribute('class');

        if (e.hasAttribute('data-mathml')) {
            e.innerHTML = e.getAttribute('data-mathml');
            e.removeAttribute('data-mathml');
        }
    });

    return root.innerHTML;
}

export const removeKey = (obj, keyToRemove) => {
    const { [keyToRemove]: removed, ...rest } = obj;
    return rest;
};
