import ReactDom from 'react-dom';
import Quiz from './quiz/Quiz';
import ShareQuiz from './quiz/ShareQuiz';

const initQuiz = document.getElementById('initQuiz');

if (initQuiz) {
    const props = Object.assign({}, initQuiz.dataset);

    ReactDom.render(
        <Quiz {...props}/>,
        initQuiz
    );
}

const initShareQuiz = document.getElementById('initShareQuiz');

if (initShareQuiz) {
    const props = Object.assign({}, initShareQuiz.dataset);

    ReactDom.render(
        <ShareQuiz {...props}/>,
        initShareQuiz
    );
}
