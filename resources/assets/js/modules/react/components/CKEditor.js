import React, { useState } from 'react';
import useCKEditor from '../hook/useCKEditor';

function CKEditor({ config, editorUrl, initData, name, type, onInstanceReady, onChange }) {
    console.log('CKEditor');
    const [element, setElement] = useState(null);

    const newEditor = useCKEditor({
        config,
        editorUrl,
        element,
        initContent: typeof initData === 'string' ? initData : undefined,
        type,
        onInstanceReady,
        onChange,
    });

    // if (newEditor) newEditor.setData('<math xmlns="http://www.w3.org/1998/Math/MathML"><mi>x</mi><mo>=</mo><mfrac><mrow><mo>-</mo><mi>b</mi><mo>&#x000B1;</mo><msqrt><msup><mi>b</mi><mn>2</mn></msup><mo>-</mo><mn>4</mn><mi>a</mi><mi>c</mi></msqrt></mrow><mrow><mn>2</mn><mi>a</mi></mrow></mfrac></math>');

    return (
        <textarea
            rows="3"
            id={name ?? undefined}
            ref={setElement}
            defaultValue={typeof initData === 'string' ? initData : ''}
        />
        // <div
        //     id={name ?? undefined}
        //     ref={setElement}
        // >
        //     {typeof initData === 'string' ? null : initData}
        // </div>
    );
}

export default CKEditor;
