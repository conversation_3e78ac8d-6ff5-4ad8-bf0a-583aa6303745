import React, {
  useRef,
  useLayoutEffect,
  useCallback,
  useState,
  useMemo
} from 'react';

const AutoResizeInput = React.memo(({
  value,
  onChange = () => {},
  placeholder = '',
  style: styleProp = {},
  minWidth = 60,
  transitionDuration = '0.15s',
  transitionTimingFunction = 'ease',
  ...props
}) => {
  const inputRef = useRef(null);

  const [internalValue, setInternalValue] = useState(value);
  const isControlled = value != null;
  const displayValue  = isControlled ? value : internalValue;

  // Tạo 1 canvas/ctx tái sử dụng
  const { canvas, ctx } = useMemo(() => {
    const c = document.createElement('canvas');
    return { canvas: c, ctx: c.getContext('2d') };
  }, []);

  const combinedStyle = useMemo(() => ({
    transition: `width ${transitionDuration} ${transitionTimingFunction}`,
    minWidth: `${minWidth}px`,
    ...styleProp
  }), [
    transitionDuration,
    transitionTimingFunction,
    minWidth,
    styleProp
  ]);

  const resizeInput = useCallback((text) => {
    const el = inputRef.current;
    if (!el) return;

    const cs = window.getComputedStyle(el);
    ctx.font = `${cs.fontStyle} ${cs.fontWeight} ${cs.fontSize} ${cs.fontFamily}`;

    const content = text || placeholder;
    const textWidth = ctx.measureText(content).width;

    const paddingLR = parseFloat(cs.paddingLeft) + parseFloat(cs.paddingRight);
    const borderLR  = parseFloat(cs.borderLeftWidth) + parseFloat(cs.borderRightWidth);

    const newWidth = Math.max(
      minWidth,
      textWidth + paddingLR + borderLR + 2
    );
    el.style.width = `${newWidth}px`;
  }, [ctx, placeholder, minWidth]);

  // Resize on mount và khi displayValue thay đổi
  useLayoutEffect(() => {
    resizeInput(displayValue);
  }, [resizeInput, displayValue]);

  // Stable handler
  const handleChange = useCallback((e) => {
    const txt = e.target.value;
    if (!isControlled) setInternalValue(txt);
    onChange(e);
    resizeInput(txt);
  }, [isControlled, onChange, resizeInput]);

  return (
    <input
      className="auto-resize-input"
      name="blank"
      {...props}
      ref={inputRef}
      value={displayValue}
      onChange={handleChange}
      placeholder={placeholder}
      style={combinedStyle}
    />
  );
});

export default AutoResizeInput;
