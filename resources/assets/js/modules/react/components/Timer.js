import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';

const Timer = forwardRef(({ initTimer, isPaused, onPause, onSubmitExam }, ref) => {
    const [time, setTime] = useState(initTimer || 0);

    useImperativeHandle(ref, () => ({
        getTime: () => time,
    }), [time]);

    useEffect(() => {
        let timer;

        if (!isPaused) {
            timer = setInterval(() => {
                setTime((prevTime) => {
                    if (initTimer) {
                        const newTime = prevTime > 0 ? prevTime - 1 : 0;
                        return newTime;
                    } else {
                        return prevTime + 1;
                    }
                });
            }, 1000);
        }

        return () => clearInterval(timer);
    }, [initTimer, isPaused]);

    useEffect(() => {
        if (initTimer && time === 0) {
            onPause()
            alert("<PERSON>ết thời gian làm bài! Vui lòng nộp bài để không bị hủy bài làm.");
            onSubmitExam();
        }
    }, [time, initTimer, onPause, onSubmitExam]);

    const formatTime = (seconds) => {
        const m = Math.floor(seconds / 60);
        const s = (seconds % 60).toString().padStart(2, '0');
        return `${m}:${s}`;
    };

    const timerStyle = {
        color: initTimer && time <= 60 ? 'red' : 'inherit',
    };

    return (
        <div className="timer-wrapper">
            <span id="timer" style={timerStyle}>{formatTime(time)}</span>
        </div>
    );
});

export default Timer;
