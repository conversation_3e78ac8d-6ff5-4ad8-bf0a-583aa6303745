import React, { memo, useMemo, createContext, useContext, useRef } from 'react';
import parse from 'html-react-parser';
import AutoResizeInput from './AutoResizeInput';
import useMathJax from '../hook/useMathJax';

const AnswersCtx = createContext({ answerInputs: {}, handleInputChange: () => {} });

const BlankInput = React.memo(({ id }) => {
  const { answerInputs, handleInputChange, saveData } = useContext(AnswersCtx);
  const check = saveData?.result?.[id] ?? null;

  return (
    <AutoResizeInput
      key={id}
      value={answerInputs[id] || ''}
      onChange={e => handleInputChange(id, e.target.value)}
      placeholder=""
      className={`auto-resize-input ${check !== null ? (check ? 'correct' : 'wrong') : ''}`}
    />
  );
});

const HtmlContent = memo(({ html = '', contentType = null, answerInputs = {}, handleInputChange = () => {}, saveData= {}, ...rest }) => {
  const ref = useRef(null);

  const reactTree = useMemo(() => {
    if (contentType === 'blank') {
      return parse(html, {
        replace: (node) => {
          if (
            node.type === 'tag' &&
            node.name === 'span' &&
            node.attribs?.class?.split(' ').includes('flag-el')
          ) {
            const { id } = node.attribs;

            return <BlankInput key={id} id={id} />;
          }

          return undefined;
        }
      });
    }

    return parse(html);
  }, [html, contentType]);

  useMathJax(ref, [html, contentType]);

  return (
    <AnswersCtx.Provider value={{ answerInputs, handleInputChange, saveData }}>
      <div ref={ref} {...rest}>{reactTree}</div>
    </AnswersCtx.Provider>
  );
});

export default HtmlContent;
