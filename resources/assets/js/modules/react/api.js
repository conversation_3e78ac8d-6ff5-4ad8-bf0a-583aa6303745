export const APP_ENV = process.env.APP_ENV;
export const BASE_URL = '/admincp/';
export const API_URL = process.env.MIX_APP_URL;

export const ADD_ITEM = BASE_URL + 'services/course/curriculum/add-item';
export const GET_ALL_ITEMS = BASE_URL + 'services/course/curriculum/get-all-items';
export const DELETE_ITEM = BASE_URL + 'services/course/curriculum/delete-item';
export const UPDATE_ITEM = BASE_URL + 'services/course/curriculum/update-item';
export const CLONE_ITEM = BASE_URL + 'services/course/curriculum/clone-item';
export const UPDATE_ON_SORT_END = BASE_URL + 'services/course/curriculum/update-on-sort-end';
export const UPDATE_ON_COPY = BASE_URL + 'services/course/curriculum/update-on-copy';
export const UPDATE_DESCRIPTION = BASE_URL + 'services/course/curriculum/update-description';
export const UPDATE_STATUS = BASE_URL + 'services/course/curriculum/update-course-status';
export const UPDATE_PREVIEW = BASE_URL + 'services/course/curriculum/update-preview-status';
export const GET_RELATED_LECTURE = BASE_URL + 'services/course/curriculum/get-lectures';
export const GET_TEST_FOR_LECTURE = BASE_URL + 'services/course/curriculum/get-test-for-lecture';
export const GET_TEST_OF_LECTURE = BASE_URL + 'services/course/curriculum/get-test-of-lecture';
export const ADD_TEST_FOR_LECTURE = BASE_URL + 'services/course/curriculum/add-test-for-lecture';
export const DELETE_TEST_FOR_LECTURE = BASE_URL + 'services/course/curriculum/delete-test-for-lecture';

export const GET_RELATED_CURRICULUM_OF_COURSE = BASE_URL + 'services/course/curriculum/get-related';
export const SEARCH_RELATED_CURRICULUM_OF_COURSE = BASE_URL + 'services/course/curriculum/search-related';
export const ADD_RELATED_CURRICULUM_OF_COURSE = BASE_URL + 'services/course/curriculum/add-related';
export const DELETE_RELATED_CURRICULUM_OF_COURSE = BASE_URL + 'services/course/curriculum/delete-related';

export const MOVE_ITEM_TO_PARENT = BASE_URL + 'services/course/curriculum/move-item-to-parent';

export const UPLOAD_IMAGE = API_URL + 'services/course/curriculum/upload-image';

export const UPLOAD_VIDEO = API_URL + 'services/media/video/upload';
export const UPLOAD_VIDEO_SD = API_URL + 'services/media/video/upload-sd';
export const UPLOAD_YOUTUBE = API_URL + 'services/media/video/upload-youtube';
export const DOWNLOAD_VIDEO = API_URL + 'services/media/video/download';
export const UPLOAD_URL_LIVESTREAM = API_URL + 'services/media/video/live-stream';
export const DELETE_VIDEO = API_URL + 'services/media/video/delete-video';
export const CHANGE_FREE = API_URL + 'services/course/curriculum/change-free';

export const GET_LIST_VIDEO = BASE_URL + 'services/media/video/get-list-video'
export const UPDATE_CURRICULUM_VIDEO = BASE_URL + 'services/media/video/update-curriculum-video';
export const CONVERT_VIDEO_TO_SD = BASE_URL + 'services/media/video/convert-to-sd';

export const UPLOAD_RESOURCE = API_URL + 'services/media/resource/upload-resource';

export const DELETE_RESOURCE = BASE_URL + 'services/media/resource/delete-resource';
export const SET_NULL_RESOURCE = BASE_URL + 'services/media/resource/set-null-resource';
export const GET_LIST_RESOURCE = BASE_URL + 'services/media/resource/get-list-resource';
export const UPDATE_RESOURCE = BASE_URL + 'services/media/resource/update-resource'

export const ADD_QUESTION = BASE_URL + 'services/multiplechoices/question/add-question';
export const UPDATE_QUESTION_ON_SORT_END = BASE_URL + 'services/multiplechoices/question/update-on-sort-end';
export const DELETE_QUESTION = BASE_URL + 'services/multiplechoices/question/delete-question';
export const UPDATE_QUESTION = BASE_URL + 'services/multiplechoices/question/update-question';
export const MOVE_QUESTION = BASE_URL + 'services/multiplechoices/question/move-question';
export const ADD_QUESTION_BY_ID = BASE_URL + 'services/multiplechoices/question/add-question-by-id';

export const IMPORT_QUESTION = API_URL + 'services/multiplechoices/question/import-question';

export const TINY_MCE_WIRIS =  '/assets/tinymce/plugins/mathtype-tinymce4/plugin.min.js'
