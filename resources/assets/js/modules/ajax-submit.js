export function ajaxSubmit($form) {
    $form.on('submit', function(event) {
        event.preventDefault();
        $form.find('[type=submit]').prop('disabled', true);
        $form.find('.js-loading-icon').removeClass('d-none');
        $form.find('.has-error').removeClass('has-error');
        $form.find('.help-block').text('');

        var formData = new FormData($(this)[0]);

        axios({
            method: $form.attr('method'),
            url: $form.attr('action'),
            data: formData
        })
        .then(function(res) {
            const redirectTo = res.data.backUrl ? res.data.backUrl : $form.data('redirect');

            if (redirectTo) {
                window.location.href = redirectTo;
                return;
            }

            window.location.reload();
        })
        .catch(function(error) {
            if (error.response && error.response.status === 422) {
                const { errors } = error.response.data;
                let topErrorOffset = null;
                const isInModal = !!$form.parents('.modal').length;

                for (let [key, value] of Object.entries(errors)) {
                    // array validate, e.g. places.0, places.1...
                    const match = key.match(/^(\w+)\.\d+$/);

                    if (match && match.length) {
                        key = match[1] + '[]';
                    }

                    const $inputGroup = $form
                        .find(`[name="${key}"]`)
                        .parents('.form-group')
                        .first();

                    if (!isInModal) {
                        if (topErrorOffset === null) {
                            topErrorOffset = $inputGroup.offset().top;
                        } else {
                            topErrorOffset = Math.min(topErrorOffset, $inputGroup.offset().top);
                        }
                    }

                    $inputGroup.addClass('has-error');
                    $inputGroup.find('.help-block').text(value[0]);
                }

                if (topErrorOffset !== null) {
                    $('html, body').animate({ scrollTop: topErrorOffset }, 'slow');
                }
            } else if (error.response && error.response.data && error.response.data.message) {
                const $text = error.response.status === 419 ?
                    'Phiên làm việc của bạn đã hết hạn. Vui lòng tải lại trang!' :
                    error.response.data.message;

                alert($text);
            } else {
                window.location.reload();
            }
        })
        .finally(function() {
            $form.find('[type=submit]').prop('disabled', false);
            $form.find('.js-loading-icon').addClass('d-none');
        });
    });
}
