server {
    listen 80;

    client_max_body_size 5G;
    client_header_timeout 3000;
    client_body_timeout 3000;

    root /var/www/public;

    index index.php index.html;
    error_log  /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;

    ##
    # Gzip Settings
    ##

    gzip on;
    gzip_disable "msie6";

    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        # vj_khoahoc_php phải trùng với tên container_name trong docker-compose.yml
        fastcgi_pass vj_khoahoc_php:9000;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;

        fastcgi_read_timeout 3000;
        fastcgi_buffers 8 128k;
        fastcgi_buffer_size 128k;

    }
    location / {
        try_files $uri $uri/ /index.php?$query_string;
        gzip_static on;

        # kill cache
        add_header Last-Modified $date_gmt;
        add_header Cache-Control 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0';
        if_modified_since off;
        expires off;
        etag off;

        # Headers
        add_header Access-Control-Allow-Headers 'Origin,Range,Accept-Encoding,Referer,Cache-Control';
        add_header Access-Control-Expose-Headers 'Server,Content-Length,Content-Range,Date';
        add_header Access-Control-Allow-Methods 'GET, HEAD, OPTIONS';
        add_header Access-Control-Allow-Origin 'http://localhost:8000';
    }
}
