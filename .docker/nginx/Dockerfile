FROM node:18-alpine AS assets-frontend

WORKDIR /var/www

RUN apk add --no-cache git curl

COPY package*.json ./

RUN npm install --legacy-peer-deps --verbose && npm cache clean --force

FROM assets-frontend AS build-frontend

COPY . .

RUN npm run prod

FROM nginx:1.25-alpine AS app-nginx

COPY /.docker/nginx/vhost.conf /etc/nginx/conf.d/default.conf
COPY --from=build-frontend /var/www/public /var/www/public
