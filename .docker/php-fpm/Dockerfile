FROM php:7.2.5-fpm AS base

ENV USER=www
ENV GROUP=www
ENV UID=1000
ENV GID=1000

RUN sed -i 's/deb.debian.org/archive.debian.org/g' /etc/apt/sources.list && \
    sed -i 's|security.debian.org|archive.debian.org|g' /etc/apt/sources.list && \
    sed -i '/stretch-updates/d' /etc/apt/sources.list

# Install dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    mariadb-client \
    libmcrypt-dev \
    libpng-dev \
    libjpeg62-turbo-dev \
    libfreetype6-dev \
    libmagickwand-dev \
    libzip-dev \
    libonig-dev \
    libtidy-dev \
    libxslt-dev \
    locales \
    zip \
    jpegoptim optipng pngquant gifsicle \
    vim \
    unzip \
    curl \
    ffmpeg \
    pdftk \
    ghostscript \
    poppler-utils \
    unoconv \
    && pecl channel-update pecl.php.net \
    && pecl install imagick \
    && docker-php-ext-enable imagick

# Install last version pandoc
RUN PANDOC_VERSION=3.5 \
    && curl -L https://github.com/jgm/pandoc/releases/download/${PANDOC_VERSION}/pandoc-${PANDOC_VERSION}-linux-amd64.tar.gz -o pandoc.tar.gz \
    && tar xvzf pandoc.tar.gz --strip-components 1 -C /usr/local/ \
    && rm pandoc.tar.gz

# Install extensions php
RUN docker-php-ext-configure gd --with-freetype-dir=/usr/include/ --with-jpeg-dir=/usr/include/ --with-png-dir=/usr/include/ \
    && docker-php-ext-install mysqli pdo pdo_mysql mbstring zip exif pcntl tidy xsl gd

# Install composer
RUN curl -sS https://getcomposer.org/installer | php -- --version=1.10.20 --install-dir=/usr/local/bin --filename=composer

# Clear cache
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

COPY .docker/php-fpm/custom.ini /usr/local/etc/php/conf.d/
COPY .docker/php-fpm/policy.xml /etc/ImageMagick-6/policy.xml

FROM base AS vendor

WORKDIR /var/www

# Create User and Group for application
RUN groupadd -g ${GID} ${GROUP} && useradd -u ${UID} -ms /bin/bash -g ${GROUP} ${USER}

RUN chown -R ${USER}:${GROUP} /var/www

USER ${USER}

COPY --chown=${USER}:${GROUP} composer.json composer.lock ./

RUN composer install --prefer-dist --no-scripts --no-autoloader

FROM vendor AS app-fpm

COPY --from=vendor /var/www/vendor /var/www/vendor

COPY --chown=${USER}:${GROUP} . .

RUN chmod -R ug+w /var/www/storage

RUN composer dump-autoload -o
RUN php artisan view:cache
# RUN php artisan config:cache
# RUN php artisan route:cache

USER ${USER}

# Expose port 9000 and start php-fpm server
EXPOSE 9000
CMD ["php-fpm"]
