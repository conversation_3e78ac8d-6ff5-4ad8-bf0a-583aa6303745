FROM php:7.2.5-fpm

# Set working directory
WORKDIR /var/www

RUN sed -i 's/deb.debian.org/archive.debian.org/g' /etc/apt/sources.list && \
    sed -i 's|security.debian.org|archive.debian.org|g' /etc/apt/sources.list && \
    sed -i '/stretch-updates/d' /etc/apt/sources.list

# Install dependencies
RUN apt-get update && apt-get install -y --allow-unauthenticated --no-install-recommends \
    debian-keyring \
    debian-archive-keyring \
    build-essential \
    mariadb-client \
    libmcrypt-dev \
    libpng-dev \
    libjpeg62-turbo-dev \
    libfreetype6-dev \
    libmagickwand-dev \
    libzip-dev \
    libonig-dev \
    libtidy-dev \
    libxslt-dev \
    locales \
    zip \
    jpegoptim optipng pngquant gifsicle \
    vim \
    unzip \
    curl \
    ffmpeg \
    pdftk \
    ghostscript \
    poppler-utils \
    unoconv \
    && pecl install imagick

# RUN apt-get update && apt-get install -y pandoc
# Install last version pandoc
RUN PANDOC_VERSION=3.5 \
    && curl -L https://github.com/jgm/pandoc/releases/download/${PANDOC_VERSION}/pandoc-${PANDOC_VERSION}-linux-amd64.tar.gz -o pandoc.tar.gz \
    && tar xvzf pandoc.tar.gz --strip-components 1 -C /usr/local/ \
    && rm pandoc.tar.gz

# Install extensions
RUN docker-php-ext-install mysqli pdo pdo_mysql mbstring zip exif pcntl tidy xsl
RUN docker-php-ext-configure gd --with-gd --with-freetype-dir=/usr/include/ --with-jpeg-dir=/usr/include/ --with-png-dir=/usr/include/
RUN docker-php-ext-install gd
RUN docker-php-ext-enable imagick

# Install composer
RUN curl -sS https://getcomposer.org/installer | php -- --version=1.10.20 --install-dir=/usr/local/bin --filename=composer

# Install nodejs
RUN curl -sL https://deb.nodesource.com/setup_16.x | bash -
RUN apt-get update && apt-get install -y --allow-unauthenticated gnupg nodejs

# Clear cache
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# Add user for laravel application
RUN groupadd -g 1000 www
RUN useradd -u 1000 -ms /bin/bash -g www www

# Change current user to www
USER www

# Expose port 9000 and start php-fpm server
EXPOSE 9000
CMD ["php-fpm"]
