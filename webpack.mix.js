let mix = require('laravel-mix');
// const fs = require('fs');
// const path = require('path');

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */

// Custum id sau build thành int dành cho các server nếu có bản npm và node khác nhau chạy cùng 1 dự án
// const manifestPath = path.resolve(__dirname, 'public', 'mix-manifest.json');
// const oldManifestPath = path.resolve(__dirname, 'public', 'mix-manifest-old.json');
// const customManifestPath = path.resolve(__dirname, 'public', 'mix-manifest-custom.json');

// Hàm sao lưu mix-manifest.json trước khi build
// function backupManifest() {
//     if (fs.existsSync(customManifestPath)) {
//         fs.copyFileSync(manifestPath, customManifestPath);
//     } else {
//         if (fs.existsSync(manifestPath)) {
//             fs.copyFileSync(manifestPath, oldManifestPath);
//         } else {
//             fs.writeFileSync(manifestPath, '{}');
//             fs.copyFileSync(manifestPath, oldManifestPath);
//         }

//         fs.writeFileSync(customManifestPath, '{}');
//     }
// }

// Hàm cập nhật version nếu có thay đổi
// function updateVersion() {
//     const newManifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
//     const oldManifest = JSON.parse(fs.readFileSync(oldManifestPath, 'utf8'));
//     const customManifest = JSON.parse(fs.readFileSync(customManifestPath, 'utf8'));

//     const updatedManifest = {};

//     for (const key in newManifest) {
//         const match = customManifest[key] && customManifest[key].match(/id=(\d+)/);
//         const version = match && match[1] ? parseInt(match[1]) : 0;

//         if (!oldManifest[key] || newManifest[key] !== oldManifest[key]) {
//             updatedManifest[key] = `${key}?id=${version + 1}`;
//         } else {
//             updatedManifest[key] = `${key}?id=${version}`;
//         }
//     }

//     fs.writeFileSync(manifestPath, JSON.stringify(updatedManifest, null, 4));
//     fs.writeFileSync(oldManifestPath, JSON.stringify(newManifest, null, 4));
// }

// Sao lưu manifest trước khi build
// backupManifest();

/**
 * Web
 */
mix.js('resources/assets/js/frontend/vendor.js', 'public/js/frontend')
    .js('resources/assets/js/frontend/main.js', 'public/js/frontend')
    .js('resources/assets/js/frontend/modules/select2.js', 'public/js/frontend')
    .js('resources/assets/js/frontend/modules/editor-custom.js', 'public/js/frontend');

mix.sass('resources/assets/sass/frontend/vendor.scss', 'public/css/frontend')
    .sass('resources/assets/sass/frontend/style.scss', 'public/css/frontend')
    .sass('resources/assets/sass/frontend/modules/select2.scss', 'public/css/frontend')
    .sass('resources/assets/sass/frontend/modules/editor-custom.scss', 'public/css/frontend')
    .sass('resources/assets/sass/frontend/modules/exam-preview.scss', 'public/css/frontend')
    .sass('resources/assets/sass/frontend/modules/exam.scss', 'public/css/frontend')
    .sass('resources/assets/sass/frontend/modules/qa-page.scss', 'public/css/frontend')
    .sass('resources/assets/sass/frontend/modules/livestreams.scss', 'public/css/frontend')
    .sass('resources/assets/sass/frontend/modules/package-vip.scss', 'public/css/frontend');

/**
 * video
 */
mix.sass('resources/assets/sass/frontend/video.scss', 'public/css/frontend')
    .js('resources/assets/js/frontend/video-simple.js', 'public/js/frontend')
    .js('resources/assets/js/frontend/video.js', 'public/js/frontend')
    .js('resources/assets/js/frontend/video-free.js', 'public/js/frontend');

/**
 * App
 */
mix.js('resources/assets/js/app.js', 'public/js')
    .sass('resources/assets/sass/app.scss', 'public/css')
    .sass('resources/assets/sass/error_page.scss', 'public/css');

/**
 * Admin
 */
mix.js('resources/assets/js/backend/admin.js', 'public/js/backend')
    .js('resources/assets/js/backend/pages/lesson.js', 'public/js/backend')
    .js('resources/assets/js/backend/pages/livestreams_lesson.js', 'public/js/backend')
    .js('resources/assets/js/backend/pages/combo.js', 'public/js/backend')
    .js('resources/assets/js/backend/pages/upload_video.js', 'public/js/backend');

mix.copy('resources/assets/js/backend/bootstrap-datetimepicker.js', 'public/js/backend/bootstrap-datetimepicker.js');

/**
 * Editor
 */
mix.js('resources/assets/js/modules/editor.js', 'public/js/modules/editor.js')

/**
 * React
 */
mix.js('resources/assets/js/modules/react/curriculum.js', 'public/js/modules')
    .js('resources/assets/js/modules/react/category.js', 'public/js/modules')
    .js('resources/assets/js/modules/react/equestion.js', 'public/js/modules')
    .js('resources/assets/js/modules/react/quiz.js', 'public/js/modules')
    .js('resources/assets/js/modules/react/exam.js', 'public/js/modules')
    .js('resources/assets/js/modules/react/exam-result.js', 'public/js/modules')
    .react();

mix.version();

// Sau khi build, cập nhật version sang dạng int
// mix.then(() => {
//   updateVersion();
// });
