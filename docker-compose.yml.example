version: '3'

services:
    #PHP Service
    vj_khoahoc_php:
        build:
            context: .
            dockerfile: .docker/dev/Dockerfile
        container_name: vj_khoahoc_php
        working_dir: /var/www
        volumes:
            - ./:/var/www
            - ./.docker/php-fpm/custom.ini:/usr/local/etc/php/conf.d/local.ini
            - ./.docker/php-fpm/policy.xml:/etc/ImageMagick-6/policy.xml
        expose:
            - "9000"
        networks:
            - app-network

    #Nginx Service
    vj_khoahoc_nginx:
        image: nginx:alpine
        container_name: vj_khoahoc_nginx
        ports:
            - "9090:80"
            - "4433:443"
        volumes:
            - ./:/var/www
            - ./.docker/nginx/vhostdev.conf/:/etc/nginx/conf.d/default.conf
        networks:
            - app-network

    #MySQL Service
    vj_khoahoc_mysql:
        image: mysql:5.7.24
        container_name: vj_khoahoc_mysql
        ports:
            - "3308:3306"
        environment:
            MYSQL_DATABASE: vjkhoahoc
            MYSQL_ROOT_PASSWORD: root
        command: mysqld --sql_mode="STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION" --max-allowed-packet=15000000
        volumes:
            - dbdata:/var/lib/mysql/
            - ./.docker/mysql/my.cnf:/etc/mysql/my.cnf
        networks:
            - app-network

    #Redis Service
    vj_khoahoc_redis:
        image: redis:alpine
        container_name: vj_khoahoc_redis
        networks:
            - app-network

#Docker Networks
networks:
    app-network:
        driver: bridge

#Volumes
volumes:
    dbdata:
        driver: local
