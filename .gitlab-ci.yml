image: docker:19

services:
  - docker:dind

workflow:
  rules:
    - changes:
      - .docker/**/* # ** để đệ quy nếu nhiều folder lồng nhau
      - app/**/*
      - config/*
      - module/**/*
      - public/**/*
      - resources/**/*
      - routes/*
      - package.json
      - composer.json
      - webpack.mix.js
      when: always
    - when: never

stages:
  - build
  - release

before_script:
  - docker version
  # các biến được gitlab khai báo: https://docs.gitlab.com/ee/ci/variables/
  - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY # xem thêm về biến: https://docs.gitlab.com/ee/ci/variables/predefined_variables.html

build:
  stage: build
  only:
    - main
  script:
    - docker pull $CI_REGISTRY_IMAGE/nginx:latest || true
    - docker build -f .docker/nginx/Dockerfile --cache-from $CI_REGISTRY_IMAGE/nginx:latest --tag $CI_REGISTRY_IMAGE/nginx:$CI_COMMIT_SHA --tag $CI_REGISTRY_IMAGE/nginx:latest --target app-nginx .
    - docker push $CI_REGISTRY_IMAGE/nginx:$CI_COMMIT_SHA

    - docker pull $CI_REGISTRY_IMAGE/fpm:latest || true
    - docker build -f .docker/php-fpm/Dockerfile --cache-from $CI_REGISTRY_IMAGE/fpm:latest --tag $CI_REGISTRY_IMAGE/fpm:$CI_COMMIT_SHA --tag $CI_REGISTRY_IMAGE/fpm:latest --target app-fpm .
    - docker push $CI_REGISTRY_IMAGE/fpm:$CI_COMMIT_SHA

release-test:
  variables:
    GIT_STRATEGY: none
  stage: release
  only:
    - test
  script:
    - docker pull $CI_REGISTRY_IMAGE/nginx:$CI_COMMIT_SHA
    - docker tag $CI_REGISTRY_IMAGE/nginx:$CI_COMMIT_SHA $CI_REGISTRY_IMAGE/nginx:$CI_COMMIT_REF_NAME
    - docker push $CI_REGISTRY_IMAGE/nginx:$CI_COMMIT_REF_NAME

    - docker pull $CI_REGISTRY_IMAGE/fpm:$CI_COMMIT_SHA
    - docker tag $CI_REGISTRY_IMAGE/fpm:$CI_COMMIT_SHA $CI_REGISTRY_IMAGE/fpm:$CI_COMMIT_REF_NAME
    - docker push $CI_REGISTRY_IMAGE/fpm:$CI_COMMIT_REF_NAME

release-latest:
  variables:
    GIT_STRATEGY: none
  stage: release
  only:
    - main # push latest chỉ dành cho main để ko bị ghi đè bới các nhánh khác
  script:
    - docker pull $CI_REGISTRY_IMAGE/nginx:$CI_COMMIT_SHA
    - docker tag $CI_REGISTRY_IMAGE/nginx:$CI_COMMIT_SHA $CI_REGISTRY_IMAGE/nginx:latest
    - docker push $CI_REGISTRY_IMAGE/nginx:latest

    - docker pull $CI_REGISTRY_IMAGE/fpm:$CI_COMMIT_SHA
    - docker tag $CI_REGISTRY_IMAGE/fpm:$CI_COMMIT_SHA $CI_REGISTRY_IMAGE/fpm:latest
    - docker push $CI_REGISTRY_IMAGE/fpm:latest
