<?php

use Illuminate\Database\Seeder;
use Subject\Models\Subject;
use Level\Models\Level;
use ClassLevel\Models\ClassLevel;

class CoursePreTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $subjects_name = [
            "<PERSON><PERSON>",
            "<PERSON>ă<PERSON>",
            '<PERSON><PERSON><PERSON> lý',
            '<PERSON><PERSON><PERSON> học',
            'tiếng anh',
            'L<PERSON>ch sử',
            '<PERSON><PERSON><PERSON> lý',
            '<PERSON><PERSON> học',
        ];

        foreach ($subjects_name as $item) {
            Subject::firstOrCreate(
                ['slug' => stripUnicode($item)],
                [
                    'name' => $item,
                    'icon' => 'demo-icon icon-emo-happy',
                    'seo_title' => $item,
                    'author' => 1
                ]
            );
        }

        $levels_name = [
            "Ôn thi đại học điểm 5-6",
            "Ôn thi đại học điểm 6-8",
            'Ôn học sinh cấp 3',
            'Ôn thi đại học',
        ];

        foreach ($levels_name as $item) {
            Level::firstOrCreate(
                ['slug' => stripUnicode($item)],
                [
                    'name' => $item,
                    'seo_title' => $item,
                    'author' => 1,
                    'status' => 'active',
                    'featured' => 'active',
                ]
            );
        }

        for ($i = 1; $i <= 12; $i++) {
            $name = 'Lớp ' . $i;

            if ($i <= 5) {
                $group = 'primary';
            } elseif ($i <= 9) {
                $group = 'secondary';
            } else {
                $group = 'high';
            }

            ClassLevel::firstOrCreate(
                ['slug' => stripUnicode($name)],
                [
                    'name' => $name,
                    'group' => $group,
                    'seo_title' => $name,
                    'author' => 1,
                    'status' => 'active',
                ]
            );
        }
    }
}
