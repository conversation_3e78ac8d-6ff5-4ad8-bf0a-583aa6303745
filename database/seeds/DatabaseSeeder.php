<?php

use Illuminate\Database\Seeder;
use Acl\Models\Role;
use Setting\Models\Setting;
use Users\Models\Users;
use Acl\Models\Permission;

class DatabaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Setting
        // $settingData = config('course.setting');

        // foreach ($settingData as $setting) {
        //     Setting::firstOrCreate(
        //         ['name' => $setting['name']],
        //         [
        //             'content' => $setting['content'],
        //             'author' => $setting['author'],
        //             'status' => $setting['status'],
        //         ]
        //     );
        // }

        // Roles
        $rolesData = config('permissions.roles');

        foreach ($rolesData as $role) {
            Role::firstOrCreate(
                ['name' => $role['name']],
                [
                    'display_name' => $role['display_name'],
                    'description' => $role['description'],
                ]
            );
        }

        // Permissions
        $permissionsData = config('permissions.permissions');

        foreach ($permissionsData as $module => $permissions) {
            foreach ($permissions as $permission) {
                Permission::firstOrCreate(
                    ['name' => $permission['name']],
                    [
                        'display_name' => $permission['display_name'],
                        'description' => $permission['description'],
                        'module' => $module,
                    ]
                );
            }
        }

        // Users
        // $user = Users::firstOrCreate(
        //     ['email' => '<EMAIL>'],
        //     [
        //         'password' => Hash::make('123456'),
        //         'thumbnail' => 'adminux/img/user-header.png',
        //         'first_name' => 'Administrator',
        //         'status' => 'active',
        //         'sex' => 'male',
        //     ]
        // );

        // $role_admin = Role::where('name', 'administrator')->firstOrFail();

        // if ($role_admin) {
        //     $user->roles()->sync([$role_admin->id]);
        // }

        // $this->call(CoursePreTableSeeder::class);
    }
}
