{"private": true, "scripts": {"dev": "npm run development", "development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "production": "mix --production"}, "devDependencies": {"@babel/preset-react": "^7.24.7", "babel-plugin-transform-class-properties": "^6.24.1", "cross-env": "^5.2.1", "laravel-mix": "^6.0.49", "resolve-url-loader": "^5.0.0", "sass": "^1.26.2", "sass-loader": "^10.0.0"}, "dependencies": {"@fortawesome/fontawesome-free": "^5.11.2", "@silvermine/videojs-quality-selector": "^1.2.5", "@tinymce/tinymce-react": "^2.6.1", "@wiris/mathtype-tinymce4": "^7.17.0", "axios": "^1.7.5", "bootstrap3": "^3.3.5", "html-react-parser": "^5.2.6", "jquery": "^3.4.1", "jquery-validation": "^1.19.1", "lodash": "^4.17.11", "medium-zoom": "^1.0.5", "moment": "^2.24.0", "owl.carousel": "^2.3.4", "qrcode.react": "^3.2.0", "react": "^16.14.0", "react-bootstrap": "^0.32.4", "react-datetime": "^2.16.3", "react-dom": "^16.14.0", "react-sortable-hoc": "^0.6.8", "react-sortable-tree": "^2.3.0", "react-tinymce": "^0.7.0", "react-toggle-button": "^2.2.0", "slick-carousel": "^1.8.1", "tinymce": "^7.3.0", "uuid": "^11.1.0", "video.js": "^7.6.6", "videojs-youtube": "^3.0.1"}}