<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Crypt;
use Firebase\JWT\JWT;

Route::group(['prefix' => 'tlv'], function (Router $router) {
    $router->post('handle-document', 'UploadController@handleDocumentTlv');
    $router->get('pdf-to-html', 'UploadController@pdfToHtml');
    $router->get('pdf-2-htmlex', 'UploadController@pdf2HtmlEX');
});

Route::group(['prefix' => 'media'], function (Router $router) {
    $router->post('upload-image', 'UploadController@uploadImage');
    $router->get('upload-image', 'UploadController@uploadImage');
    $router->post('upload-video', 'UploadController@uploadVideo');
    $router->get('video/{token}/video.m3u8', function($token = null) {
        try {
            $decoded = JWT::decode($token, config('app.video_url_secret'), array('HS256'));
            // Giải mã payload - APP_KEY giống nhau mới giải mã được
            $payload = Crypt::decrypt($decoded->data);

            if (!empty($payload['raw_url'])) {
                $videoUrl = $payload['raw_url'] . (!empty($payload['clipTo']) ? ('/clipTo/' . $payload['clipTo']) : '') . '/index.m3u8';
                $client = new \GuzzleHttp\Client();
                $res = $client->request('GET', $videoUrl, ['http_errors' => false]);

                if ($res->getStatusCode() == 200) {
                    return response($res->getBody()->getContents(), 200)->header('Content-Type', 'application/vnd.apple.mpeg');
                }
            }

            throw new \Exception('Not get file: ' . $videoUrl);
       } catch (\Exception $e) {
           Log::error($e);

           return abort(500);
       }
    });
});
