<?php

namespace Base\Services\File;

use Exception;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FileService
{
    private function storage($is_media = false)
    {
        return Storage::disk($is_media ? config('document.media_storage_disk') : config('document.storage_disk'));
    }

    public function fileFolder($prefixFolder = '', $suffixFolder = '', $is_media = false)
    {
        $location = $is_media ? config('document.media_base_folder') : config('document.base_folder');

        if ($prefixFolder) {
            $location .= ($prefixFolder . '/');
        }

        if ($suffixFolder) {
            $location .= ($suffixFolder . '/');
        } elseif (auth()->id()) {
            $location .= (auth()->id() . '/');
        }

        if (!File::exists($location)) {
            File::makeDirectory($location, 0775, true, true);
        }

        return $location;
    }

    protected function deleteOldFiles($oldFiles)
    {
        try {
            $this->storage()->delete($oldFiles);
        } catch (Exception $e) {
            report($e);
        }
    }

    private function constructFileName($fileName, $extension)
    {
        $fileName = Str::slug($fileName, '-') . '-' . time() . uniqid();

        return strtolower("{$fileName}.{$extension}");
    }

    public function upload($prefixFolder, $file, $textFileName = null, $oldFilesPath = null, $suffixFolder = '')
    {
        try {
            if ($oldFilesPath) {
                $this->deleteOldFiles($oldFilesPath);
            }

            $folder = $this->fileFolder($prefixFolder, $suffixFolder);
            $fileName = $this->constructFileName($textFileName ?: pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME), $file->getClientOriginalExtension());
            $isUploaded = $this->storage()->putFileAs($folder, $file, $fileName);

            if (!$isUploaded) {
                return null;
            }

            return $folder . $fileName;
        } catch (Exception $e) {
            report($e);
            return null;
        }
    }

    public function uploadVideo($folder, $file, $textFileName = null, $oldFilesPath = null)
    {
        try {
            $fileName = $this->constructFileName($textFileName ?: pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME), $file->getClientOriginalExtension());
            $isUploaded = $this->storage(true)->putFileAs($folder, $file, $fileName);

            if (!$isUploaded) {
                return null;
            }

            if ($oldFilesPath) {
                $this->deleteOldFiles($oldFilesPath);
            }

            return $folder . $fileName;
        } catch (Exception $e) {
            report($e);
            return null;
        }
    }

    public function saveHtmlFile($folder, string $content, $fileName)
    {
        try {
            $fullPath = $folder . $fileName;

            $isSaved = $this->storage()->put($fullPath, $content);

            if (!$isSaved) {
                return null;
            }

            return $fullPath;
        } catch (Exception $e) {
            report($e);
            return null;
        }
    }

    public function uploadedFileUrl($uploadedPath)
    {
        // return $this->storage()->url($uploadedPath);
        return asset($uploadedPath);
    }

    public function relativeFileUrl($uploadedPath)
    {
        return config('filesystems.disks.' . config('document.storage_disk') . '.root') . '/' . $uploadedPath;
    }

    public function move($fromPath, $toPath)
    {
        $this->storage()->move($fromPath, $toPath);

        return $this->storage()->path($toPath);
    }

    public function delete($uploadedPaths)
    {
        $this->deleteOldFiles($uploadedPaths);
    }

    public function download($filePath, $fileName, $headers = ['Content-Type' => 'application/octet-stream'])
    {
        $fileExtension = File::extension($filePath);

        if (!File::exists($filePath)) {
            $contents = 'Tệp đang trong quá trình biên soạn, vui lòng thử lại sau ít phút!';

            return response()->stream(function () use ($contents) {
                echo $contents;
            }, 200, [
               'Cache-Control'         => 'must-revalidate, post-check=0, pre-check=0',
               'Content-Type'          => 'application/octet-stream',
               'Content-Disposition'   => 'attachment; filename="Download.txt"',
           ]);
        }

        return $this->storage()->download($filePath, "$fileName.$fileExtension", $headers);
    }
}
