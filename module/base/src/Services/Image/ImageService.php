<?php

namespace Base\Services\Image;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\Storage;
use Spatie\ImageOptimizer\Optimizers\Jpegoptim;
use Spatie\ImageOptimizer\Optimizers\Pngquant;
use Spatie\ImageOptimizer\OptimizerChain;
use Illuminate\Support\Str;
use Exception;

class ImageService
{
    private function storage()
    {
        return Storage::disk(config('image.storage_disk'));
    }

    protected function resizedImagePath($originalFilePath, $width, $height)
    {
        $lastPositionOfDot = strrpos($originalFilePath, '.');

        if ($lastPositionOfDot === false) {
            return sprintf('%s_%sx%s', $originalFilePath, $width, $height);
        }

        $extension = substr($originalFilePath, $lastPositionOfDot);
        $startName = substr($originalFilePath, 0, $lastPositionOfDot);

        return sprintf('%s_%sx%s%s', $startName, $width, $height, $extension);
    }

    public function imageFolder($prefixFolder = '', $suffixFolder = '')
    {
        $location = config('image.base_folder');

        if ($prefixFolder) {
            $location .= ($prefixFolder . '/');
        }

        if ($suffixFolder) {
            $location .= ($suffixFolder . '/');
        } elseif (auth()->id()) {
            $location .= (auth()->id() . '/');
        }

        if (!File::exists($location)) {
            File::makeDirectory($location, 0775, true, true);
        }

        return $location;
    }

    protected function deleteOldFiles($oldFiles)
    {
        try {
            $this->storage()->delete($oldFiles);
        } catch (Exception $e) {
            report($e);
        }
    }

    protected function deleteOldResizedFiles($oldFiles, $resizes)
    {
        try {
            foreach ($resizes as $size) {
                $oldResizedFiles = $this->resizedImagePath($oldFiles, $size[0], $size[1]);

                $this->storage()->delete($oldResizedFiles);
            }
        } catch (Exception $e) {
            report($e);
        }
    }

    protected function resizes($uploadedFile, $storedFilePath, $resizes)
    {
        try {
            $image = Image::make($uploadedFile);

            foreach ($resizes as $size) {
                $imageData = (clone $image)
                    ->fit($size[0], $size[1])
                    ->encode()
                    ->getEncoded();
                $imageResize = Image::make($imageData)->orientate();
                $imageName = $this->resizedImagePath($storedFilePath, $size[0], $size[1]);

                $imageResize->save($imageName);
            }
        } catch (Exception $e) {
            report($e);
        }
    }

    private function constructFileName($fileName, $extension)
    {
        $fileName = Str::slug($fileName, '-') . '-' . time();

        return strtolower("{$fileName}.{$extension}");
    }

    public function upload($prefixFolder, $file, $resizes = [], $oldFile = null, $suffixFolder = '')
    {
        try {
            $folder = $this->imageFolder($prefixFolder, $suffixFolder);
            $fileName = $this->constructFileName(pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME), $file->getClientOriginalExtension());
            $filePath = $folder . $fileName;

            // Optimize image before store to storage
            $this->optimizeImage($filePath);

            $image = Image::make($file->path())->orientate();
            $storedFilePath = $this->storage()->path($filePath);

            $image->save($storedFilePath);

            if ($resizes) {
                $this->resizes($file, $storedFilePath, $resizes);
            }

            if ($oldFile) {
                $this->delete($oldFile, $resizes);
            }

            return $filePath;
        } catch (Exception $e) {
            report($e);
            return null;
        }
    }

    public function uploadWebp($prefixFolder, $file, $oldFile = null, $suffixFolder = '')
    {
        try {
            $folder = $this->imageFolder($prefixFolder, $suffixFolder);
            $fileName = $this->constructFileName(pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME),$file->getClientOriginalExtension());
            $filePath = $folder . $fileName;
            $storedFilePath = $this->storage()->path($filePath);

            $file->move(dirname($storedFilePath), $fileName);

            if ($oldFile) {
                $this->delete($oldFile);
            }

            return $filePath;
        } catch (Exception $e) {
            report($e);
            return null;
        }
    }

    public function uploadedImageUrl($uploadedPath)
    {
        return $this->storage()->url($uploadedPath);
    }

    public function resizedImageUrl($uploadedPath, $size, $useAsset = true)
    {
        $imagePath = $this->resizedImagePath($uploadedPath, $size[0], $size[1]);

        return $useAsset ? asset($imagePath) : $this->storage()->url($imagePath);
    }

    public function relativeFileUrl($uploadedPath)
    {
        return config('filesystems.disks.' . config('image.storage_disk') . '.root') . '/' . $uploadedPath;
    }

    public function move($fromPath, $toPath)
    {
        $this->storage()->move($fromPath, $toPath);

        return $this->storage()->path($toPath);
    }

    public function delete($uploadedPaths, $resizes = [])
    {
        if (!empty($resizes)) {
            $this->deleteOldResizedFiles($uploadedPaths, $resizes);
        }

        $this->deleteOldFiles($uploadedPaths);
    }

    public function uploadBase64($prefixFolder, $base64, $resizes = [], $oldFiles = [])
    {
        try {
            $extension = explode('/', explode(':', substr($base64, 0, strpos($base64, ';')))[1])[1];

            $image = substr($base64, strpos($base64, ',') + 1);
            $image = str_replace(' ', '+', $image);


            $imageName = Str::random(10).'_' .time().'.'.$extension;
            $folder = $this->imageFolder($prefixFolder);

            $storedFilePath = $folder . $imageName;

            $isUploaded = $this->storage()->put($storedFilePath, base64_decode($image));

            if (!$isUploaded) {
                return null;
            }

            $this->optimizeImage($storedFilePath);

            return $storedFilePath;
        } catch (Exception $e) {
            $this->logger()->error($e);
            return null;
        }
    }

    protected function optimizeImage($imagePath)
    {
        try {
            $optimizerChain = (new OptimizerChain)
                ->addOptimizer(new Jpegoptim([
                    '--strip-all',
                    '--all-progressive',
                ]))
                ->addOptimizer(new Pngquant([
                    '--force',
                ]));

            $optimizerChain->optimize($this->storage()->path($imagePath));

            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    private function logger()
    {
        return Log::channel('images_service');
    }
}
