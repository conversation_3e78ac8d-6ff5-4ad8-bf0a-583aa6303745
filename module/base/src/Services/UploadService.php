<?php

namespace Base\Services;

use Base\Services\File\FileService;
use Base\Services\Image\ImageService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Exception;

class UploadService
{
    protected $imageService;
    protected $fileService;

    public function __construct(ImageService $imageService, FileService $fileService)
    {
        $this->imageService = $imageService;
        $this->fileService = $fileService;
    }

    /**
     * Handle image upload from various input types
     * @throws Exception
     */
    public function uploadFile(Request $request, $prefixFolder = 'vj-image')
    {
        $inputType = $this->detectInputType($request);

        $uploadedPath = null;

        switch ($inputType) {
            case 'base64':
                $uploadedPath = $this->handleBase64Upload($request, $prefixFolder);
                break;

            case 'file':
                $uploadedPath = $this->handleFileUpload($request, $prefixFolder);
                break;

            case 'url':
                $uploadedPath = $this->handleUrlUpload($request, $prefixFolder);
                break;

            default:
                throw new Exception('Invalid input type. Please provide base64 data, file upload, or image URL.');
        }

        if (!$uploadedPath) {
            throw new Exception('Failed to upload image. Please try again.');
        }

        return $uploadedPath;
    }

    /**
     * Detect the type of input provided
     */
    public function detectInputType(Request $request)
    {
        // Check for file upload
        if ($request->hasFile('image') || $request->hasFile('file')) {
            return 'file';
        }

        // Check for URL
        if ($request->has('image_url') || $request->has('url')) {
            return 'url';
        }

        // Check for base64 data
        if ($request->has('image_data') || $request->has('base64')) {
            $data = $request->image_data ?: $request->base64;
            if ($this->isValidBase64Image($data)) {
                return 'base64';
            }
        }

        return 'unknown';
    }

    /**
     * Get validation rules based on input type
     */
    public function getValidationRules(Request $request, $inputType)
    {
        $rules = [];
        $messages = [];

        switch ($inputType) {
            case 'file':
                $fileField = $request->hasFile('image') ? 'image' : 'file';
                $rules[$fileField] = 'required|image|mimes:jpeg,jpg,png,gif,webp|max:10240'; // 10MB max
                break;

            case 'url':
                $urlField = $request->has('image_url') ? 'image_url' : 'url';
                $rules[$urlField] = 'required|url|regex:/\.(jpeg|jpg|png|gif|webp)(\?.*)?$/i';
                $messages[$urlField . '.regex'] = 'The URL must point to a valid image file (jpeg, jpg, png, gif, webp).';
                break;

            case 'base64':
                $dataField = $request->has('image_data') ? 'image_data' : 'base64';
                $rules[$dataField] = 'required|string';
                break;
        }

        return [$rules, $messages];
    }

    /**
     * Validate base64 image data
     */
    public function isValidBase64Image($data)
    {
        if (empty($data)) {
            return false;
        }

        // Check if it starts with data:image
        if (!preg_match('/^data:image\/(jpeg|jpg|png|gif|webp);base64,/', $data)) {
            return false;
        }

        // Extract base64 part
        $base64 = substr($data, strpos($data, ',') + 1);

        // Validate base64 encoding
        return base64_encode(base64_decode($base64, true)) === $base64;
    }

    /**
     * Handle base64 image upload
     * @throws Exception
     */
    private function handleBase64Upload(Request $request, $prefixFolder)
    {
        $imageData = $request->image_data ?: $request->base64;

        if (!$this->isValidBase64Image($imageData)) {
            throw new Exception('Invalid base64 image data');
        }

        return $this->imageService->uploadBase64($prefixFolder, $imageData);
    }

    /**
     * Handle file upload
     * @throws Exception
     */
    private function handleFileUpload(Request $request, $prefixFolder)
    {
        $file = $request->file('image') ?: $request->file('file');

        if (!$file || !$file->isValid()) {
            throw new Exception('Invalid file upload');
        }

        return $this->imageService->upload($prefixFolder, $file);
    }

    /**
     * Handle URL image download and upload
     */
    private function handleUrlUpload(Request $request, $prefixFolder)
    {
        $imageUrl = $request->image_url ?: $request->url;

        try {
            // Download image from URL with timeout and size limits
            $response = Http::timeout(30)
                ->withHeaders(['User-Agent' => 'Mozilla/5.0 (compatible; ImageUploader/1.0)'])
                ->get($imageUrl);

            if (!$response->successful()) {
                throw new Exception('Failed to download image from URL');
            }

            // Check content type
            $contentType = $response->header('Content-Type');
            if (!in_array($contentType, ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'])) {
                throw new Exception('URL does not point to a valid image');
            }

            // Check file size (10MB limit)
            $contentLength = $response->header('Content-Length');
            if ($contentLength && $contentLength > 10485760) {
                throw new Exception('Image file is too large (max 10MB)');
            }

            $imageData = $response->body();
            if (empty($imageData)) {
                throw new Exception('Downloaded image is empty');
            }

            // Create temporary file
            $tempFile = tempnam(sys_get_temp_dir(), 'img_upload_');
            file_put_contents($tempFile, $imageData);

            // Get file extension from content type
            $extension = $this->getExtensionFromContentType($contentType);

            // Create uploaded file instance
            $uploadedFile = new \Illuminate\Http\UploadedFile(
                $tempFile,
                'downloaded_image.' . $extension,
                $contentType,
                null,
                true
            );

            $uploadedPath = $this->imageService->upload($prefixFolder, $uploadedFile);

            // Clean up temp file
            unlink($tempFile);

            return $uploadedPath;

        } catch (Exception $e) {
            Log::error('URL image upload error: ' . $e->getMessage());
            throw new Exception('Failed to download and process image from URL: ' . $e->getMessage());
        }
    }

    /**
     * Get file extension from content type
     */
    private function getExtensionFromContentType($contentType)
    {
        $extensions = [
            'image/jpeg' => 'jpg',
            'image/jpg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp',
        ];

        return $extensions[$contentType] ?? 'jpg';
    }
}
