<?php

namespace Base\Http\Controllers;

use App\Http\Controllers\Controller;
use Base\Services\File\FileService;
use Base\Services\Image\ImageService;
use Base\Services\DocumentService;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class UploadController extends Controller
{
    protected $imageService;
    protected $fileService;
    protected $documentService;

    public function __construct(
        ImageService $imageService,
        FileService $fileService,
        DocumentService $documentService
    )
    {
        $this->imageService = $imageService;
        $this->fileService = $fileService;
        $this->documentService = $documentService;
    }

    public function handleDocumentTlv(Request $request)
    {
        $request->validate([
            'document' => 'nullable|mimes:pdf,doc,docx,xls,xlsx,ppt,pptx,zip,rar|max:12499968', // 12GB
            'document_preview' => 'nullable|mimes:pdf,doc,docx,xls,xlsx,ppt,pptx,zip,rar|max:12499968', // 12GB
            'banner' => 'nullable|mimes:jpg,png,jpeg|max:20000',
        ]);

        $data = [];
        $from = $request->page_from ?: 1;
        $to = $request->page_to ?: 5;
        $pfrom = $request->page_pfrom ?: 1;
        $pto = $request->page_pto ?: 5;

        if ($request->document) { // upload new origin doc
            $hasSplit = $request->split_document;
            $data['document'] = $this->documentService->upload($request->document_md5, $request->document, $hasSplit, $from, $to);
        } elseif ($request->split_document_path) { // split origin doc
            $data['document_id'] = $request->document_id;
            $data['document'] = $this->documentService->splitDocument($request->document_md5, $request->split_document_path, $from, $to);
        }

        if ($request->document_preview) { // upload new preview doc
            $data['document_preview'] = $this->documentService->upload($request->document_preview_md5, $request->document_preview, true, $pfrom, $pto);
        }

        if ($request->banner) { // upload banner
            $bannerPath = $this->imageService->upload(
                'documents',
                $request->banner,
                [],
                null,
                'banner'
            );
            $data['banner'] =  $this->fileService->uploadedFileUrl($bannerPath);
        }

        return response()->json($data);
    }

    public function pdfToHtml(Request $request)
    {
        $outHtml = '';

        if ($request->document_path) {
            $outHtml = $this->documentService->pdfToHtml($request->document_path);
        }

        return response()->json(['outHtml' => $outHtml]);
    }

    public function pdf2HtmlEX(Request $request)
    {
        $content = $asset_content = '';

        if ($request->pdf_path && $request->md5) {
            [$content, $asset_content] = $this->documentService->getContentPDF($request->md5, $request->pdf_path);
        }

        return response()->json(['content' => $content, 'asset_content' => $asset_content]);
    }

    public function uploadVideo(Request $request)
    {
        try {
            $request->validate([
                'video' => 'required|mimes:mp4,mov,mp3|max:4166656', // 12GB
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => [
                    'message' => $e->errors(),
                ],
            ]);
        }

        $folder = $this->fileService->fileFolder($request->folder ?: 'videos', null, true);
        $videoPath = $this->fileService->uploadVideo(
            $folder,
            $request->video
        );
        $videoUrl = $videoPath ? $this->fileService->uploadedFileUrl($videoPath) : '';

        return response()->json([
            'vide_url' => $videoUrl,
        ]);
    }

    public function uploadImage(Request $request)
    {
        $prefixFolder = $request->folder ?: 'vj-image';
        $imageData = $request->image_data;

        $uploadedPath = $this->imageService->uploadBase64($prefixFolder, $imageData);

        return response()->json([
            'path' => $uploadedPath,
            'img_url' => $uploadedPath ? asset($uploadedPath) : null,
        ]);
    }
}
