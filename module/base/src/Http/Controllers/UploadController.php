<?php

namespace Base\Http\Controllers;

use App\Http\Controllers\Controller;
use Base\Services\File\FileService;
use Base\Services\Image\ImageService;
use Base\Services\DocumentService;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Exception;

class UploadController extends Controller
{
    protected $imageService;
    protected $fileService;
    protected $documentService;

    public function __construct(
        ImageService $imageService,
        FileService $fileService,
        DocumentService $documentService
    )
    {
        $this->imageService = $imageService;
        $this->fileService = $fileService;
        $this->documentService = $documentService;
    }

    public function handleDocumentTlv(Request $request)
    {
        $request->validate([
            'document' => 'nullable|mimes:pdf,doc,docx,xls,xlsx,ppt,pptx,zip,rar|max:12499968', // 12GB
            'document_preview' => 'nullable|mimes:pdf,doc,docx,xls,xlsx,ppt,pptx,zip,rar|max:12499968', // 12GB
            'banner' => 'nullable|mimes:jpg,png,jpeg|max:20000',
        ]);

        $data = [];
        $from = $request->page_from ?: 1;
        $to = $request->page_to ?: 5;
        $pfrom = $request->page_pfrom ?: 1;
        $pto = $request->page_pto ?: 5;

        if ($request->document) { // upload new origin doc
            $hasSplit = $request->split_document;
            $data['document'] = $this->documentService->upload($request->document_md5, $request->document, $hasSplit, $from, $to);
        } elseif ($request->split_document_path) { // split origin doc
            $data['document_id'] = $request->document_id;
            $data['document'] = $this->documentService->splitDocument($request->document_md5, $request->split_document_path, $from, $to);
        }

        if ($request->document_preview) { // upload new preview doc
            $data['document_preview'] = $this->documentService->upload($request->document_preview_md5, $request->document_preview, true, $pfrom, $pto);
        }

        if ($request->banner) { // upload banner
            $bannerPath = $this->imageService->upload(
                'documents',
                $request->banner,
                [],
                null,
                'banner'
            );
            $data['banner'] =  $this->fileService->uploadedFileUrl($bannerPath);
        }

        return response()->json($data);
    }

    public function pdfToHtml(Request $request)
    {
        $outHtml = '';

        if ($request->document_path) {
            $outHtml = $this->documentService->pdfToHtml($request->document_path);
        }

        return response()->json(['outHtml' => $outHtml]);
    }

    public function pdf2HtmlEX(Request $request)
    {
        $content = $asset_content = '';

        if ($request->pdf_path && $request->md5) {
            [$content, $asset_content] = $this->documentService->getContentPDF($request->md5, $request->pdf_path);
        }

        return response()->json(['content' => $content, 'asset_content' => $asset_content]);
    }

    public function uploadVideo(Request $request)
    {
        try {
            $request->validate([
                'video' => 'required|mimes:mp4,mov,mp3|max:4166656', // 12GB
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => [
                    'message' => $e->errors(),
                ],
            ]);
        }

        $folder = $this->fileService->fileFolder($request->folder ?: 'videos', null, true);
        $videoPath = $this->fileService->uploadVideo(
            $folder,
            $request->video
        );
        $videoUrl = $videoPath ? $this->fileService->uploadedFileUrl($videoPath) : '';

        return response()->json([
            'vide_url' => $videoUrl,
        ]);
    }

    public function uploadImage(Request $request)
    {
        try {
            $prefixFolder = $request->folder ?: 'vj-image';
            $uploadedPath = null;
            $inputType = $this->detectInputType($request);

            // Validate input based on detected type
            $this->validateImageInput($request, $inputType);

            switch ($inputType) {
                case 'base64':
                    $uploadedPath = $this->handleBase64Upload($request, $prefixFolder);
                    break;

                case 'file':
                    $uploadedPath = $this->handleFileUpload($request, $prefixFolder);
                    break;

                case 'url':
                    $uploadedPath = $this->handleUrlUpload($request, $prefixFolder);
                    break;

                default:
                    return $this->errorResponse('Invalid input type. Please provide base64 data, file upload, or image URL.');
            }

            if (!$uploadedPath) {
                return $this->errorResponse('Failed to upload image. Please try again.');
            }

            return $this->successResponse($uploadedPath);
        } catch (ValidationException $e) {
            return $this->errorResponse('Validation failed.', $e->errors());
        } catch (Exception $e) {
            Log::error('Image upload error: ' . $e->getMessage());
            return $this->errorResponse('An error occurred while uploading the image.');
        }
    }

    /**
     * Detect the type of input provided
     */
    private function detectInputType(Request $request)
    {
        // Check for file upload
        if ($request->hasFile('image') || $request->hasFile('file')) {
            return 'file';
        }

        // Check for URL
        if ($request->has('image_url') || $request->has('url')) {
            return 'url';
        }

        // Check for base64 data
        if ($request->has('image_data') || $request->has('base64')) {
            $data = $request->image_data ?: $request->base64;
            if ($this->isValidBase64Image($data)) {
                return 'base64';
            }
        }

        return 'unknown';
    }

    /**
     * Validate base64 image data
     */
    private function isValidBase64Image($data)
    {
        if (empty($data)) {
            return false;
        }

        // Check if it starts with data:image
        if (!preg_match('/^data:image\/(jpeg|jpg|png|gif|webp);base64,/', $data)) {
            return false;
        }

        // Extract base64 part
        $base64 = substr($data, strpos($data, ',') + 1);

        // Validate base64 encoding
        return base64_encode(base64_decode($base64, true)) === $base64;
    }

    /**
     * Validate input based on type
     */
    private function validateImageInput(Request $request, $inputType)
    {
        $rules = [];
        $messages = [];

        switch ($inputType) {
            case 'file':
                $fileField = $request->hasFile('image') ? 'image' : 'file';
                $rules[$fileField] = 'required|image|mimes:jpeg,jpg,png,gif,webp|max:10240'; // 10MB max
                break;

            case 'url':
                $urlField = $request->has('image_url') ? 'image_url' : 'url';
                $rules[$urlField] = 'required|url|regex:/\.(jpeg|jpg|png|gif|webp)(\?.*)?$/i';
                $messages[$urlField . '.regex'] = 'The URL must point to a valid image file (jpeg, jpg, png, gif, webp).';
                break;

            case 'base64':
                $dataField = $request->has('image_data') ? 'image_data' : 'base64';
                $rules[$dataField] = 'required|string';
                break;
        }

        if (!empty($rules)) {
            $request->validate($rules, $messages);
        }
    }

    /**
     * Handle base64 image upload
     */
    private function handleBase64Upload(Request $request, $prefixFolder)
    {
        $imageData = $request->image_data ?: $request->base64;

        if (!$this->isValidBase64Image($imageData)) {
            throw new Exception('Invalid base64 image data');
        }

        return $this->imageService->uploadBase64($prefixFolder, $imageData);
    }

    /**
     * Handle file upload
     */
    private function handleFileUpload(Request $request, $prefixFolder)
    {
        $file = $request->file('image') ?: $request->file('file');

        if (!$file || !$file->isValid()) {
            throw new Exception('Invalid file upload');
        }

        return $this->imageService->upload($prefixFolder, $file);
    }

    /**
     * Handle URL image download and upload
     */
    private function handleUrlUpload(Request $request, $prefixFolder)
    {
        $imageUrl = $request->image_url ?: $request->url;

        try {
            // Download image from URL with timeout and size limits
            $response = Http::timeout(30)
                ->withHeaders(['User-Agent' => 'Mozilla/5.0 (compatible; ImageUploader/1.0)'])
                ->get($imageUrl);

            if (!$response->successful()) {
                throw new Exception('Failed to download image from URL');
            }

            // Check content type
            $contentType = $response->header('Content-Type');
            if (!in_array($contentType, ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'])) {
                throw new Exception('URL does not point to a valid image');
            }

            // Check file size (10MB limit)
            $contentLength = $response->header('Content-Length');
            if ($contentLength && $contentLength > 10485760) {
                throw new Exception('Image file is too large (max 10MB)');
            }

            $imageData = $response->body();
            if (empty($imageData)) {
                throw new Exception('Downloaded image is empty');
            }

            // Create temporary file
            $tempFile = tempnam(sys_get_temp_dir(), 'img_upload_');
            file_put_contents($tempFile, $imageData);

            // Get file extension from content type
            $extension = $this->getExtensionFromContentType($contentType);

            // Create uploaded file instance
            $uploadedFile = new \Illuminate\Http\UploadedFile(
                $tempFile,
                'downloaded_image.' . $extension,
                $contentType,
                null,
                true
            );

            $uploadedPath = $this->imageService->upload($prefixFolder, $uploadedFile);

            // Clean up temp file
            unlink($tempFile);

            return $uploadedPath;

        } catch (Exception $e) {
            Log::error('URL image upload error: ' . $e->getMessage());
            throw new Exception('Failed to download and process image from URL: ' . $e->getMessage());
        }
    }

    /**
     * Get file extension from content type
     */
    private function getExtensionFromContentType($contentType)
    {
        $extensions = [
            'image/jpeg' => 'jpg',
            'image/jpg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp',
        ];

        return $extensions[$contentType] ?? 'jpg';
    }

    /**
     * Return success response
     */
    private function successResponse($uploadedPath)
    {
        return response()->json([
            'success' => true,
            'path' => $uploadedPath,
            'img_url' => $uploadedPath ? asset($uploadedPath) : null,
            'message' => 'Image uploaded successfully'
        ]);
    }

    /**
     * Return error response
     */
    private function errorResponse($message, $errors = null)
    {
        $response = [
            'success' => false,
            'message' => $message
        ];

        if ($errors) {
            $response['errors'] = $errors;
        }

        return response()->json($response, 400);
    }
}
