{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "c5ed61a6f3665d946cbee4df13a8f71d", "packages": [{"name": "alchemy/binary-driver", "version": "v5.2.0", "source": {"type": "git", "url": "https://github.com/alchemy-fr/BinaryDriver.git", "reference": "e0615cdff315e6b4b05ada67906df6262a020d22"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alchemy-fr/BinaryDriver/zipball/e0615cdff315e6b4b05ada67906df6262a020d22", "reference": "e0615cdff315e6b4b05ada67906df6262a020d22", "shasum": ""}, "require": {"evenement/evenement": "^3.0|^2.0|^1.0", "php": ">=5.5", "psr/log": "^1.0", "symfony/process": "^2.3|^3.0|^4.0|^5.0"}, "require-dev": {"phpunit/phpunit": "^4.0|^5.0"}, "type": "library", "autoload": {"psr-0": {"Alchemy": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.lickmychip.com/"}, {"name": "Phraseanet Team", "email": "<EMAIL>", "homepage": "http://www.phraseanet.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://jens-hausdorf.de", "role": "Maintainer"}], "description": "A set of tools to build binary drivers", "keywords": ["binary", "driver"], "time": "2020-02-12T19:35:11+00:00"}, {"name": "arcanedev/log-viewer", "version": "4.6.4", "source": {"type": "git", "url": "https://github.com/ARCANEDEV/LogViewer.git", "reference": "747f2969152f6bb8740eaedc314c0601394c6619"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ARCANEDEV/LogViewer/zipball/747f2969152f6bb8740eaedc314c0601394c6619", "reference": "747f2969152f6bb8740eaedc314c0601394c6619", "shasum": ""}, "require": {"arcanedev/support": "~4.4.0", "ext-json": "*", "php": ">=7.1.3", "psr/log": "~1.0"}, "require-dev": {"mockery/mockery": "~1.0", "orchestra/testbench": "~3.7.0", "phpunit/phpcov": "~5.0", "phpunit/phpunit": "~7.0"}, "type": "library", "extra": {"laravel": {"providers": ["Arcanedev\\LogViewer\\LogViewerServiceProvider"]}}, "autoload": {"files": ["constants.php", "helpers.php"], "psr-4": {"Arcanedev\\LogViewer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "ARCANEDEV", "email": "<EMAIL>", "homepage": "https://github.com/arcanedev-maroc", "role": "Developer"}], "description": "Provides a Log Viewer for Laravel 5", "homepage": "https://github.com/ARCANEDEV/LogViewer", "keywords": ["arcanedev", "arcanesoft", "laravel", "log", "log viewer", "log-viewer", "logviewer"], "time": "2019-09-12T19:11:28+00:00"}, {"name": "arcanedev/support", "version": "4.4.0", "source": {"type": "git", "url": "https://github.com/ARCANEDEV/Support.git", "reference": "9f4d9947563c6bec10ea7fa7954129e012216af9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ARCANEDEV/Support/zipball/9f4d9947563c6bec10ea7fa7954129e012216af9", "reference": "9f4d9947563c6bec10ea7fa7954129e012216af9", "shasum": ""}, "require": {"illuminate/filesystem": "~5.7.0", "illuminate/support": "~5.7.0", "php": ">=7.1.3"}, "require-dev": {"orchestra/testbench": "~3.7.0", "phpunit/phpcov": "~5.0", "phpunit/phpunit": "~7.0"}, "type": "library", "autoload": {"files": ["constants.php", "helpers.php"], "psr-4": {"Arcanedev\\Support\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "ARCANEDEV", "email": "<EMAIL>", "homepage": "https://github.com/arcanedev-maroc"}], "description": "ARCANEDEV Support Helpers", "homepage": "https://github.com/ARCANEDEV/Support", "keywords": ["arcanedev", "arcanesoft", "laravel", "support"], "time": "2018-09-04T17:19:43+00:00"}, {"name": "asm89/stack-cors", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/asm89/stack-cors.git", "reference": "b9c31def6a83f84b4d4a40d35996d375755f0e08"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/asm89/stack-cors/zipball/b9c31def6a83f84b4d4a40d35996d375755f0e08", "reference": "b9c31def6a83f84b4d4a40d35996d375755f0e08", "shasum": ""}, "require": {"php": ">=5.5.9", "symfony/http-foundation": "~2.7|~3.0|~4.0|~5.0", "symfony/http-kernel": "~2.7|~3.0|~4.0|~5.0"}, "require-dev": {"phpunit/phpunit": "^5.0 || ^4.8.10", "squizlabs/php_codesniffer": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"psr-4": {"Asm89\\Stack\\": "src/Asm89/Stack/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Cross-origin resource sharing library and stack middleware", "homepage": "https://github.com/asm89/stack-cors", "keywords": ["cors", "stack"], "time": "2019-12-24T22:41:47+00:00"}, {"name": "aws/aws-crt-php", "version": "v1.2.6", "source": {"type": "git", "url": "https://github.com/awslabs/aws-crt-php.git", "reference": "a63485b65b6b3367039306496d49737cf1995408"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/awslabs/aws-crt-php/zipball/a63485b65b6b3367039306496d49737cf1995408", "reference": "a63485b65b6b3367039306496d49737cf1995408", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35||^5.6.3||^9.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-awscrt": "Make sure you install awscrt native extension to use any of the functionality."}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "AWS SDK Common Runtime Team", "email": "<EMAIL>"}], "description": "AWS Common Runtime for PHP", "homepage": "https://github.com/awslabs/aws-crt-php", "keywords": ["amazon", "aws", "crt", "sdk"], "time": "2024-06-13T17:21:28+00:00"}, {"name": "aws/aws-sdk-php", "version": "3.322.9", "source": {"type": "git", "url": "https://github.com/aws/aws-sdk-php.git", "reference": "0bd7513a7dd9b5330cb92575da99d9057f79b39c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aws/aws-sdk-php/zipball/0bd7513a7dd9b5330cb92575da99d9057f79b39c", "reference": "0bd7513a7dd9b5330cb92575da99d9057f79b39c", "shasum": ""}, "require": {"aws/aws-crt-php": "^1.2.3", "ext-json": "*", "ext-pcre": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^6.5.8 || ^7.4.5", "guzzlehttp/promises": "^1.4.0 || ^2.0", "guzzlehttp/psr7": "^1.9.1 || ^2.4.5", "mtdowling/jmespath.php": "^2.6", "php": ">=7.2.5", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"andrewsville/php-token-reflection": "^1.4", "aws/aws-php-sns-message-validator": "~1.0", "behat/behat": "~3.0", "composer/composer": "^1.10.22", "dms/phpunit-arraysubset-asserts": "^0.4.0", "doctrine/cache": "~1.4", "ext-dom": "*", "ext-openssl": "*", "ext-pcntl": "*", "ext-sockets": "*", "nette/neon": "^2.3", "paragonie/random_compat": ">= 2", "phpunit/phpunit": "^5.6.3 || ^8.5 || ^9.5", "psr/cache": "^1.0", "psr/simple-cache": "^1.0", "sebastian/comparator": "^1.2.3 || ^4.0", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"aws/aws-php-sns-message-validator": "To validate incoming SNS notifications", "doctrine/cache": "To use the DoctrineCacheAdapter", "ext-curl": "To send requests using cURL", "ext-openssl": "Allows working with CloudFront private distributions and verifying received SNS messages", "ext-sockets": "To use client-side monitoring"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Aws\\": "src/"}, "exclude-from-classmap": ["src/data/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Amazon Web Services", "homepage": "http://aws.amazon.com"}], "description": "AWS SDK for PHP - Use Amazon Web Services in your PHP project", "homepage": "http://aws.amazon.com/sdkforphp", "keywords": ["amazon", "aws", "cloud", "dynamodb", "ec2", "glacier", "s3", "sdk"], "time": "2024-10-01T18:23:10+00:00"}, {"name": "bacon/bacon-qr-code", "version": "2.0.8", "source": {"type": "git", "url": "https://github.com/Bacon/BaconQrCode.git", "reference": "8674e51bb65af933a5ffaf1c308a660387c35c22"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Bacon/BaconQrCode/zipball/8674e51bb65af933a5ffaf1c308a660387c35c22", "reference": "8674e51bb65af933a5ffaf1c308a660387c35c22", "shasum": ""}, "require": {"dasprid/enum": "^1.0.3", "ext-iconv": "*", "php": "^7.1 || ^8.0"}, "require-dev": {"phly/keep-a-changelog": "^2.1", "phpunit/phpunit": "^7 | ^8 | ^9", "spatie/phpunit-snapshot-assertions": "^4.2.9", "squizlabs/php_codesniffer": "^3.4"}, "suggest": {"ext-imagick": "to generate QR code images"}, "type": "library", "autoload": {"psr-4": {"BaconQrCode\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "https://dasprids.de/", "role": "Developer"}], "description": "BaconQrCode is a QR code generator for PHP.", "homepage": "https://github.com/Bacon/BaconQrCode", "time": "2022-12-07T17:46:57+00:00"}, {"name": "barryvdh/laravel-cors", "version": "v0.11.4", "source": {"type": "git", "url": "https://github.com/fruitcake/laravel-cors.git", "reference": "03492f1a3bc74a05de23f93b94ac7cc5c173eec9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fruitcake/laravel-cors/zipball/03492f1a3bc74a05de23f93b94ac7cc5c173eec9", "reference": "03492f1a3bc74a05de23f93b94ac7cc5c173eec9", "shasum": ""}, "require": {"asm89/stack-cors": "^1.2", "illuminate/support": "^5.5|^6", "php": ">=7", "symfony/http-foundation": "^3.1|^4", "symfony/http-kernel": "^3.1|^4"}, "require-dev": {"laravel/framework": "^5.5", "orchestra/testbench": "3.3.x|3.4.x|3.5.x|3.6.x|3.7.x", "phpunit/phpunit": "^4.8|^5.2|^7.0", "squizlabs/php_codesniffer": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.11-dev"}, "laravel": {"providers": ["Barryvdh\\Cors\\ServiceProvider"]}}, "autoload": {"psr-4": {"Barryvdh\\Cors\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "Adds CORS (Cross-Origin Resource Sharing) headers support in your Laravel application", "keywords": ["api", "cors", "crossdomain", "laravel"], "abandoned": true, "time": "2019-08-28T11:27:11+00:00"}, {"name": "clue/stream-filter", "version": "v1.7.0", "source": {"type": "git", "url": "https://github.com/clue/stream-filter.git", "reference": "049509fef80032cb3f051595029ab75b49a3c2f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/stream-filter/zipball/049509fef80032cb3f051595029ab75b49a3c2f7", "reference": "049509fef80032cb3f051595029ab75b49a3c2f7", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"Clue\\StreamFilter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A simple and modern approach to stream filtering in PHP", "homepage": "https://github.com/clue/stream-filter", "keywords": ["bucket brigade", "callback", "filter", "php_user_filter", "stream", "stream_filter_append", "stream_filter_register"], "funding": [{"url": "https://clue.engineering/support", "type": "custom"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2023-12-20T15:40:13+00:00"}, {"name": "composer/package-versions-deprecated", "version": "*********", "source": {"type": "git", "url": "https://github.com/composer/package-versions-deprecated.git", "reference": "b4f54f74ef3453349c24a845d22392cd31e65f1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/package-versions-deprecated/zipball/b4f54f74ef3453349c24a845d22392cd31e65f1d", "reference": "b4f54f74ef3453349c24a845d22392cd31e65f1d", "shasum": ""}, "require": {"composer-plugin-api": "^1.1.0 || ^2.0", "php": "^7 || ^8"}, "replace": {"ocramius/package-versions": "1.11.99"}, "require-dev": {"composer/composer": "^1.9.3 || ^2.0@dev", "ext-zip": "^1.13", "phpunit/phpunit": "^6.5 || ^7"}, "type": "composer-plugin", "extra": {"class": "PackageVersions\\Installer", "branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"PackageVersions\\": "src/PackageVersions"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "Composer plugin that provides efficient querying for installed package versions (no runtime IO)", "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-01-17T14:14:24+00:00"}, {"name": "dasprid/enum", "version": "1.0.6", "source": {"type": "git", "url": "https://github.com/DASPRiD/Enum.git", "reference": "8dfd07c6d2cf31c8da90c53b83c026c7696dda90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/DASPRiD/Enum/zipball/8dfd07c6d2cf31c8da90c53b83c026c7696dda90", "reference": "8dfd07c6d2cf31c8da90c53b83c026c7696dda90", "shasum": ""}, "require": {"php": ">=7.1 <9.0"}, "require-dev": {"phpunit/phpunit": "^7 || ^8 || ^9 || ^10 || ^11", "squizlabs/php_codesniffer": "*"}, "type": "library", "autoload": {"psr-4": {"DASPRiD\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "https://dasprids.de/", "role": "Developer"}], "description": "PHP 7.1 enum implementation", "keywords": ["enum", "map"], "time": "2024-08-09T14:30:48+00:00"}, {"name": "dnoegel/php-xdg-base-dir", "version": "v0.1.1", "source": {"type": "git", "url": "https://github.com/dnoegel/php-xdg-base-dir.git", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dnoegel/php-xdg-base-dir/zipball/8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "~7.0|~6.0|~5.0|~4.8.35"}, "type": "library", "autoload": {"psr-4": {"XdgBaseDir\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "implementation of xdg base directory specification for php", "time": "2019-12-04T15:06:13+00:00"}, {"name": "doctrine/cache", "version": "1.13.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "56cd022adb5514472cb144c087393c1821911d09"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/56cd022adb5514472cb144c087393c1821911d09", "reference": "56cd022adb5514472cb144c087393c1821911d09", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "predis/predis": "~1.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2022-05-20T20:06:54+00:00"}, {"name": "doctrine/dbal", "version": "2.13.9", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "c480849ca3ad6706a39c970cdfe6888fa8a058b8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/c480849ca3ad6706a39c970cdfe6888fa8a058b8", "reference": "c480849ca3ad6706a39c970cdfe6888fa8a058b8", "shasum": ""}, "require": {"doctrine/cache": "^1.0|^2.0", "doctrine/deprecations": "^0.5.3|^1", "doctrine/event-manager": "^1.0", "ext-pdo": "*", "php": "^7.1 || ^8"}, "require-dev": {"doctrine/coding-standard": "9.0.0", "jetbrains/phpstorm-stubs": "2021.1", "phpstan/phpstan": "1.4.6", "phpunit/phpunit": "^7.5.20|^8.5|9.5.16", "psalm/plugin-phpunit": "0.16.1", "squizlabs/php_codesniffer": "3.6.2", "symfony/cache": "^4.4", "symfony/console": "^2.0.5|^3.0|^4.0|^5.0", "vimeo/psalm": "4.22.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "autoload": {"psr-4": {"Doctrine\\DBAL\\": "lib/Doctrine/DBAL"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlanywhere", "sqlite", "sqlserver", "sqlsrv"], "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "time": "2022-05-02T20:28:55+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "phpstan/phpstan": "1.4.10 || 1.10.15", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psalm/plugin-phpunit": "0.18.4", "psr/log": "^1 || ^2 || ^3", "vimeo/psalm": "4.30.0 || 5.12.0"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "time": "2024-01-30T19:34:25+00:00"}, {"name": "doctrine/event-manager", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "95aa4cb529f1e96576f3fda9f5705ada4056a520"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/95aa4cb529f1e96576f3fda9f5705ada4056a520", "reference": "95aa4cb529f1e96576f3fda9f5705ada4056a520", "shasum": ""}, "require": {"doctrine/deprecations": "^0.5.3 || ^1", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "~1.4.10 || ^1.8.8", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.24"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2022-10-12T20:51:15+00:00"}, {"name": "doctrine/inflector", "version": "1.4.4", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9", "reference": "4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector", "Doctrine\\Common\\Inflector\\": "lib/Doctrine/Common/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2021-04-16T17:34:40+00:00"}, {"name": "doctrine/lexer", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6", "reference": "861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^4.11 || ^5.21"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2024-02-05T11:35:39+00:00"}, {"name": "dragonmantank/cron-expression", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/dragonmantank/cron-expression.git", "reference": "65b2d8ee1f10915efb3b55597da3404f096acba2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/65b2d8ee1f10915efb3b55597da3404f096acba2", "reference": "65b2d8ee1f10915efb3b55597da3404f096acba2", "shasum": ""}, "require": {"php": "^7.0|^8.0"}, "require-dev": {"phpunit/phpunit": "^6.4|^7.0|^8.0|^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/dragonmantank"}], "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "funding": [{"url": "https://github.com/dragonmantank", "type": "github"}], "time": "2020-10-13T00:52:37+00:00"}, {"name": "egulias/email-validator", "version": "3.2.6", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7", "reference": "e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7", "shasum": ""}, "require": {"doctrine/lexer": "^1.2|^2", "php": ">=7.2", "symfony/polyfill-intl-idn": "^1.15"}, "require-dev": {"phpunit/phpunit": "^8.5.8|^9.3.3", "vimeo/psalm": "^4"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2023-06-01T07:04:22+00:00"}, {"name": "elasticsearch/elasticsearch", "version": "v7.9.0", "source": {"type": "git", "url": "https://github.com/elastic/elasticsearch-php.git", "reference": "1380925ccd276ab897c76596cf3705cb9714a78c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/elastic/elasticsearch-php/zipball/1380925ccd276ab897c76596cf3705cb9714a78c", "reference": "1380925ccd276ab897c76596cf3705cb9714a78c", "shasum": ""}, "require": {"ext-json": ">=1.3.7", "ezimuel/ringphp": "^1.1.2", "php": "^7.1", "psr/log": "~1.0"}, "require-dev": {"cpliakas/git-wrapper": "~2.0", "doctrine/inflector": "^1.3", "mockery/mockery": "^1.2", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^7.5", "squizlabs/php_codesniffer": "^3.4", "symfony/finder": "~4.0", "symfony/yaml": "~4.0"}, "suggest": {"ext-curl": "*", "monolog/monolog": "Allows for client-level logging and tracing"}, "type": "library", "autoload": {"files": ["src/autoload.php"], "psr-4": {"Elasticsearch\\": "src/Elasticsearch/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "description": "PHP Client for Elasticsearch", "keywords": ["client", "elasticsearch", "search"], "time": "2020-08-18T13:44:18+00:00"}, {"name": "erusev/parsedown", "version": "1.7.4", "source": {"type": "git", "url": "https://github.com/erusev/parsedown.git", "reference": "cb17b6477dfff935958ba01325f2e8a2bfa6dab3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/erusev/parsedown/zipball/cb17b6477dfff935958ba01325f2e8a2bfa6dab3", "reference": "cb17b6477dfff935958ba01325f2e8a2bfa6dab3", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35"}, "type": "library", "autoload": {"psr-0": {"Parsedown": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://erusev.com"}], "description": "<PERSON><PERSON>r for <PERSON>down.", "homepage": "http://parsedown.org", "keywords": ["markdown", "parser"], "time": "2019-12-30T22:54:17+00:00"}, {"name": "evenement/evenement", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/igorw/evenement.git", "reference": "0a16b0d71ab13284339abb99d9d2bd813640efbc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/igorw/evenement/zipball/0a16b0d71ab13284339abb99d9d2bd813640efbc", "reference": "0a16b0d71ab13284339abb99d9d2bd813640efbc", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^9 || ^6"}, "type": "library", "autoload": {"psr-4": {"Evenement\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}], "description": "Événement is a very simple event dispatching library for PHP", "keywords": ["event-dispatcher", "event-emitter"], "time": "2023-08-08T05:53:35+00:00"}, {"name": "ezimuel/guzzlestreams", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/ezimuel/guzzlestreams.git", "reference": "b4b5a025dfee70d6cd34c780e07330eb93d5b997"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/guzzlestreams/zipball/b4b5a025dfee70d6cd34c780e07330eb93d5b997", "reference": "b4b5a025dfee70d6cd34c780e07330eb93d5b997", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Stream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/streams (abandoned) to be used with elasticsearch-php", "homepage": "http://guzzlephp.org/", "keywords": ["Guzzle", "stream"], "time": "2022-10-24T12:58:50+00:00"}, {"name": "ezimuel/ringphp", "version": "1.2.2", "source": {"type": "git", "url": "https://github.com/ezimuel/ringphp.git", "reference": "7887fc8488013065f72f977dcb281994f5fde9f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/ringphp/zipball/7887fc8488013065f72f977dcb281994f5fde9f4", "reference": "7887fc8488013065f72f977dcb281994f5fde9f4", "shasum": ""}, "require": {"ezimuel/guzzlestreams": "^3.0.1", "php": ">=5.4.0", "react/promise": "~2.0"}, "replace": {"guzzlehttp/ringphp": "self.version"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "~9.0"}, "suggest": {"ext-curl": "Guzzle will use specific adapters if cURL is present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Ring\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/RingPHP (abandoned) to be used with elasticsearch-php", "time": "2022-12-07T11:28:53+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.13.0", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "08e27c97e4c6ed02f37c5b2b20488046c8d90d75"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/08e27c97e4c6ed02f37c5b2b20488046c8d90d75", "reference": "08e27c97e4c6ed02f37c5b2b20488046c8d90d75", "shasum": ""}, "require": {"php": ">=5.2"}, "require-dev": {"simpletest/simpletest": "dev-master#72de02a7b80c6bb8864ef9bf66d41d2f58f826bd"}, "type": "library", "autoload": {"files": ["library/HTMLPurifier.composer.php"], "psr-0": {"HTMLPurifier": "library/"}, "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "time": "2020-06-29T00:56:53+00:00"}, {"name": "fideloper/proxy", "version": "4.4.2", "source": {"type": "git", "url": "https://github.com/fideloper/TrustedProxy.git", "reference": "a751f2bc86dd8e6cfef12dc0cbdada82f5a18750"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fideloper/TrustedProxy/zipball/a751f2bc86dd8e6cfef12dc0cbdada82f5a18750", "reference": "a751f2bc86dd8e6cfef12dc0cbdada82f5a18750", "shasum": ""}, "require": {"illuminate/contracts": "^5.0|^6.0|^7.0|^8.0|^9.0", "php": ">=5.4.0"}, "require-dev": {"illuminate/http": "^5.0|^6.0|^7.0|^8.0|^9.0", "mockery/mockery": "^1.0", "phpunit/phpunit": "^8.5.8|^9.3.3"}, "type": "library", "extra": {"laravel": {"providers": ["Fideloper\\Proxy\\TrustedProxyServiceProvider"]}}, "autoload": {"psr-4": {"Fideloper\\Proxy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Set trusted proxies for <PERSON><PERSON>", "keywords": ["load balancing", "proxy", "trusted proxy"], "time": "2022-02-09T13:33:34+00:00"}, {"name": "firebase/php-jwt", "version": "v5.5.1", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "83b609028194aa042ea33b5af2d41a7427de80e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/83b609028194aa042ea33b5af2d41a7427de80e6", "reference": "83b609028194aa042ea33b5af2d41a7427de80e6", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": ">=4.8 <=9"}, "suggest": {"paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "time": "2021-11-08T20:18:51+00:00"}, {"name": "gloudemans/shoppingcart", "version": "2.6.0", "source": {"type": "git", "url": "https://github.com/Crinsane/LaravelShoppingcart.git", "reference": "f460ab7312cce32cb428cf39a500a2d16600b1d6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Crinsane/LaravelShoppingcart/zipball/f460ab7312cce32cb428cf39a500a2d16600b1d6", "reference": "f460ab7312cce32cb428cf39a500a2d16600b1d6", "shasum": ""}, "require": {"illuminate/events": "5.1.* || 5.2.* || 5.3.* || 5.4.* || 5.5.*|| 5.6.* || 5.7.*", "illuminate/session": "5.1.* || 5.2.* || 5.3.* || 5.4.* || 5.5.*|| 5.6.* || 5.7.*", "illuminate/support": "5.1.* || 5.2.* || 5.3.* || 5.4.* || 5.5.*|| 5.6.* || 5.7.*"}, "require-dev": {"mockery/mockery": "~0.9.0", "orchestra/testbench": "~3.1", "phpunit/phpunit": "~5.0 || ~6.0 || ~7.0"}, "suggest": {"gloudemans/notify": "Simple flash notifications for <PERSON><PERSON>"}, "type": "library", "extra": {"laravel": {"providers": ["Gloudemans\\Shoppingcart\\ShoppingcartServiceProvider"], "aliases": {"Cart": "Gloudemans\\Shoppingcart\\Facades\\Cart"}}}, "autoload": {"psr-4": {"Gloudemans\\Shoppingcart\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Laravel <PERSON>cart", "keywords": ["laravel", "shoppingcart"], "time": "2019-02-04T14:29:34+00:00"}, {"name": "google/apiclient", "version": "v2.14.0", "source": {"type": "git", "url": "https://github.com/googleapis/google-api-php-client.git", "reference": "789c8b07cad97f420ac0467c782036f955a2ad89"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-api-php-client/zipball/789c8b07cad97f420ac0467c782036f955a2ad89", "reference": "789c8b07cad97f420ac0467c782036f955a2ad89", "shasum": ""}, "require": {"firebase/php-jwt": "~2.0||~3.0||~4.0||~5.0||~6.0", "google/apiclient-services": "~0.200", "google/auth": "^1.10", "guzzlehttp/guzzle": "~5.3.3||~6.0||~7.0", "guzzlehttp/psr7": "^1.8.4||^2.2.1", "monolog/monolog": "^1.17||^2.0||^3.0", "php": "^5.6|^7.0|^8.0", "phpseclib/phpseclib": "~2.0||^3.0.2"}, "require-dev": {"cache/filesystem-adapter": "^0.3.2|^1.1", "composer/composer": "^1.10.22", "phpcompatibility/php-compatibility": "^9.2", "phpspec/prophecy-phpunit": "^1.1||^2.0", "phpunit/phpunit": "^5.7.21 || ^6.0 || ^7.0 || ^8.0 || ^9.0", "squizlabs/php_codesniffer": "^3.0", "symfony/css-selector": "~2.1", "symfony/dom-crawler": "~2.1", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"cache/filesystem-adapter": "For caching certs and tokens (using Google\\Client::setCache)"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"files": ["src/aliases.php"], "psr-4": {"Google\\": "src/"}, "classmap": ["src/aliases.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Client library for Google APIs", "homepage": "http://developers.google.com/api-client-library/php", "keywords": ["google"], "time": "2023-05-11T21:54:55+00:00"}, {"name": "google/apiclient-services", "version": "v0.302.0", "source": {"type": "git", "url": "https://github.com/googleapis/google-api-php-client-services.git", "reference": "ac872f59a7b4631b12628fe990c167d18a71c783"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-api-php-client-services/zipball/ac872f59a7b4631b12628fe990c167d18a71c783", "reference": "ac872f59a7b4631b12628fe990c167d18a71c783", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^5.7||^8.5.13"}, "type": "library", "autoload": {"files": ["autoload.php"], "psr-4": {"Google\\Service\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Client library for Google APIs", "homepage": "http://developers.google.com/api-client-library/php", "keywords": ["google"], "time": "2023-05-16T01:08:12+00:00"}, {"name": "google/auth", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/googleapis/google-auth-library-php.git", "reference": "f1f0d0319e2e7750ebfaa523c78819792a9ed9f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-auth-library-php/zipball/f1f0d0319e2e7750ebfaa523c78819792a9ed9f7", "reference": "f1f0d0319e2e7750ebfaa523c78819792a9ed9f7", "shasum": ""}, "require": {"firebase/php-jwt": "^5.5||^6.0", "guzzlehttp/guzzle": "^6.2.1|^7.0", "guzzlehttp/psr7": "^1.7|^2.0", "php": "^7.1||^8.0", "psr/cache": "^1.0|^2.0|^3.0", "psr/http-message": "^1.0"}, "require-dev": {"guzzlehttp/promises": "0.1.1|^1.3", "kelvinmo/simplejwt": "0.7.0", "phpseclib/phpseclib": "^2.0.31||^3.0", "phpspec/prophecy-phpunit": "^1.1||^2.0", "phpunit/phpunit": "^7.5||^9.0.0", "sebastian/comparator": ">=1.2.3", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"phpseclib/phpseclib": "May be used in place of OpenSSL for signing strings or for token management. Please require version ^2."}, "type": "library", "autoload": {"psr-4": {"Google\\Auth\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google Auth Library for PHP", "homepage": "http://github.com/google/google-auth-library-php", "keywords": ["Authentication", "google", "oauth2"], "time": "2023-04-05T15:11:57+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.5.8", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "a52f0440530b54fa079ce76e8c5d196a42cad981"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/a52f0440530b54fa079ce76e8c5d196a42cad981", "reference": "a52f0440530b54fa079ce76e8c5d196a42cad981", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.9", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.17"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.1"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.5-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2022-06-20T22:16:07+00:00"}, {"name": "guzzlehttp/promises", "version": "1.5.3", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "67ab6e18aaa14d753cc148911d273f6e6cb6721e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/67ab6e18aaa14d753cc148911d273f6e6cb6721e", "reference": "67ab6e18aaa14d753cc148911d273f6e6cb6721e", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2023-05-21T12:31:43+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.9.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "e4490cabc77465aaee90b20cfc9a770f8c04be6b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/e4490cabc77465aaee90b20cfc9a770f8c04be6b", "reference": "e4490cabc77465aaee90b20cfc9a770f8c04be6b", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2023-04-17T16:00:37+00:00"}, {"name": "http-interop/http-factory-guzzle", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/http-interop/http-factory-guzzle.git", "reference": "6e1efa1e020bf1c47cf0f13654e8ef9efb1463b3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/http-interop/http-factory-guzzle/zipball/6e1efa1e020bf1c47cf0f13654e8ef9efb1463b3", "reference": "6e1efa1e020bf1c47cf0f13654e8ef9efb1463b3", "shasum": ""}, "require": {"guzzlehttp/psr7": "^1.4.2||^2.0", "psr/http-factory": "^1.0"}, "provide": {"psr/http-factory-implementation": "^1.0"}, "require-dev": {"http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^8.5"}, "type": "library", "autoload": {"psr-4": {"Http\\Factory\\Guzzle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "An HTTP Factory using Guzzle PSR7", "keywords": ["factory", "http", "psr-17", "psr-7"], "time": "2021-07-23T15:14:50+00:00"}, {"name": "intervention/image", "version": "2.7.2", "source": {"type": "git", "url": "https://github.com/Intervention/image.git", "reference": "04be355f8d6734c826045d02a1079ad658322dad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Intervention/image/zipball/04be355f8d6734c826045d02a1079ad658322dad", "reference": "04be355f8d6734c826045d02a1079ad658322dad", "shasum": ""}, "require": {"ext-fileinfo": "*", "guzzlehttp/psr7": "~1.1 || ^2.0", "php": ">=5.4.0"}, "require-dev": {"mockery/mockery": "~0.9.2", "phpunit/phpunit": "^4.8 || ^5.7 || ^7.5.15"}, "suggest": {"ext-gd": "to use GD library based image processing.", "ext-imagick": "to use Imagick based image processing.", "intervention/imagecache": "Caching extension for the Intervention Image library"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}, "laravel": {"providers": ["Intervention\\Image\\ImageServiceProvider"], "aliases": {"Image": "Intervention\\Image\\Facades\\Image"}}}, "autoload": {"psr-4": {"Intervention\\Image\\": "src/Intervention/Image"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://intervention.io/"}], "description": "Image handling and manipulation library with support for Laravel integration", "homepage": "http://image.intervention.io/", "keywords": ["gd", "image", "imagick", "laravel", "thumbnail", "watermark"], "funding": [{"url": "https://paypal.me/interventionio", "type": "custom"}, {"url": "https://github.com/Intervention", "type": "github"}], "time": "2022-05-21T17:30:32+00:00"}, {"name": "jakub-onderka/php-console-color", "version": "v0.2", "source": {"type": "git", "url": "https://github.com/JakubOnderka/PHP-Console-Color.git", "reference": "d5deaecff52a0d61ccb613bb3804088da0307191"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/JakubOnderka/PHP-Console-Color/zipball/d5deaecff52a0d61ccb613bb3804088da0307191", "reference": "d5deaecff52a0d61ccb613bb3804088da0307191", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"jakub-onderka/php-code-style": "1.0", "jakub-onderka/php-parallel-lint": "1.0", "jakub-onderka/php-var-dump-check": "0.*", "phpunit/phpunit": "~4.3", "squizlabs/php_codesniffer": "1.*"}, "type": "library", "autoload": {"psr-4": {"JakubOnderka\\PhpConsoleColor\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "abandoned": "php-parallel-lint/php-console-color", "time": "2018-09-29T17:23:10+00:00"}, {"name": "jakub-onderka/php-console-highlighter", "version": "v0.4", "source": {"type": "git", "url": "https://github.com/JakubOnderka/PHP-Console-Highlighter.git", "reference": "9f7a229a69d52506914b4bc61bfdb199d90c5547"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/JakubOnderka/PHP-Console-Highlighter/zipball/9f7a229a69d52506914b4bc61bfdb199d90c5547", "reference": "9f7a229a69d52506914b4bc61bfdb199d90c5547", "shasum": ""}, "require": {"ext-tokenizer": "*", "jakub-onderka/php-console-color": "~0.2", "php": ">=5.4.0"}, "require-dev": {"jakub-onderka/php-code-style": "~1.0", "jakub-onderka/php-parallel-lint": "~1.0", "jakub-onderka/php-var-dump-check": "~0.1", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~1.5"}, "type": "library", "autoload": {"psr-4": {"JakubOnderka\\PhpConsoleHighlighter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.acci.cz/"}], "description": "Highlight PHP code in terminal", "abandoned": "php-parallel-lint/php-console-highlighter", "time": "2018-09-29T18:48:56+00:00"}, {"name": "jaybizzle/crawler-detect", "version": "v1.2.120", "source": {"type": "git", "url": "https://github.com/JayBizzle/Crawler-Detect.git", "reference": "2b325bdce46bbb8a2e96dc740ad37c743c9d8617"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/JayBizzle/Crawler-Detect/zipball/2b325bdce46bbb8a2e96dc740ad37c743c9d8617", "reference": "2b325bdce46bbb8a2e96dc740ad37c743c9d8617", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8|^5.5|^6.5|^9.4"}, "type": "library", "autoload": {"psr-4": {"Jaybizzle\\CrawlerDetect\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CrawlerDetect is a PHP class for detecting bots/crawlers/spiders via the user agent", "homepage": "https://github.com/JayBizzle/Crawler-Detect/", "keywords": ["crawler", "crawler detect", "crawler detector", "crawlerdetect", "php crawler detect"], "time": "2024-09-15T14:31:21+00:00"}, {"name": "jean85/pretty-package-versions", "version": "1.6.0", "source": {"type": "git", "url": "https://github.com/Jean85/pretty-package-versions.git", "reference": "1e0104b46f045868f11942aea058cd7186d6c303"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Jean85/pretty-package-versions/zipball/1e0104b46f045868f11942aea058cd7186d6c303", "reference": "1e0104b46f045868f11942aea058cd7186d6c303", "shasum": ""}, "require": {"composer/package-versions-deprecated": "^1.8.0", "php": "^7.0|^8.0"}, "require-dev": {"phpunit/phpunit": "^6.0|^8.5|^9.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Jean85\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A wrapper for ocramius/package-versions to get pretty versions strings", "keywords": ["composer", "package", "release", "versions"], "time": "2021-02-04T16:20:16+00:00"}, {"name": "jenssegers/agent", "version": "v2.6.4", "source": {"type": "git", "url": "https://github.com/jenssegers/agent.git", "reference": "daa11c43729510b3700bc34d414664966b03bffe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jenssegers/agent/zipball/daa11c43729510b3700bc34d414664966b03bffe", "reference": "daa11c43729510b3700bc34d414664966b03bffe", "shasum": ""}, "require": {"jaybizzle/crawler-detect": "^1.2", "mobiledetect/mobiledetectlib": "^2.7.6", "php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5.0|^6.0|^7.0"}, "suggest": {"illuminate/support": "Required for laravel service providers"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}, "laravel": {"providers": ["Jenssegers\\Agent\\AgentServiceProvider"], "aliases": {"Agent": "Jenssegers\\Agent\\Facades\\Agent"}}}, "autoload": {"psr-4": {"Jenssegers\\Agent\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://jenssegers.com"}], "description": "Desktop/mobile user agent parser with support for <PERSON><PERSON>, based on Mobiledetect", "homepage": "https://github.com/jenssegers/agent", "keywords": ["Agent", "browser", "desktop", "laravel", "mobile", "platform", "user agent", "useragent"], "funding": [{"url": "https://github.com/jenssegers", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/jenssegers/agent", "type": "tidelift"}], "time": "2020-06-13T08:05:20+00:00"}, {"name": "kalnoy/nestedset", "version": "v4.3.4", "source": {"type": "git", "url": "https://github.com/lazychaser/laravel-nestedset.git", "reference": "351fab2eaab95f885a0e52b01985301e93c4d7e7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lazychaser/laravel-nestedset/zipball/351fab2eaab95f885a0e52b01985301e93c4d7e7", "reference": "351fab2eaab95f885a0e52b01985301e93c4d7e7", "shasum": ""}, "require": {"illuminate/database": "5.2 - 5.8", "illuminate/events": "5.2 - 5.8", "illuminate/support": "5.2 - 5.8", "php": ">=5.5.9"}, "require-dev": {"phpunit/phpunit": "4.8.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "v4.2.x-dev"}, "laravel": {"providers": ["Kalnoy\\Nestedset\\NestedSetServiceProvider"]}}, "autoload": {"psr-4": {"Kalnoy\\Nestedset\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Nested Set Model for Laravel 4-5", "keywords": ["database", "hierarchy", "laravel", "nested sets", "nsm"], "time": "2019-02-27T15:22:30+00:00"}, {"name": "kylekatarnls/update-helper", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/kylekatarnls/update-helper.git", "reference": "429be50660ed8a196e0798e5939760f168ec8ce9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kylekatarnls/update-helper/zipball/429be50660ed8a196e0798e5939760f168ec8ce9", "reference": "429be50660ed8a196e0798e5939760f168ec8ce9", "shasum": ""}, "require": {"composer-plugin-api": "^1.1.0 || ^2.0.0", "php": ">=5.3.0"}, "require-dev": {"codeclimate/php-test-reporter": "dev-master", "composer/composer": "2.0.x-dev || ^2.0.0-dev", "phpunit/phpunit": ">=4.8.35 <6.0"}, "type": "composer-plugin", "extra": {"class": "UpdateHelper\\ComposerPlugin"}, "autoload": {"psr-0": {"UpdateHelper\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Update helper", "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2020-04-07T20:44:10+00:00"}, {"name": "laravel/framework", "version": "v5.7.15", "source": {"type": "git", "url": "https://github.com/laravel/framework.git", "reference": "e0dbd6ab143286d81bedf2b34f8820f3d49ea15f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/framework/zipball/e0dbd6ab143286d81bedf2b34f8820f3d49ea15f", "reference": "e0dbd6ab143286d81bedf2b34f8820f3d49ea15f", "shasum": ""}, "require": {"doctrine/inflector": "^1.1", "dragonmantank/cron-expression": "^2.0", "erusev/parsedown": "^1.7", "ext-mbstring": "*", "ext-openssl": "*", "league/flysystem": "^1.0.8", "monolog/monolog": "^1.12", "nesbot/carbon": "^1.26.3", "opis/closure": "^3.1", "php": "^7.1.3", "psr/container": "^1.0", "psr/simple-cache": "^1.0", "ramsey/uuid": "^3.7", "swiftmailer/swiftmailer": "^6.0", "symfony/console": "^4.1", "symfony/debug": "^4.1", "symfony/finder": "^4.1", "symfony/http-foundation": "^4.1", "symfony/http-kernel": "^4.1", "symfony/process": "^4.1", "symfony/routing": "^4.1", "symfony/var-dumper": "^4.1", "tijsverkoyen/css-to-inline-styles": "^2.2.1", "vlucas/phpdotenv": "^2.2"}, "conflict": {"tightenco/collect": "<5.5.33"}, "replace": {"illuminate/auth": "self.version", "illuminate/broadcasting": "self.version", "illuminate/bus": "self.version", "illuminate/cache": "self.version", "illuminate/config": "self.version", "illuminate/console": "self.version", "illuminate/container": "self.version", "illuminate/contracts": "self.version", "illuminate/cookie": "self.version", "illuminate/database": "self.version", "illuminate/encryption": "self.version", "illuminate/events": "self.version", "illuminate/filesystem": "self.version", "illuminate/hashing": "self.version", "illuminate/http": "self.version", "illuminate/log": "self.version", "illuminate/mail": "self.version", "illuminate/notifications": "self.version", "illuminate/pagination": "self.version", "illuminate/pipeline": "self.version", "illuminate/queue": "self.version", "illuminate/redis": "self.version", "illuminate/routing": "self.version", "illuminate/session": "self.version", "illuminate/support": "self.version", "illuminate/translation": "self.version", "illuminate/validation": "self.version", "illuminate/view": "self.version"}, "require-dev": {"aws/aws-sdk-php": "^3.0", "doctrine/dbal": "^2.6", "filp/whoops": "^2.1.4", "guzzlehttp/guzzle": "^6.3", "league/flysystem-cached-adapter": "^1.0", "mockery/mockery": "^1.0", "moontoast/math": "^1.1", "orchestra/testbench-core": "3.7.*", "pda/pheanstalk": "^3.0", "phpunit/phpunit": "^7.0", "predis/predis": "^1.1.1", "symfony/css-selector": "^4.1", "symfony/dom-crawler": "^4.1", "true/punycode": "^2.1"}, "suggest": {"aws/aws-sdk-php": "Required to use the SQS queue driver and SES mail driver (^3.0).", "doctrine/dbal": "Required to rename columns and drop SQLite columns (^2.6).", "ext-pcntl": "Required to use all features of the queue worker.", "ext-posix": "Required to use all features of the queue worker.", "filp/whoops": "Required for friendly error pages in development (^2.1.4).", "fzaninotto/faker": "Required to use the eloquent factory builder (^1.4).", "guzzlehttp/guzzle": "Required to use the Mailgun and Mandrill mail drivers and the ping methods on schedules (^6.0).", "laravel/tinker": "Required to use the tinker console command (^1.0).", "league/flysystem-aws-s3-v3": "Required to use the Flysystem S3 driver (^1.0).", "league/flysystem-cached-adapter": "Required to use the Flysystem cache (^1.0).", "league/flysystem-rackspace": "Required to use the Flysystem Rackspace driver (^1.0).", "league/flysystem-sftp": "Required to use the Flysystem SFTP driver (^1.0).", "moontoast/math": "Required to use ordered UUIDs (^1.1).", "nexmo/client": "Required to use the Nexmo transport (^1.0).", "pda/pheanstalk": "Required to use the beanstalk queue driver (^3.0).", "predis/predis": "Required to use the redis cache and queue drivers (^1.0).", "pusher/pusher-php-server": "Required to use the <PERSON><PERSON><PERSON> broadcast driver (^3.0).", "symfony/css-selector": "Required to use some of the crawler integration testing tools (^4.1).", "symfony/dom-crawler": "Required to use most of the crawler integration testing tools (^4.1).", "symfony/psr-http-message-bridge": "Required to psr7 bridging features (^1.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}}, "autoload": {"files": ["src/Illuminate/Foundation/helpers.php", "src/Illuminate/Support/helpers.php"], "psr-4": {"Illuminate\\": "src/Illuminate/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Laravel Framework.", "homepage": "https://laravel.com", "keywords": ["framework", "laravel"], "time": "2018-11-26T14:10:57+00:00"}, {"name": "laravel/socialite", "version": "v4.4.1", "source": {"type": "git", "url": "https://github.com/laravel/socialite.git", "reference": "80951df0d93435b773aa00efe1fad6d5015fac75"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/socialite/zipball/80951df0d93435b773aa00efe1fad6d5015fac75", "reference": "80951df0d93435b773aa00efe1fad6d5015fac75", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/guzzle": "^6.0|^7.0", "illuminate/http": "~5.7.0|~5.8.0|^6.0|^7.0", "illuminate/support": "~5.7.0|~5.8.0|^6.0|^7.0", "league/oauth1-client": "^1.0", "php": "^7.1.3"}, "require-dev": {"illuminate/contracts": "~5.7.0|~5.8.0|^6.0|^7.0", "mockery/mockery": "^1.0", "orchestra/testbench": "^3.7|^3.8|^4.0|^5.0", "phpunit/phpunit": "^7.0|^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}, "laravel": {"providers": ["Laravel\\Socialite\\SocialiteServiceProvider"], "aliases": {"Socialite": "Laravel\\Socialite\\Facades\\Socialite"}}}, "autoload": {"psr-4": {"Laravel\\Socialite\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Laravel wrapper around OAuth 1 & OAuth 2 libraries.", "homepage": "https://laravel.com", "keywords": ["laravel", "o<PERSON>h"], "time": "2020-06-03T13:30:03+00:00"}, {"name": "laravel/tinker", "version": "v1.0.10", "source": {"type": "git", "url": "https://github.com/laravel/tinker.git", "reference": "ad571aacbac1539c30d480908f9d0c9614eaf1a7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/tinker/zipball/ad571aacbac1539c30d480908f9d0c9614eaf1a7", "reference": "ad571aacbac1539c30d480908f9d0c9614eaf1a7", "shasum": ""}, "require": {"illuminate/console": "~5.1|^6.0", "illuminate/contracts": "~5.1|^6.0", "illuminate/support": "~5.1|^6.0", "php": ">=5.5.9", "psy/psysh": "0.7.*|0.8.*|0.9.*", "symfony/var-dumper": "~3.0|~4.0"}, "require-dev": {"phpunit/phpunit": "~4.0|~5.0"}, "suggest": {"illuminate/database": "The Illuminate Database package (~5.1)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}, "laravel": {"providers": ["Laravel\\Tinker\\TinkerServiceProvider"]}}, "autoload": {"psr-4": {"Laravel\\Tinker\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful REPL for the Laravel framework.", "keywords": ["REPL", "Tinker", "laravel", "psysh"], "time": "2019-08-07T15:10:45+00:00"}, {"name": "laravelcollective/html", "version": "v5.7.1", "source": {"type": "git", "url": "https://github.com/LaravelCollective/html.git", "reference": "777b6d390811ba249255ed5750bf17a019cd88a5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/LaravelCollective/html/zipball/777b6d390811ba249255ed5750bf17a019cd88a5", "reference": "777b6d390811ba249255ed5750bf17a019cd88a5", "shasum": ""}, "require": {"illuminate/http": "5.7.*", "illuminate/routing": "5.7.*", "illuminate/session": "5.7.*", "illuminate/support": "5.7.*", "illuminate/view": "5.7.*", "php": ">=7.1.3"}, "require-dev": {"illuminate/database": "5.7.*", "mockery/mockery": "~1.0", "phpunit/phpunit": "~7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.7-dev"}, "laravel": {"providers": ["Collective\\Html\\HtmlServiceProvider"], "aliases": {"Form": "Collective\\Html\\FormFacade", "Html": "Collective\\Html\\HtmlFacade"}}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Collective\\Html\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "HTML and Form Builders for the Laravel Framework", "homepage": "https://laravelcollective.com", "abandoned": "spatie/laravel-html", "time": "2018-09-05T18:32:53+00:00"}, {"name": "lcobucci/jwt", "version": "3.4.6", "source": {"type": "git", "url": "https://github.com/lcobucci/jwt.git", "reference": "3ef8657a78278dfeae7707d51747251db4176240"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lcobucci/jwt/zipball/3ef8657a78278dfeae7707d51747251db4176240", "reference": "3ef8657a78278dfeae7707d51747251db4176240", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-openssl": "*", "php": "^5.6 || ^7.0"}, "require-dev": {"mikey179/vfsstream": "~1.5", "phpmd/phpmd": "~2.2", "phpunit/php-invoker": "~1.1", "phpunit/phpunit": "^5.7 || ^7.3", "squizlabs/php_codesniffer": "~2.3"}, "suggest": {"lcobucci/clock": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["compat/class-aliases.php", "compat/json-exception-polyfill.php", "compat/lcobucci-clock-polyfill.php"], "psr-4": {"Lcobucci\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to work with JSON Web Token and JSON Web Signature", "keywords": ["JWS", "jwt"], "funding": [{"url": "https://github.com/lcobucci", "type": "github"}, {"url": "https://www.patreon.com/lcobucci", "type": "patreon"}], "time": "2021-09-28T19:18:28+00:00"}, {"name": "league/flysystem", "version": "1.1.10", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "3239285c825c152bcc315fe0e87d6b55f5972ed1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/3239285c825c152bcc315fe0e87d6b55f5972ed1", "reference": "3239285c825c152bcc315fe0e87d6b55f5972ed1", "shasum": ""}, "require": {"ext-fileinfo": "*", "league/mime-type-detection": "^1.3", "php": "^7.2.5 || ^8.0"}, "conflict": {"league/flysystem-sftp": "<1.0.6"}, "require-dev": {"phpspec/prophecy": "^1.11.1", "phpunit/phpunit": "^8.5.8"}, "suggest": {"ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Filesystem abstraction: Many filesystems, one API.", "keywords": ["Cloud Files", "WebDAV", "abstraction", "aws", "cloud", "copy.com", "dropbox", "file systems", "files", "filesystem", "filesystems", "ftp", "rackspace", "remote", "s3", "sftp", "storage"], "funding": [{"url": "https://offset.earth/frankdejonge", "type": "other"}], "time": "2022-10-04T09:16:37+00:00"}, {"name": "league/mime-type-detection", "version": "1.12.0", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "c7f2872fb273bf493811473dafc88d60ae829f48"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/c7f2872fb273bf493811473dafc88d60ae829f48", "reference": "c7f2872fb273bf493811473dafc88d60ae829f48", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3"}, "type": "library", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2023-08-03T07:14:11+00:00"}, {"name": "league/oauth1-client", "version": "v1.10.1", "source": {"type": "git", "url": "https://github.com/thephpleague/oauth1-client.git", "reference": "d6365b901b5c287dd41f143033315e2f777e1167"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/oauth1-client/zipball/d6365b901b5c287dd41f143033315e2f777e1167", "reference": "d6365b901b5c287dd41f143033315e2f777e1167", "shasum": ""}, "require": {"ext-json": "*", "ext-openssl": "*", "guzzlehttp/guzzle": "^6.0|^7.0", "guzzlehttp/psr7": "^1.7|^2.0", "php": ">=7.1||>=8.0"}, "require-dev": {"ext-simplexml": "*", "friendsofphp/php-cs-fixer": "^2.17", "mockery/mockery": "^1.3.3", "phpstan/phpstan": "^0.12.42", "phpunit/phpunit": "^7.5||9.5"}, "suggest": {"ext-simplexml": "For decoding XML-based responses."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev", "dev-develop": "2.0-dev"}}, "autoload": {"psr-4": {"League\\OAuth1\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.webcomm.com.au", "role": "Developer"}], "description": "OAuth 1.0 Client Library", "keywords": ["Authentication", "SSO", "authorization", "bitbucket", "identity", "idp", "o<PERSON>h", "oauth1", "single sign on", "trello", "tumblr", "twitter"], "time": "2022-04-15T14:02:14+00:00"}, {"name": "league/omnipay", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/thephpleague/omnipay.git", "reference": "9e10d91cbf84744207e13d4483e79de39b133368"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/omnipay/zipball/9e10d91cbf84744207e13d4483e79de39b133368", "reference": "9e10d91cbf84744207e13d4483e79de39b133368", "shasum": ""}, "require": {"omnipay/common": "^3", "php": "^5.6|^7", "php-http/guzzle6-adapter": "^1.1|^2"}, "require-dev": {"omnipay/tests": "^3"}, "type": "metapackage", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "Omnipay payment processing library", "homepage": "https://omnipay.thephpleague.com/", "keywords": ["checkout", "creditcard", "omnipay", "payment"], "time": "2019-03-20T14:28:28+00:00"}, {"name": "maatwebsite/excel", "version": "3.1.25", "source": {"type": "git", "url": "https://github.com/Maatwebsite/Laravel-Excel.git", "reference": "a3e56f1a60e49f21798fd242a3b3d2f4051eeda7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Maatwebsite/Laravel-Excel/zipball/a3e56f1a60e49f21798fd242a3b3d2f4051eeda7", "reference": "a3e56f1a60e49f21798fd242a3b3d2f4051eeda7", "shasum": ""}, "require": {"ext-json": "*", "illuminate/support": "5.5.*|5.6.*|5.7.*|5.8.*|^6.0|^7.0|^8.0", "php": "^7.0", "phpoffice/phpspreadsheet": "^1.14"}, "require-dev": {"orchestra/testbench": "^6.0", "predis/predis": "^1.1"}, "type": "library", "extra": {"laravel": {"providers": ["Maatwebsite\\Excel\\ExcelServiceProvider"], "aliases": {"Excel": "Maatwebsite\\Excel\\Facades\\Excel"}}}, "autoload": {"psr-4": {"Maatwebsite\\Excel\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Supercharged Excel exports and imports in Laravel", "keywords": ["PHPExcel", "batch", "csv", "excel", "export", "import", "laravel", "php", "phpspreadsheet"], "funding": [{"url": "https://laravel-excel.com/commercial-support", "type": "custom"}, {"url": "https://github.com/patrickbrouwers", "type": "github"}], "time": "2020-11-13T10:37:36+00:00"}, {"name": "maennchen/zipstream-php", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/maennchen/ZipStream-PHP.git", "reference": "c4c5803cc1f93df3d2448478ef79394a5981cc58"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/c4c5803cc1f93df3d2448478ef79394a5981cc58", "reference": "c4c5803cc1f93df3d2448478ef79394a5981cc58", "shasum": ""}, "require": {"myclabs/php-enum": "^1.5", "php": ">= 7.1", "psr/http-message": "^1.0", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"ext-zip": "*", "guzzlehttp/guzzle": ">= 6.3", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": ">= 7.5"}, "type": "library", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "funding": [{"url": "https://github.com/maennchen", "type": "github"}, {"url": "https://opencollective.com/zipstream", "type": "open_collective"}], "time": "2020-05-30T13:11:16+00:00"}, {"name": "markbaker/complex", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "time": "2022-12-06T16:21:08+00:00"}, {"name": "markbaker/matrix", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "728434227fe21be27ff6d86621a1b13107a2562c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/728434227fe21be27ff6d86621a1b13107a2562c", "reference": "728434227fe21be27ff6d86621a1b13107a2562c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "time": "2022-12-02T22:17:43+00:00"}, {"name": "mews/purifier", "version": "3.2.3", "source": {"type": "git", "url": "https://github.com/mewebstudio/Purifier.git", "reference": "01445654edf1a737efd9c93bbbe5c90ddf006712"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mewebstudio/Purifier/zipball/01445654edf1a737efd9c93bbbe5c90ddf006712", "reference": "01445654edf1a737efd9c93bbbe5c90ddf006712", "shasum": ""}, "require": {"ezyang/htmlpurifier": "4.13.*", "illuminate/config": "^5.1|^6.0|^7.0", "illuminate/filesystem": "^5.1|^6.0|^7.0", "illuminate/support": "^5.1|^6.0|^7.0", "php": "^7.2"}, "require-dev": {"graham-campbell/testbench": "^3.2", "mockery/mockery": "0.9.*", "phpunit/phpunit": "^4.8|^5.0", "scrutinizer/ocular": "^1.3"}, "suggest": {"laravel/framework": "To test the Laravel bindings", "laravel/lumen-framework": "To test the Lumen bindings"}, "type": "package", "extra": {"laravel": {"providers": ["Mews\\Purifier\\PurifierServiceProvider"], "aliases": {"Purifier": "Mews\\Purifier\\Facades\\Purifier"}}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Mews\\Purifier\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Muharrem ERİN", "email": "<EMAIL>", "homepage": "https://github.com/mewebstudio", "role": "Developer"}], "description": "Laravel 5/6/7 HtmlPurifier Package", "homepage": "https://github.com/mewebstudio/purifier", "keywords": ["Purifier", "htmlpurifier", "laravel5 HtmlPurifier", "laravel5 Purifier", "laravel5 Security", "laravel6 HtmlPurifier", "laravel6 Purifier", "laravel6 Security", "security", "xss"], "time": "2020-07-10T17:14:50+00:00"}, {"name": "mobiledetect/mobiledetectlib", "version": "2.8.45", "source": {"type": "git", "url": "https://github.com/serbanghita/Mobile-Detect.git", "reference": "96aaebcf4f50d3d2692ab81d2c5132e425bca266"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/serbanghita/Mobile-Detect/zipball/96aaebcf4f50d3d2692ab81d2c5132e425bca266", "reference": "96aaebcf4f50d3d2692ab81d2c5132e425bca266", "shasum": ""}, "require": {"php": ">=5.0.0"}, "require-dev": {"phpunit/phpunit": "~4.8.36"}, "type": "library", "autoload": {"psr-0": {"Detection": "namespaced/"}, "classmap": ["Mobile_Detect.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "serban<PERSON><PERSON>@gmail.com", "homepage": "http://mobiledetect.net", "role": "Developer"}], "description": "Mobile_Detect is a lightweight PHP class for detecting mobile devices. It uses the User-Agent string combined with specific HTTP headers to detect the mobile environment.", "homepage": "https://github.com/serbanghita/Mobile-Detect", "keywords": ["detect mobile devices", "mobile", "mobile detect", "mobile detector", "php mobile detect"], "funding": [{"url": "https://github.com/serbanghita", "type": "github"}], "time": "2023-11-07T21:57:25+00:00"}, {"name": "moneyphp/money", "version": "v3.3.3", "source": {"type": "git", "url": "https://github.com/moneyphp/money.git", "reference": "0dc40e3791c67e8793e3aa13fead8cf4661ec9cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/moneyphp/money/zipball/0dc40e3791c67e8793e3aa13fead8cf4661ec9cd", "reference": "0dc40e3791c67e8793e3aa13fead8cf4661ec9cd", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.6"}, "require-dev": {"cache/taggable-cache": "^0.4.0", "doctrine/instantiator": "^1.0.5", "ext-bcmath": "*", "ext-gmp": "*", "ext-intl": "*", "florianv/exchanger": "^1.0", "florianv/swap": "^3.0", "friends-of-phpspec/phpspec-code-coverage": "^3.1.1 || ^4.3", "moneyphp/iso-currencies": "^3.2.1", "php-http/message": "^1.4", "php-http/mock-client": "^1.0.0", "phpspec/phpspec": "^3.4.3", "phpunit/phpunit": "^5.7.27 || ^6.5.14 || ^7.5.18 || ^8.5", "psr/cache": "^1.0", "symfony/phpunit-bridge": "^4"}, "suggest": {"ext-bcmath": "Calculate without integer limits", "ext-gmp": "Calculate without integer limits", "ext-intl": "Format Money objects with intl", "florianv/exchanger": "Exchange rates library for PHP", "florianv/swap": "Exchange rates library for PHP", "psr/cache-implementation": "Used for Currency caching"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Money\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://verraes.net"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP implementation of Fowler's Money pattern", "homepage": "http://moneyphp.org", "keywords": ["Value Object", "money", "vo"], "time": "2022-09-21T07:43:36+00:00"}, {"name": "monolog/monolog", "version": "1.27.1", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "904713c5929655dc9b97288b69cfeedad610c9a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/904713c5929655dc9b97288b69cfeedad610c9a1", "reference": "904713c5929655dc9b97288b69cfeedad610c9a1", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpstan/phpstan": "^0.12.59", "phpunit/phpunit": "~4.5", "ruflin/elastica": ">=0.90 <3.0", "sentry/sentry": "^0.13", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "type": "library", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2022-06-09T08:53:42+00:00"}, {"name": "mtdowling/jmespath.php", "version": "2.8.0", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "a2a865e05d5f420b50cc2f85bb78d565db12a6bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/a2a865e05d5f420b50cc2f85bb78d565db12a6bc", "reference": "a2a865e05d5f420b50cc2f85bb78d565db12a6bc", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "symfony/polyfill-mbstring": "^1.17"}, "require-dev": {"composer/xdebug-handler": "^3.0.3", "phpunit/phpunit": "^8.5.33"}, "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"files": ["src/JmesPath.php"], "psr-4": {"JmesPath\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "time": "2024-09-04T18:46:31+00:00"}, {"name": "myclabs/php-enum", "version": "1.7.7", "source": {"type": "git", "url": "https://github.com/myclabs/php-enum.git", "reference": "d178027d1e679832db9f38248fcc7200647dc2b7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/php-enum/zipball/d178027d1e679832db9f38248fcc7200647dc2b7", "reference": "d178027d1e679832db9f38248fcc7200647dc2b7", "shasum": ""}, "require": {"ext-json": "*", "php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^3.8"}, "type": "library", "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP Enum contributors", "homepage": "https://github.com/myclabs/php-enum/graphs/contributors"}], "description": "PHP Enum implementation", "homepage": "http://github.com/myclabs/php-enum", "keywords": ["enum"], "funding": [{"url": "https://github.com/mnapoli", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/myclabs/php-enum", "type": "tidelift"}], "time": "2020-11-14T18:14:52+00:00"}, {"name": "namshi/jose", "version": "7.2.3", "source": {"type": "git", "url": "https://github.com/namshi/jose.git", "reference": "89a24d7eb3040e285dd5925fcad992378b82bcff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/namshi/jose/zipball/89a24d7eb3040e285dd5925fcad992378b82bcff", "reference": "89a24d7eb3040e285dd5925fcad992378b82bcff", "shasum": ""}, "require": {"ext-date": "*", "ext-hash": "*", "ext-json": "*", "ext-pcre": "*", "ext-spl": "*", "php": ">=5.5", "symfony/polyfill-php56": "^1.0"}, "require-dev": {"phpseclib/phpseclib": "^2.0", "phpunit/phpunit": "^4.5|^5.0", "satooshi/php-coveralls": "^1.0"}, "suggest": {"ext-openssl": "Allows to use OpenSSL as crypto engine.", "phpseclib/phpseclib": "Allows to use Phpseclib as crypto engine, use version ^2.0."}, "type": "library", "autoload": {"psr-4": {"Namshi\\JOSE\\": "src/Namshi/JOSE/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON> (cirpo)", "email": "<EMAIL>"}], "description": "JSON Object Signing and Encryption library for PHP.", "keywords": ["JSON Web Signature", "JSON Web Token", "JWS", "json", "jwt", "token"], "time": "2016-12-05T07:27:31+00:00"}, {"name": "nesbot/carbon", "version": "1.39.1", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon.git", "reference": "4be0c005164249208ce1b5ca633cd57bdd42ff33"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/4be0c005164249208ce1b5ca633cd57bdd42ff33", "reference": "4be0c005164249208ce1b5ca633cd57bdd42ff33", "shasum": ""}, "require": {"kylekatarnls/update-helper": "^1.1", "php": ">=5.3.9", "symfony/translation": "~2.6 || ~3.0 || ~4.0"}, "require-dev": {"composer/composer": "^1.2", "friendsofphp/php-cs-fixer": "~2", "phpunit/phpunit": "^4.8.35 || ^5.7"}, "bin": ["bin/upgrade-carbon"], "type": "library", "extra": {"update-helper": "Carbon\\Upgrade", "laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}}, "autoload": {"psr-4": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nesbot.com"}], "description": "A simple API extension for DateTime.", "homepage": "http://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "time": "2019-10-14T05:51:36+00:00"}, {"name": "neutron/temporary-filesystem", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/romainneutron/Temporary-Filesystem.git", "reference": "55f3d4896eff3bf070e491916e6c564db5e640b5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/romainneutron/Temporary-Filesystem/zipball/55f3d4896eff3bf070e491916e6c564db5e640b5", "reference": "55f3d4896eff3bf070e491916e6c564db5e640b5", "shasum": ""}, "require": {"php": ">=5.6", "symfony/filesystem": "^2.3 || ^3.0 || ^4.0 || ^5.0 || ^6.0"}, "require-dev": {"symfony/phpunit-bridge": "^5.0.4 || ^6.0"}, "type": "library", "autoload": {"psr-0": {"Neutron": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony filesystem extension to handle temporary files", "time": "2021-12-14T07:30:33+00:00"}, {"name": "nikic/php-parser", "version": "v4.19.4", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "715f4d25e225bc47b293a8b997fe6ce99bf987d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/715f4d25e225bc47b293a8b997fe6ce99bf987d2", "reference": "715f4d25e225bc47b293a8b997fe6ce99bf987d2", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.1"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "time": "2024-09-29T15:01:53+00:00"}, {"name": "omnipay/common", "version": "v3.3.0", "source": {"type": "git", "url": "https://github.com/thephpleague/omnipay-common.git", "reference": "2eca3823e9069e2c36b6007a090577d5584f9518"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/omnipay-common/zipball/2eca3823e9069e2c36b6007a090577d5584f9518", "reference": "2eca3823e9069e2c36b6007a090577d5584f9518", "shasum": ""}, "require": {"moneyphp/money": "^3.1|^4.0.3", "php": "^7.2|^8", "php-http/client-implementation": "^1", "php-http/discovery": "^1.14", "php-http/message": "^1.5", "php-http/message-factory": "^1.1", "symfony/http-foundation": "^2.1|^3|^4|^5|^6|^7"}, "require-dev": {"http-interop/http-factory-guzzle": "^1.1", "omnipay/tests": "^4.1", "php-http/guzzle7-adapter": "^1", "php-http/mock-client": "^1.6", "squizlabs/php_codesniffer": "^3.8.1"}, "suggest": {"league/omnipay": "The default Omnipay package provides a default HTTP Adapter."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"psr-4": {"Omnipay\\Common\\": "src/Common"}, "classmap": ["src/Omnipay.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Barry vd. Heuvel", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Del"}, {"name": "Omnipay Contributors", "homepage": "https://github.com/thephpleague/omnipay-common/contributors"}], "description": "Common components for Omnipay payment processing library", "homepage": "https://github.com/thephpleague/omnipay-common", "keywords": ["gateway", "merchant", "omnipay", "pay", "payment", "purchase"], "funding": [{"url": "https://github.com/barryvdh", "type": "github"}], "time": "2024-03-08T11:56:40+00:00"}, {"name": "opis/closure", "version": "3.6.3", "source": {"type": "git", "url": "https://github.com/opis/closure.git", "reference": "3d81e4309d2a927abbe66df935f4bb60082805ad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opis/closure/zipball/3d81e4309d2a927abbe66df935f4bb60082805ad", "reference": "3d81e4309d2a927abbe66df935f4bb60082805ad", "shasum": ""}, "require": {"php": "^5.4 || ^7.0 || ^8.0"}, "require-dev": {"jeremeamia/superclosure": "^2.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.6.x-dev"}}, "autoload": {"files": ["functions.php"], "psr-4": {"Opis\\Closure\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Sorin Sarca", "email": "<EMAIL>"}], "description": "A library that can be used to serialize closures (anonymous functions) and arbitrary objects.", "homepage": "https://opis.io/closure", "keywords": ["anonymous functions", "closure", "function", "serializable", "serialization", "serialize"], "time": "2022-01-27T09:35:39+00:00"}, {"name": "paquettg/php-html-parser", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/paquettg/php-html-parser.git", "reference": "c116346b2fcc038d4ab6615f4328346a4eebf121"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paquettg/php-html-parser/zipball/c116346b2fcc038d4ab6615f4328346a4eebf121", "reference": "c116346b2fcc038d4ab6615f4328346a4eebf121", "shasum": ""}, "require": {"ext-curl": "*", "ext-mbstring": "*", "ext-zlib": "*", "guzzlehttp/psr7": "^1.6", "myclabs/php-enum": "^1.7", "paquettg/string-encode": "~1.0.0", "php": ">=7.1", "php-http/guzzle6-adapter": "^2.0", "php-http/httplug": "^2.1"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16", "infection/infection": "^0.13.4", "mockery/mockery": "^1.2", "phan/phan": "^2.4", "php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^7.5.1"}, "type": "library", "autoload": {"psr-4": {"PHPHtmlParser\\": "src/PHPHtmlParser"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://gillespaquette.ca"}], "description": "An HTML DOM parser. It allows you to manipulate HTML. Find tags on an HTML page with selectors just like jQuery.", "homepage": "https://github.com/paquettg/php-html-parser", "keywords": ["dom", "html", "parser"], "time": "2020-07-19T18:28:37+00:00"}, {"name": "paquettg/string-encode", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/paquettg/string-encoder.git", "reference": "a8708e9fac9d5ddfc8fc2aac6004e2cd05d80fee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paquettg/string-encoder/zipball/a8708e9fac9d5ddfc8fc2aac6004e2cd05d80fee", "reference": "a8708e9fac9d5ddfc8fc2aac6004e2cd05d80fee", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.5.1"}, "type": "library", "autoload": {"psr-0": {"stringEncode": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://gillespaquette.ca"}], "description": "Facilitating the process of altering string encoding in PHP.", "homepage": "https://github.com/paquettg/string-encoder", "keywords": ["charset", "encoding", "string"], "time": "2018-12-21T02:25:09+00:00"}, {"name": "paragonie/constant_time_encoding", "version": "v2.7.0", "source": {"type": "git", "url": "https://github.com/paragonie/constant_time_encoding.git", "reference": "52a0d99e69f56b9ec27ace92ba56897fe6993105"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/52a0d99e69f56b9ec27ace92ba56897fe6993105", "reference": "52a0d99e69f56b9ec27ace92ba56897fe6993105", "shasum": ""}, "require": {"php": "^7|^8"}, "require-dev": {"phpunit/phpunit": "^6|^7|^8|^9", "vimeo/psalm": "^1|^2|^3|^4"}, "type": "library", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "time": "2024-05-08T12:18:48+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "time": "2020-10-15T08:29:30+00:00"}, {"name": "php-ffmpeg/php-ffmpeg", "version": "v0.18.0", "source": {"type": "git", "url": "https://github.com/PHP-FFMpeg/PHP-FFMpeg.git", "reference": "edc0a7729d8818ed883e77b3d26ceb6d49ec41de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHP-FFMpeg/PHP-FFMpeg/zipball/edc0a7729d8818ed883e77b3d26ceb6d49ec41de", "reference": "edc0a7729d8818ed883e77b3d26ceb6d49ec41de", "shasum": ""}, "require": {"alchemy/binary-driver": "^1.5 || ~2.0.0 || ^5.0", "doctrine/cache": "^1.0", "evenement/evenement": "^3.0 || ^2.0 || ^1.0", "neutron/temporary-filesystem": "^2.1.1 || ^3.0", "php": ">=5.3.9"}, "require-dev": {"silex/silex": "~1.0", "symfony/phpunit-bridge": "^5.0.4", "symfony/process": "2.8 || 3.3"}, "suggest": {"php-ffmpeg/extras": "A compilation of common audio & video drivers for PHP-FFMpeg"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.x-dev"}}, "autoload": {"psr-0": {"FFMpeg": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.lickmychip.com/"}, {"name": "Phraseanet Team", "email": "<EMAIL>", "homepage": "http://www.phraseanet.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.karisch.guru"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.strime.io/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://jens-hausdorf.de"}], "description": "FFMpeg PHP, an Object Oriented library to communicate with AVconv / ffmpeg", "keywords": ["audio", "audio processing", "avconv", "avprobe", "ffmpeg", "ffprobe", "video", "video processing"], "time": "2021-03-29T20:20:00+00:00"}, {"name": "php-http/client-common", "version": "2.7.2", "source": {"type": "git", "url": "https://github.com/php-http/client-common.git", "reference": "0cfe9858ab9d3b213041b947c881d5b19ceeca46"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/client-common/zipball/0cfe9858ab9d3b213041b947c881d5b19ceeca46", "reference": "0cfe9858ab9d3b213041b947c881d5b19ceeca46", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/httplug": "^2.0", "php-http/message": "^1.6", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0 || ^2.0", "symfony/options-resolver": "~4.0.15 || ~4.1.9 || ^4.2.1 || ^5.0 || ^6.0 || ^7.0", "symfony/polyfill-php80": "^1.17"}, "require-dev": {"doctrine/instantiator": "^1.1", "guzzlehttp/psr7": "^1.4", "nyholm/psr7": "^1.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "phpspec/prophecy": "^1.10.2", "phpunit/phpunit": "^7.5.20 || ^8.5.33 || ^9.6.7"}, "suggest": {"ext-json": "To detect JSON responses with the ContentTypePlugin", "ext-libxml": "To detect XML responses with the ContentTypePlugin", "php-http/cache-plugin": "PSR-6 Cache plugin", "php-http/logger-plugin": "PSR-3 Logger plugin", "php-http/stopwatch-plugin": "Symfony Stopwatch plugin"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\Common\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common HTTP Client implementations and tools for HTTPlug", "homepage": "http://httplug.io", "keywords": ["client", "common", "http", "httplug"], "time": "2024-09-24T06:21:48+00:00"}, {"name": "php-http/discovery", "version": "1.19.4", "source": {"type": "git", "url": "https://github.com/php-http/discovery.git", "reference": "0700efda8d7526335132360167315fdab3aeb599"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/discovery/zipball/0700efda8d7526335132360167315fdab3aeb599", "reference": "0700efda8d7526335132360167315fdab3aeb599", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": "^7.1 || ^8.0"}, "conflict": {"nyholm/psr7": "<1.0", "zendframework/zend-diactoros": "*"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "*", "psr/http-factory-implementation": "*", "psr/http-message-implementation": "*"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "graham-campbell/phpspec-skip-example-extension": "^5.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/message-factory": "^1.0", "phpspec/phpspec": "^5.1 || ^6.1 || ^7.3", "sebastian/comparator": "^3.0.5 || ^4.0.8", "symfony/phpunit-bridge": "^6.4.4 || ^7.0.1"}, "type": "composer-plugin", "extra": {"class": "Http\\Discovery\\Composer\\Plugin", "plugin-optional": true}, "autoload": {"psr-4": {"Http\\Discovery\\": "src/"}, "exclude-from-classmap": ["src/Composer/Plugin.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Finds and installs PSR-7, PSR-17, PSR-18 and HTTPlug implementations", "homepage": "http://php-http.org", "keywords": ["adapter", "client", "discovery", "factory", "http", "message", "psr17", "psr7"], "time": "2024-03-29T13:00:05+00:00"}, {"name": "php-http/guzzle6-adapter", "version": "v2.0.2", "source": {"type": "git", "url": "https://github.com/php-http/guzzle6-adapter.git", "reference": "9d1a45eb1c59f12574552e81fb295e9e53430a56"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/guzzle6-adapter/zipball/9d1a45eb1c59f12574552e81fb295e9e53430a56", "reference": "9d1a45eb1c59f12574552e81fb295e9e53430a56", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.0", "php": "^7.1 || ^8.0", "php-http/httplug": "^2.0", "psr/http-client": "^1.0"}, "provide": {"php-http/async-client-implementation": "1.0", "php-http/client-implementation": "1.0", "psr/http-client-implementation": "1.0"}, "require-dev": {"ext-curl": "*", "php-http/client-integration-tests": "^2.0 || ^3.0", "phpunit/phpunit": "^7.4 || ^8.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Http\\Adapter\\Guzzle6\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Guzzle 6 HTTP Adapter", "homepage": "http://httplug.io", "keywords": ["Guzzle", "http"], "abandoned": "guzzlehttp/guzzle or php-http/guzzle7-adapter", "time": "2021-03-02T10:52:33+00:00"}, {"name": "php-http/httplug", "version": "2.4.1", "source": {"type": "git", "url": "https://github.com/php-http/httplug.git", "reference": "5cad731844891a4c282f3f3e1b582c46839d22f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/httplug/zipball/5cad731844891a4c282f3f3e1b582c46839d22f4", "reference": "5cad731844891a4c282f3f3e1b582c46839d22f4", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/promise": "^1.1", "psr/http-client": "^1.0", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.1 || ^5.0 || ^6.0", "phpspec/phpspec": "^5.1 || ^6.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Eric <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "HTTPlug, the HTTP client abstraction for PHP", "homepage": "http://httplug.io", "keywords": ["client", "http"], "time": "2024-09-23T11:39:58+00:00"}, {"name": "php-http/message", "version": "1.16.1", "source": {"type": "git", "url": "https://github.com/php-http/message.git", "reference": "5997f3289332c699fa2545c427826272498a2088"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message/zipball/5997f3289332c699fa2545c427826272498a2088", "reference": "5997f3289332c699fa2545c427826272498a2088", "shasum": ""}, "require": {"clue/stream-filter": "^1.5", "php": "^7.2 || ^8.0", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"php-http/message-factory-implementation": "1.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.6", "ext-zlib": "*", "guzzlehttp/psr7": "^1.0 || ^2.0", "laminas/laminas-diactoros": "^2.0 || ^3.0", "php-http/message-factory": "^1.0.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "slim/slim": "^3.0"}, "suggest": {"ext-zlib": "Used with compressor/decompressor streams", "guzzlehttp/psr7": "Used with Guzzle PSR-7 Factories", "laminas/laminas-diactoros": "Used with Diactoros Factories", "slim/slim": "Used with Slim Framework PSR-7 implementation"}, "type": "library", "autoload": {"files": ["src/filters.php"], "psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "HTTP Message related tools", "homepage": "http://php-http.org", "keywords": ["http", "message", "psr-7"], "time": "2024-03-07T13:22:09+00:00"}, {"name": "php-http/message-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-http/message-factory.git", "reference": "4d8778e1c7d405cbb471574821c1ff5b68cc8f57"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message-factory/zipball/4d8778e1c7d405cbb471574821c1ff5b68cc8f57", "reference": "4d8778e1c7d405cbb471574821c1ff5b68cc8f57", "shasum": ""}, "require": {"php": ">=5.4", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Factory interfaces for PSR-7 HTTP Message", "homepage": "http://php-http.org", "keywords": ["factory", "http", "message", "stream", "uri"], "abandoned": "psr/http-factory", "time": "2023-04-14T14:16:17+00:00"}, {"name": "php-http/promise", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/php-http/promise.git", "reference": "fc85b1fba37c169a69a07ef0d5a8075770cc1f83"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/promise/zipball/fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "reference": "fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.3.2 || ^6.3", "phpspec/phpspec": "^5.1.2 || ^6.2 || ^7.4"}, "type": "library", "autoload": {"psr-4": {"Http\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Promise used for asynchronous HTTP requests", "homepage": "http://httplug.io", "keywords": ["promise"], "time": "2024-03-15T13:55:21+00:00"}, {"name": "phpoffice/math", "version": "0.2.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/Math.git", "reference": "fc2eb6d1a61b058d5dac77197059db30ee3c8329"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/Math/zipball/fc2eb6d1a61b058d5dac77197059db30ee3c8329", "reference": "fc2eb6d1a61b058d5dac77197059db30ee3c8329", "shasum": ""}, "require": {"ext-dom": "*", "ext-xml": "*", "php": "^7.1|^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.88 || ^1.0.0", "phpunit/phpunit": "^7.0 || ^9.0"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\Math\\": "src/Math/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Progi1984", "homepage": "https://lefevre.dev"}], "description": "Math - Manipulate Math Formula", "homepage": "https://phpoffice.github.io/Math/", "keywords": ["MathML", "officemathml", "php"], "time": "2024-08-12T07:30:45+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "1.19.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "a9ab55bfae02eecffb3df669a2e19ba0e2f04bbf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/a9ab55bfae02eecffb3df669a2e19ba0e2f04bbf", "reference": "a9ab55bfae02eecffb3df669a2e19ba0e2f04bbf", "shasum": ""}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "ezyang/htmlpurifier": "^4.13", "maennchen/zipstream-php": "^2.1", "markbaker/complex": "^3.0", "markbaker/matrix": "^3.0", "php": "^7.2 || ^8.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "dompdf/dompdf": "^1.0", "friendsofphp/php-cs-fixer": "^2.18", "jpgraph/jpgraph": "^4.0", "mpdf/mpdf": "^8.0", "phpcompatibility/php-compatibility": "^9.3", "phpstan/phpstan": "^0.12.82", "phpstan/phpstan-phpunit": "^0.12.18", "phpunit/phpunit": "^8.5", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "^6.3"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer (doesn't yet support PHP8)", "jpgraph/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer (doesn't yet support PHP8)"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "time": "2021-10-31T15:09:20+00:00"}, {"name": "phpoffice/phpword", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/PHPWord.git", "reference": "8392134ce4b5dba65130ba956231a1602b848b7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PHPWord/zipball/8392134ce4b5dba65130ba956231a1602b848b7f", "reference": "8392134ce4b5dba65130ba956231a1602b848b7f", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-xml": "*", "php": "^7.1|^8.0", "phpoffice/math": "^0.2"}, "require-dev": {"dompdf/dompdf": "^2.0", "ext-gd": "*", "ext-libxml": "*", "ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.3", "mpdf/mpdf": "^8.1", "phpmd/phpmd": "^2.13", "phpstan/phpstan-phpunit": "@stable", "phpunit/phpunit": ">=7.0", "symfony/process": "^4.4 || ^5.0", "tecnickcom/tcpdf": "^6.5"}, "suggest": {"dompdf/dompdf": "Allows writing PDF", "ext-gd2": "Allows adding images", "ext-xmlwriter": "Allows writing OOXML and ODF", "ext-xsl": "Allows applying XSL style sheet to headers, to main document part, and to footers of an OOXML template", "ext-zip": "Allows writing OOXML and ODF"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpWord\\": "src/PhpWord"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://gabrielbull.com/"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net/blog/"}, {"name": "<PERSON>", "homepage": "http://ivan.lanin.org"}, {"name": "<PERSON>", "homepage": "http://ru.linkedin.com/pub/roman-syroeshko/34/a53/994/"}, {"name": "<PERSON>"}], "description": "PHPWord - A pure PHP library for reading and writing word processing documents (OOXML, ODF, RTF, HTML, PDF)", "homepage": "https://phpoffice.github.io/PHPWord/", "keywords": ["ISO IEC 29500", "OOXML", "Office Open XML", "OpenDocument", "OpenXML", "PhpOffice", "PhpWord", "Rich Text Format", "WordprocessingML", "doc", "docx", "html", "odf", "odt", "office", "pdf", "php", "reader", "rtf", "template", "template processor", "word", "writer"], "time": "2024-08-30T18:03:42+00:00"}, {"name": "phpseclib/phpseclib", "version": "3.0.43", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "709ec107af3cb2f385b9617be72af8cf62441d02"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/709ec107af3cb2f385b9617be72af8cf62441d02", "reference": "709ec107af3cb2f385b9617be72af8cf62441d02", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^1|^2|^3", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": ">=5.6.1"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-dom": "Install the DOM extension to load XML formatted public keys.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations."}, "type": "library", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib3\\": "phpseclib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "time": "2024-12-14T21:12:59+00:00"}, {"name": "pragmarx/google2fa", "version": "v7.0.0", "source": {"type": "git", "url": "https://github.com/antonioribeiro/google2fa.git", "reference": "0afb47f8a686bd203fe85a05bab85139f3c1971e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/antonioribeiro/google2fa/zipball/0afb47f8a686bd203fe85a05bab85139f3c1971e", "reference": "0afb47f8a686bd203fe85a05bab85139f3c1971e", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "~1.0|~2.0", "paragonie/random_compat": ">=1", "php": ">=5.4", "symfony/polyfill-php56": "~1.2"}, "require-dev": {"phpunit/phpunit": "~4|~5|~6|~7|~8"}, "type": "library", "extra": {"component": "package", "branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"PragmaRX\\Google2FA\\": "src/", "PragmaRX\\Google2FA\\Tests\\": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Creator & Designer"}], "description": "A One Time Password Authentication package, compatible with Google Authenticator.", "keywords": ["2fa", "Authentication", "Two Factor Authentication", "google2fa"], "time": "2019-10-21T17:49:22+00:00"}, {"name": "predis/predis", "version": "v1.1.10", "source": {"type": "git", "url": "https://github.com/predis/predis.git", "reference": "a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/predis/predis/zipball/a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e", "reference": "a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e", "shasum": ""}, "require": {"php": ">=5.3.9"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "suggest": {"ext-curl": "Allows access to Webdis when paired with phpiredis", "ext-phpiredis": "Allows faster serialization and deserialization of the Redis protocol"}, "type": "library", "autoload": {"psr-4": {"Predis\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://clorophilla.net", "role": "Creator & Maintainer"}, {"name": "<PERSON>", "homepage": "https://till.im", "role": "Maintainer"}], "description": "Flexible and feature-complete Redis client for PHP and HHVM", "homepage": "http://github.com/predis/predis", "keywords": ["nosql", "predis", "redis"], "funding": [{"url": "https://github.com/sponsors/tillkruss", "type": "github"}], "time": "2022-01-05T17:46:08+00:00"}, {"name": "prettus/l5-repository", "version": "2.9.1", "source": {"type": "git", "url": "https://github.com/andersao/l5-repository.git", "reference": "105115badd9ee3e12d2d9368c9392f47cb5d14a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/andersao/l5-repository/zipball/105115badd9ee3e12d2d9368c9392f47cb5d14a8", "reference": "105115badd9ee3e12d2d9368c9392f47cb5d14a8", "shasum": ""}, "require": {"illuminate/config": "~5.0|~6.0|~7.0|^8.0|^9.0|^10.0|^11.0", "illuminate/console": "~5.0|~6.0|~7.0|^8.0|^9.0|^10.0|^11.0", "illuminate/database": "~5.0|~6.0|~7.0|^8.0|^9.0|^10.0|^11.0", "illuminate/filesystem": "~5.0|~6.0|~7.0|^8.0|^9.0|^10.0|^11.0", "illuminate/http": "~5.0|~6.0|~7.0|^8.0|^9.0|^10.0|^11.0", "illuminate/pagination": "~5.0|~6.0|~7.0|^8.0|^9.0|^10.0|^11.0", "illuminate/support": "~5.0|~6.0|~7.0|^8.0|^9.0|^10.0|^11.0", "illuminate/validation": "~5.0|~6.0|~7.0|^8.0|^9.0|^10.0|^11.0", "prettus/laravel-validation": "~1.1|~1.2|~1.3|~1.4|~1.5|~1.6"}, "suggest": {"league/fractal": "Required to use the Fractal Presenter (0.12.*).", "prettus/laravel-validation": "Required to provide easy validation with the repository (1.1.*)", "robclancy/presenter": "Required to use the Presenter Model (1.3.*)"}, "type": "library", "extra": {"laravel": {"providers": ["Prettus\\Repository\\Providers\\RepositoryServiceProvider"]}}, "autoload": {"psr-4": {"Prettus\\Repository\\": "src/Prettus/Repository/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://andersonandra.de", "role": "Developer"}], "description": "Laravel 5|6|7|8|9|10|11 - Repositories to  the database layer", "homepage": "http://andersao.github.io/l5-repository", "keywords": ["cache", "eloquent", "laravel", "model", "repository"], "time": "2024-05-06T14:42:53+00:00"}, {"name": "prettus/laravel-validation", "version": "1.6.0", "source": {"type": "git", "url": "https://github.com/andersao/laravel-validator.git", "reference": "d990a2cce135bcfced8db7cfff827cd9055740b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/andersao/laravel-validator/zipball/d990a2cce135bcfced8db7cfff827cd9055740b2", "reference": "d990a2cce135bcfced8db7cfff827cd9055740b2", "shasum": ""}, "require": {"illuminate/support": "~5.4|^6.0|^7.0|^8.0|^9.0|^10.0|^11.0", "illuminate/validation": "~5.4|^6.0|^7.0|^8.0|^9.0|^10.0|^11.0", "php": ">=5.4.0"}, "type": "library", "autoload": {"psr-4": {"Prettus\\Validator\\": "src/Prettus/Validator/"}}, "notification-url": "https://packagist.org/downloads/", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://andersonandra.de", "role": "Developer"}], "description": "Laravel Validation Service", "homepage": "http://andersao.github.io/laravel-validation", "keywords": ["laravel", "service", "validation"], "time": "2024-05-05T23:33:47+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/container", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "8622567409010282b7aeebe4bb841fe98b58dcaf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/8622567409010282b7aeebe4bb841fe98b58dcaf", "reference": "8622567409010282b7aeebe4bb841fe98b58dcaf", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "time": "2021-03-05T17:36:06+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "1.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2023-04-04T09:50:52+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2021-05-03T11:20:27+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "time": "2017-10-23T01:57:42+00:00"}, {"name": "psy/psysh", "version": "v0.9.12", "source": {"type": "git", "url": "https://github.com/bobthecow/psysh.git", "reference": "90da7f37568aee36b116a030c5f99c915267edd4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/psysh/zipball/90da7f37568aee36b116a030c5f99c915267edd4", "reference": "90da7f37568aee36b116a030c5f99c915267edd4", "shasum": ""}, "require": {"dnoegel/php-xdg-base-dir": "0.1.*", "ext-json": "*", "ext-tokenizer": "*", "jakub-onderka/php-console-highlighter": "0.3.*|0.4.*", "nikic/php-parser": "~1.3|~2.0|~3.0|~4.0", "php": ">=5.4.0", "symfony/console": "~2.3.10|^2.4.2|~3.0|~4.0|~5.0", "symfony/var-dumper": "~2.7|~3.0|~4.0|~5.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2", "hoa/console": "~2.15|~3.16", "phpunit/phpunit": "~4.8.35|~5.0|~6.0|~7.0"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well.", "ext-readline": "Enables support for arrow-key history navigation, and showing and manipulating command history.", "hoa/console": "A pure PHP readline implementation. You'll want this if your PHP install doesn't already support readline or libedit."}, "bin": ["bin/psysh"], "type": "library", "extra": {"branch-alias": {"dev-develop": "0.9.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "time": "2019-12-06T14:19:43+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "time": "2019-03-08T08:55:37+00:00"}, {"name": "ramsey/uuid", "version": "3.9.7", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "dc75aa439eb4c1b77f5379fd958b3dc0e6014178"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/dc75aa439eb4c1b77f5379fd958b3dc0e6014178", "reference": "dc75aa439eb4c1b77f5379fd958b3dc0e6014178", "shasum": ""}, "require": {"ext-json": "*", "paragonie/random_compat": "^1 | ^2 | ^9.99.99", "php": "^5.4 | ^7.0 | ^8.0", "symfony/polyfill-ctype": "^1.8"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"codeception/aspect-mock": "^1 | ^2", "doctrine/annotations": "^1.2", "goaop/framework": "1.0.0-alpha.2 | ^1 | >=2.1.0 <=2.3.2", "mockery/mockery": "^0.9.11 | ^1", "moontoast/math": "^1.1", "nikic/php-parser": "<=4.5.0", "paragonie/random-lib": "^2", "php-mock/php-mock-phpunit": "^0.3 | ^1.1 | ^2.6", "php-parallel-lint/php-parallel-lint": "^1.3", "phpunit/phpunit": ">=4.8.36 <9.0.0 | >=9.3.0", "squizlabs/php_codesniffer": "^3.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-ctype": "Provides support for PHP Ctype functions", "ext-libsodium": "Provides the PECL libsodium extension for use with the SodiumRandomGenerator", "ext-openssl": "Provides the OpenSSL extension for use with the OpenSslGenerator", "ext-uuid": "Provides the PECL UUID extension for use with the PeclUuidTimeGenerator and PeclUuidRandomGenerator", "moontoast/math": "Provides support for converting UUID to 128-bit integer (in string form).", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-console": "A console application for generating UUIDs with ramsey/uuid", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Ramsey\\Uuid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Formerly rhumsaa/uuid. A PHP 5.4+ library for generating RFC 4122 version 1, 3, 4, and 5 universally unique identifiers (UUID).", "homepage": "https://github.com/ramsey/uuid", "keywords": ["guid", "identifier", "uuid"], "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/uuid", "type": "tidelift"}], "time": "2022-12-19T21:55:10+00:00"}, {"name": "react/promise", "version": "v2.11.0", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "1a8460931ea36dc5c76838fec5734d55c88c6831"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/1a8460931ea36dc5c76838fec5734d55c88c6831", "reference": "1a8460931ea36dc5c76838fec5734d55c88c6831", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2023-11-16T16:16:50+00:00"}, {"name": "sentry/sdk", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/getsentry/sentry-php-sdk.git", "reference": "089858b1b27d3705a5fd1c32d8d10beb55980190"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getsentry/sentry-php-sdk/zipball/089858b1b27d3705a5fd1c32d8d10beb55980190", "reference": "089858b1b27d3705a5fd1c32d8d10beb55980190", "shasum": ""}, "require": {"http-interop/http-factory-guzzle": "^1.0", "sentry/sentry": "^2.5", "symfony/http-client": "^4.3|^5.0"}, "type": "metapackage", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Sentry", "email": "<EMAIL>"}], "description": "This is a metapackage shipping sentry/sentry with a recommended HTTP client.", "homepage": "http://sentry.io", "keywords": ["crash-reporting", "crash-reports", "error-handler", "error-monitoring", "log", "logging", "sentry"], "funding": [{"url": "https://sentry.io/", "type": "custom"}, {"url": "https://sentry.io/pricing/", "type": "custom"}], "time": "2020-09-14T09:30:55+00:00"}, {"name": "sentry/sentry", "version": "2.5.2", "source": {"type": "git", "url": "https://github.com/getsentry/sentry-php.git", "reference": "ce63f13e2cf9f72ec169413545a3f7312b2e45e3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getsentry/sentry-php/zipball/ce63f13e2cf9f72ec169413545a3f7312b2e45e3", "reference": "ce63f13e2cf9f72ec169413545a3f7312b2e45e3", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "guzzlehttp/promises": "^1.3", "guzzlehttp/psr7": "^1.7", "jean85/pretty-package-versions": "^1.5|^2.0.1", "php": "^7.1", "php-http/async-client-implementation": "^1.0", "php-http/client-common": "^1.5|^2.0", "php-http/discovery": "^1.6.1", "php-http/httplug": "^1.1|^2.0", "php-http/message": "^1.5", "psr/http-factory": "^1.0", "psr/http-message-implementation": "^1.0", "psr/log": "^1.0", "symfony/options-resolver": "^2.7|^3.0|^4.0|^5.0", "symfony/polyfill-uuid": "^1.13.1"}, "conflict": {"php-http/client-common": "1.8.0", "raven/raven": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16", "monolog/monolog": "^1.3|^2.0", "php-http/mock-client": "^1.4", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^7.5.20", "symfony/phpunit-bridge": "^5.2", "vimeo/psalm": "^4.2"}, "suggest": {"monolog/monolog": "Allow sending log messages to Sentry by using the included Monolog handler."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Sentry\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "Sentry", "email": "<EMAIL>"}], "description": "A PHP SDK for Sentry (http://sentry.io)", "homepage": "http://sentry.io", "keywords": ["crash-reporting", "crash-reports", "error-handler", "error-monitoring", "log", "logging", "sentry"], "funding": [{"url": "https://sentry.io/", "type": "custom"}, {"url": "https://sentry.io/pricing/", "type": "custom"}], "time": "2021-02-02T08:58:09+00:00"}, {"name": "sentry/sentry-laravel", "version": "1.9.0", "source": {"type": "git", "url": "https://github.com/getsentry/sentry-laravel.git", "reference": "8567e70d03081cbc04e35eb3fa7389d06816196e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getsentry/sentry-laravel/zipball/8567e70d03081cbc04e35eb3fa7389d06816196e", "reference": "8567e70d03081cbc04e35eb3fa7389d06816196e", "shasum": ""}, "require": {"illuminate/support": "5.0 - 5.8 | ^6.0 | ^7.0 | ^8.0", "php": "^7.1", "sentry/sdk": "^2.1"}, "require-dev": {"friendsofphp/php-cs-fixer": "2.16.*", "laravel/framework": "^7.0", "orchestra/testbench": "^5.0", "phpunit/phpunit": "^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev", "dev-0.x": "0.x-dev"}, "laravel": {"providers": ["Sentry\\Laravel\\ServiceProvider"], "aliases": {"Sentry": "Sentry\\Laravel\\Facade"}}}, "autoload": {"psr-0": {"Sentry\\Laravel\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Sentry", "email": "<EMAIL>"}], "description": "<PERSON>vel SDK for Sentry (https://sentry.io)", "homepage": "https://sentry.io", "keywords": ["crash-reporting", "crash-reports", "error-handler", "error-monitoring", "laravel", "log", "logging", "sentry"], "funding": [{"url": "https://sentry.io/", "type": "custom"}, {"url": "https://sentry.io/pricing/", "type": "custom"}], "time": "2020-09-07T07:16:41+00:00"}, {"name": "spatie/image-optimizer", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/spatie/image-optimizer.git", "reference": "1530d6cf72070068eecab150ffb73466c3806bdd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/image-optimizer/zipball/1530d6cf72070068eecab150ffb73466c3806bdd", "reference": "1530d6cf72070068eecab150ffb73466c3806bdd", "shasum": ""}, "require": {"php": "^7.0", "psr/log": "^1.0", "symfony/process": "^2.8|^3.0|^4.0"}, "require-dev": {"phpunit/phpunit": "^6.2|^7.0", "symfony/var-dumper": "^3.0|^4.0"}, "type": "library", "autoload": {"psr-4": {"Spatie\\ImageOptimizer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "description": "Easily optimize images using PHP", "homepage": "https://github.com/spatie/image-optimizer", "keywords": ["image-optimizer", "spatie"], "time": "2018-06-05T07:36:17+00:00"}, {"name": "spatie/pdf-to-image", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/spatie/pdf-to-image.git", "reference": "7c68cc95c0a5e63d712b705ff55459071c2a7b54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/pdf-to-image/zipball/7c68cc95c0a5e63d712b705ff55459071c2a7b54", "reference": "7c68cc95c0a5e63d712b705ff55459071c2a7b54", "shasum": ""}, "require": {"ext-imagick": "*", "php": "^7.2|^8.0"}, "require-dev": {"pestphp/pest": "^1.21"}, "type": "library", "autoload": {"psr-4": {"Spatie\\PdfToImage\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "description": "Convert a pdf to an image", "homepage": "https://github.com/spatie/pdf-to-image", "keywords": ["convert", "image", "pdf", "pdf-to-image", "spatie"], "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2024-03-07T09:42:07+00:00"}, {"name": "staudenmeir/eloquent-eager-limit", "version": "v1.2.1", "source": {"type": "git", "url": "https://github.com/staudenmeir/eloquent-eager-limit.git", "reference": "388fa4fa26911ba4be2f3076f79a489f276f7b5e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/staudenmeir/eloquent-eager-limit/zipball/388fa4fa26911ba4be2f3076f79a489f276f7b5e", "reference": "388fa4fa26911ba4be2f3076f79a489f276f7b5e", "shasum": ""}, "require": {"illuminate/database": "~5.5.43|~5.6.34|5.7.*", "illuminate/support": "^5.5.14", "php": ">=7.0"}, "require-dev": {"laravel/homestead": "^7.18", "phpunit/phpunit": "~6.5"}, "type": "library", "autoload": {"psr-4": {"Staudenmeir\\EloquentEagerLimit\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Laravel Eloquent eager loading with limit", "time": "2019-07-31T16:00:04+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v6.3.0", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/8a5d5072dca8f48460fce2f4131fcc495eec654c", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c", "shasum": ""}, "require": {"egulias/email-validator": "^2.0|^3.1", "php": ">=7.0.0", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"mockery/mockery": "^1.0", "symfony/phpunit-bridge": "^4.4|^5.4"}, "suggest": {"ext-intl": "Needed to support internationalized email addresses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/swiftmailer/swiftmailer", "type": "tidelift"}], "abandoned": "symfony/mailer", "time": "2021-10-18T15:26:12+00:00"}, {"name": "symfony/console", "version": "v4.4.49", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "33fa45ffc81fdcc1ca368d4946da859c8cdb58d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/33fa45ffc81fdcc1ca368d4946da859c8cdb58d9", "reference": "33fa45ffc81fdcc1ca368d4946da859c8cdb58d9", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2"}, "conflict": {"psr/log": ">=3", "symfony/dependency-injection": "<3.4", "symfony/event-dispatcher": "<4.3|>=5", "symfony/lock": "<4.4", "symfony/process": "<3.3"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "require-dev": {"psr/log": "^1|^2", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/event-dispatcher": "^4.3", "symfony/lock": "^4.4|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/var-dumper": "^4.3|^5.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-05T17:10:16+00:00"}, {"name": "symfony/css-selector", "version": "v5.4.40", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "ea43887e9afd2029509662d4f95e8b5ef6fc9bbb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/ea43887e9afd2029509662d4f95e8b5ef6fc9bbb", "reference": "ea43887e9afd2029509662d4f95e8b5ef6fc9bbb", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:33:22+00:00"}, {"name": "symfony/debug", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "1a692492190773c5310bc7877cb590c04c2f05be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/1a692492190773c5310bc7877cb590c04c2f05be", "reference": "1a692492190773c5310bc7877cb590c04c2f05be", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/log": "^1|^2|^3"}, "conflict": {"symfony/http-kernel": "<3.4"}, "require-dev": {"symfony/http-kernel": "^3.4|^4.0|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to ease debugging PHP code", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "abandoned": "symfony/error-handler", "time": "2022-07-28T16:29:46+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.3", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "80d075412b557d41002320b96a096ca65aa2c98d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/80d075412b557d41002320b96a096ca65aa2c98d", "reference": "80d075412b557d41002320b96a096ca65aa2c98d", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-24T14:02:46+00:00"}, {"name": "symfony/error-handler", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "be731658121ef2d8be88f3a1ec938148a9237291"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/be731658121ef2d8be88f3a1ec938148a9237291", "reference": "be731658121ef2d8be88f3a1ec938148a9237291", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/log": "^1|^2|^3", "symfony/debug": "^4.4.5", "symfony/var-dumper": "^4.4|^5.0"}, "require-dev": {"symfony/http-kernel": "^4.4|^5.0", "symfony/serializer": "^4.4|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-28T16:29:46+00:00"}, {"name": "symfony/event-dispatcher", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "1e866e9e5c1b22168e0ce5f0b467f19bba61266a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/1e866e9e5c1b22168e0ce5f0b467f19bba61266a", "reference": "1e866e9e5c1b22168e0ce5f0b467f19bba61266a", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/event-dispatcher-contracts": "^1.1", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/dependency-injection": "<3.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "1.1"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/error-handler": "~3.4|~4.4", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v1.10.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "761c8b8387cfe5f8026594a75fdf0a4e83ba6974"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/761c8b8387cfe5f8026594a75fdf0a4e83ba6974", "reference": "761c8b8387cfe5f8026594a75fdf0a4e83ba6974", "shasum": ""}, "require": {"php": ">=7.1.3"}, "suggest": {"psr/event-dispatcher": "", "symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/filesystem", "version": "v5.4.44", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "76c3818964e9d32be3862c9318ae3ba9aa280ddc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/76c3818964e9d32be3862c9318ae3ba9aa280ddc", "reference": "76c3818964e9d32be3862c9318ae3ba9aa280ddc", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/process": "^5.4|^6.4"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-16T14:52:48+00:00"}, {"name": "symfony/finder", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "66bd787edb5e42ff59d3523f623895af05043e4f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/66bd787edb5e42ff59d3523f623895af05043e4f", "reference": "66bd787edb5e42ff59d3523f623895af05043e4f", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-29T07:35:46+00:00"}, {"name": "symfony/http-client", "version": "v5.4.44", "source": {"type": "git", "url": "https://github.com/symfony/http-client.git", "reference": "58d3dc6bfa5fb37137e32d52ddc202ba4d1cea04"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client/zipball/58d3dc6bfa5fb37137e32d52ddc202ba4d1cea04", "reference": "58d3dc6bfa5fb37137e32d52ddc202ba4d1cea04", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.1|^3", "symfony/http-client-contracts": "^2.5.3", "symfony/polyfill-php73": "^1.11", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.0|^2|^3"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "2.4"}, "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4|^2.0", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "php-http/message-factory": "^1.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4.13|^5.1.5|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/stopwatch": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "homepage": "https://symfony.com", "keywords": ["http"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-16T14:04:28+00:00"}, {"name": "symfony/http-client-contracts", "version": "v2.5.3", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "e5cc97c2b4a4db0ba26bebc154f1426e3fd1d2f1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/e5cc97c2b4a4db0ba26bebc154f1426e3fd1d2f1", "reference": "e5cc97c2b4a4db0ba26bebc154f1426e3fd1d2f1", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/http-client-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-03-26T19:42:53+00:00"}, {"name": "symfony/http-foundation", "version": "v4.4.49", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "191413c7b832c015bb38eae963f2e57498c3c173"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/191413c7b832c015bb38eae963f2e57498c3c173", "reference": "191413c7b832c015bb38eae963f2e57498c3c173", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/mime": "^4.3|^5.0", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"predis/predis": "~1.0", "symfony/expression-language": "^3.4|^4.0|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-04T16:17:57+00:00"}, {"name": "symfony/http-kernel", "version": "v4.4.51", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "ad8ab192cb619ff7285c95d28c69b36d718416c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/ad8ab192cb619ff7285c95d28c69b36d718416c7", "reference": "ad8ab192cb619ff7285c95d28c69b36d718416c7", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/log": "^1|^2", "symfony/error-handler": "^4.4", "symfony/event-dispatcher": "^4.4", "symfony/http-client-contracts": "^1.1|^2", "symfony/http-foundation": "^4.4.30|^5.3.7", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/browser-kit": "<4.3", "symfony/config": "<3.4", "symfony/console": ">=5", "symfony/dependency-injection": "<4.3", "symfony/translation": "<4.2", "twig/twig": "<1.43|<2.13,>=2"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/browser-kit": "^4.3|^5.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0", "symfony/css-selector": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^4.3|^5.0", "symfony/dom-crawler": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/finder": "^3.4|^4.0|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/routing": "^3.4|^4.0|^5.0", "symfony/stopwatch": "^3.4|^4.0|^5.0", "symfony/templating": "^3.4|^4.0|^5.0", "symfony/translation": "^4.2|^5.0", "symfony/translation-contracts": "^1.1|^2", "twig/twig": "^1.43|^2.13|^3.0.4"}, "suggest": {"symfony/browser-kit": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-11-10T13:31:29+00:00"}, {"name": "symfony/mime", "version": "v5.4.43", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "a02711d6ce461edada8c0f8641aa536709b99b47"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/a02711d6ce461edada8c0f8641aa536709b99b47", "reference": "a02711d6ce461edada8c0f8641aa536709b99b47", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<4.4", "symfony/serializer": "<5.4.35|>=6,<6.3.12|>=6.4,<6.4.3"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/process": "^5.4|^6.4", "symfony/property-access": "^4.4|^5.1|^6.0", "symfony/property-info": "^4.4|^5.1|^6.0", "symfony/serializer": "^5.4.35|~6.3.12|^6.4.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-08-13T10:38:38+00:00"}, {"name": "symfony/options-resolver", "version": "v5.4.40", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "bd1afbde6613a8d6b956115e0e14b196191fd0c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/bd1afbde6613a8d6b956115e0e14b196191fd0c4", "reference": "bd1afbde6613a8d6b956115e0e14b196191fd0c4", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php73": "~1.0", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:33:22+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "48becf00c920479ca2e910c22a5a39e5d47ca956"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/48becf00c920479ca2e910c22a5a39e5d47ca956", "reference": "48becf00c920479ca2e910c22a5a39e5d47ca956", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-iconv": "*"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/c36586dcf89a12315939e00ec9b4474adcb1d773", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773", "shasum": ""}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/85181ba99b2345b0ef10ce42ecac37612d9fd341", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php56", "version": "v1.20.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php56.git", "reference": "54b8cd7e6c1643d78d011f3be89f3ef1f9f4c675"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php56/zipball/54b8cd7e6c1643d78d011f3be89f3ef1f9f4c675", "reference": "54b8cd7e6c1643d78d011f3be89f3ef1f9f4c675", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "metapackage", "extra": {"branch-alias": {"dev-main": "1.20-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 5.6+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-23T14:02:19+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "fa2ae56c44f03bed91a39bfc9822e31e7c5c38ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/fa2ae56c44f03bed91a39bfc9822e31e7c5c38ce", "reference": "fa2ae56c44f03bed91a39bfc9822e31e7c5c38ce", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "metapackage", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "0f68c03565dcaaf25a890667542e8bd75fe7e5bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/0f68c03565dcaaf25a890667542e8bd75fe7e5bb", "reference": "0f68c03565dcaaf25a890667542e8bd75fe7e5bb", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/60328e362d4c2c802a54fcbf04f9d3fb892b4cf8", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-uuid", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-uuid.git", "reference": "21533be36c24be3f4b1669c4725c7d1d2bab4ae2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/21533be36c24be3f4b1669c4725c7d1d2bab4ae2", "reference": "21533be36c24be3f4b1669c4725c7d1d2bab4ae2", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-uuid": "*"}, "suggest": {"ext-uuid": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Uuid\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for uuid functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "uuid"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/process", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "5cee9cdc4f7805e2699d9fd66991a0e6df8252a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/5cee9cdc4f7805e2699d9fd66991a0e6df8252a2", "reference": "5cee9cdc4f7805e2699d9fd66991a0e6df8252a2", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-06-27T13:16:42+00:00"}, {"name": "symfony/routing", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "f7751fd8b60a07f3f349947a309b5bdfce22d6ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/f7751fd8b60a07f3f349947a309b5bdfce22d6ae", "reference": "f7751fd8b60a07f3f349947a309b5bdfce22d6ae", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/config": "<4.2", "symfony/dependency-injection": "<3.4", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "^1.10.4", "psr/log": "^1|^2|^3", "symfony/config": "^4.2|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/service-contracts", "version": "v2.5.3", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "a2329596ddc8fd568900e3fc76cba42489ecc7f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/a2329596ddc8fd568900e3fc76cba42489ecc7f3", "reference": "a2329596ddc8fd568900e3fc76cba42489ecc7f3", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.1", "symfony/deprecation-contracts": "^2.1|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-04-21T15:04:16+00:00"}, {"name": "symfony/translation", "version": "v4.4.47", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "45036b1d53accc48fe9bab71ccd86d57eba0dd94"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/45036b1d53accc48fe9bab71ccd86d57eba0dd94", "reference": "45036b1d53accc48fe9bab71ccd86d57eba0dd94", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation-contracts": "^1.1.6|^2"}, "conflict": {"symfony/config": "<3.4", "symfony/dependency-injection": "<3.4", "symfony/http-kernel": "<4.4", "symfony/yaml": "<3.4"}, "provide": {"symfony/translation-implementation": "1.0|2.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/finder": "~2.8|~3.0|~4.0|^5.0", "symfony/http-kernel": "^4.4", "symfony/intl": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1.2|^2", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-10-03T15:15:11+00:00"}, {"name": "symfony/translation-contracts", "version": "v2.5.3", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "b0073a77ac0b7ea55131020e87b1e3af540f4664"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/b0073a77ac0b7ea55131020e87b1e3af540f4664", "reference": "b0073a77ac0b7ea55131020e87b1e3af540f4664", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-23T13:51:25+00:00"}, {"name": "symfony/var-dumper", "version": "v4.4.47", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "1069c7a3fca74578022fab6f81643248d02f8e63"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/1069c7a3fca74578022fab6f81643248d02f8e63", "reference": "1069c7a3fca74578022fab6f81643248d02f8e63", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php72": "~1.5", "symfony/polyfill-php80": "^1.16"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/console": "<3.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^3.4|^4.0|^5.0", "symfony/process": "^4.4|^5.0", "twig/twig": "^1.43|^2.13|^3.0.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-10-03T15:15:11+00:00"}, {"name": "tijsverkoyen/css-to-inline-styles", "version": "v2.2.7", "source": {"type": "git", "url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "reference": "83ee6f38df0a63106a9e4536e3060458b74ccedb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/83ee6f38df0a63106a9e4536e3060458b74ccedb", "reference": "83ee6f38df0a63106a9e4536e3060458b74ccedb", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^5.5 || ^7.0 || ^8.0", "symfony/css-selector": "^2.7 || ^3.0 || ^4.0 || ^5.0 || ^6.0 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0 || ^7.5 || ^8.5.21 || ^9.5.10"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"psr-4": {"TijsVerkoyen\\CssToInlineStyles\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CssToInlineStyles is a class that enables you to convert HTML-pages/files into HTML-pages/files with inline styles. This is very useful when you're sending emails.", "homepage": "https://github.com/tijsverkoyen/CssToInlineStyles", "time": "2023-12-08T13:03:43+00:00"}, {"name": "torann/geoip", "version": "1.0.5", "source": {"type": "git", "url": "https://github.com/Torann/laravel-geoip.git", "reference": "b5d77ed475a3a4266ae91242686ce017d8430db9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Torann/laravel-geoip/zipball/b5d77ed475a3a4266ae91242686ce017d8430db9", "reference": "b5d77ed475a3a4266ae91242686ce017d8430db9", "shasum": ""}, "require": {"illuminate/console": "~5.0", "illuminate/support": "~5.0", "php": ">=5.5.9"}, "require-dev": {"geoip2/geoip2": "~2.1", "mockery/mockery": "^0.9.4", "phpunit/phpunit": "^4.8"}, "suggest": {"geoip2/geoip2": "Required to use the MaxMind database or web service with GeoIP (~2.1).", "monolog/monolog": "Allows for storing location not found errors to the log"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}, "laravel": {"providers": ["Torann\\GeoIP\\GeoIPServiceProvider"], "aliases": {"GeoIP": "Torann\\GeoIP\\Facades\\GeoIP"}}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Torann\\GeoIP\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Support for multiple GeoIP services.", "keywords": ["IP API", "geoip", "geolocation", "infoDB", "laravel", "laravel 5", "location", "maxmind"], "time": "2017-09-19T23:43:25+00:00"}, {"name": "tymon/jwt-auth", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/tymondesigns/jwt-auth.git", "reference": "d4cf9fd2b98790712d3e6cd1094e5ff018431f19"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tymondesigns/jwt-auth/zipball/d4cf9fd2b98790712d3e6cd1094e5ff018431f19", "reference": "d4cf9fd2b98790712d3e6cd1094e5ff018431f19", "shasum": ""}, "require": {"illuminate/auth": "^5.2|^6|^7", "illuminate/contracts": "^5.2|^6|^7", "illuminate/http": "^5.2|^6|^7", "illuminate/support": "^5.2|^6|^7", "lcobucci/jwt": "^3.2", "namshi/jose": "^7.0", "nesbot/carbon": "^1.0|^2.0", "php": "^5.5.9|^7.0"}, "require-dev": {"illuminate/console": "^5.2|^6|^7", "illuminate/database": "^5.2|^6|^7", "illuminate/routing": "^5.2|^6|^7", "mockery/mockery": ">=0.9.9", "phpunit/phpunit": "~4.8|~6.0"}, "type": "library", "extra": {"branch-alias": {"dev-develop": "1.0-dev"}, "laravel": {"aliases": {"JWTAuth": "Tymon\\JWTAuth\\Facades\\JWTAuth", "JWTFactory": "Tymon\\JWTAuth\\Facades\\JWTFactory"}, "providers": ["Tymon\\JWTAuth\\Providers\\LaravelServiceProvider"]}}, "autoload": {"psr-4": {"Tymon\\JWTAuth\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://tymon.xyz", "role": "Developer"}], "description": "JSON Web Token Authentication for <PERSON><PERSON> and <PERSON><PERSON>", "homepage": "https://github.com/tymondesigns/jwt-auth", "keywords": ["Authentication", "JSON Web Token", "auth", "jwt", "laravel"], "funding": [{"url": "https://www.patreon.com/seantymon", "type": "patreon"}], "time": "2020-03-04T11:21:28+00:00"}, {"name": "unisharp/laravel-filemanager", "version": "v1.9.2", "source": {"type": "git", "url": "https://github.com/UniSharp/laravel-filemanager.git", "reference": "aaf2764f26eb8dcc9356bea1e25010333c2e61f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/UniSharp/laravel-filemanager/zipball/aaf2764f26eb8dcc9356bea1e25010333c2e61f4", "reference": "aaf2764f26eb8dcc9356bea1e25010333c2e61f4", "shasum": ""}, "require": {"ext-exif": "*", "ext-fileinfo": "*", "illuminate/support": ">=4.2.0", "intervention/image": "2.*", "php": ">=5.4.0"}, "require-dev": {"orchestra/testbench": "~3.0", "phpspec/phpspec": "~2.1", "phpunit/phpunit": "~4.8"}, "suggest": {"ext-gd": "to use GD library based image processing.", "ext-imagick": "to use Imagick based image processing."}, "bin": ["bin/debug"], "type": "library", "extra": {"laravel": {"providers": ["UniSharp\\LaravelFilemanager\\LaravelFilemanagerServiceProvider"], "aliases": []}}, "autoload": {"psr-4": {"UniSharp\\LaravelFilemanager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "UniSharp Ltd.", "email": "<EMAIL>"}], "description": "A file upload/editor intended for use with Laravel 5 and CKEditor / TinyMCE", "keywords": ["CKEditor", "file", "filemanager", "image", "laravel", "manager", "<PERSON><PERSON><PERSON>", "upload"], "time": "2018-07-05T11:36:08+00:00"}, {"name": "vlucas/phpdotenv", "version": "v2.6.9", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "2e93cc98e2e8e869f8d9cfa61bb3a99ba4fc4141"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/2e93cc98e2e8e869f8d9cfa61bb3a99ba4fc4141", "reference": "2e93cc98e2e8e869f8d9cfa61bb3a99ba4fc4141", "shasum": ""}, "require": {"php": "^5.3.9 || ^7.0 || ^8.0", "symfony/polyfill-ctype": "^1.17"}, "require-dev": {"ext-filter": "*", "ext-pcre": "*", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.21"}, "suggest": {"ext-filter": "Required to use the boolean validator.", "ext-pcre": "Required to use most of the library."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2021-12-12T22:59:22+00:00"}], "packages-dev": [{"name": "barryvdh/laravel-debugbar", "version": "v3.4.2", "source": {"type": "git", "url": "https://github.com/barryvdh/laravel-debugbar.git", "reference": "91ee8b3acf0d72a4937f4855bd245acbda9910ac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/barryvdh/laravel-debugbar/zipball/91ee8b3acf0d72a4937f4855bd245acbda9910ac", "reference": "91ee8b3acf0d72a4937f4855bd245acbda9910ac", "shasum": ""}, "require": {"illuminate/routing": "^5.5|^6|^7", "illuminate/session": "^5.5|^6|^7", "illuminate/support": "^5.5|^6|^7", "maximebf/debugbar": "^1.16.3", "php": ">=7.0", "symfony/debug": "^3|^4|^5", "symfony/finder": "^3|^4|^5"}, "require-dev": {"orchestra/testbench": "^3.5|^4.0|^5.0", "phpunit/phpunit": "^6.0|^7.0|^8.5|^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}, "laravel": {"providers": ["Barryvdh\\Debugbar\\ServiceProvider"], "aliases": {"Debugbar": "Barryvdh\\Debugbar\\Facade"}}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Barryvdh\\Debugbar\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "PHP Debugbar integration for Laravel", "keywords": ["debug", "debugbar", "laravel", "profiler", "webprofiler"], "funding": [{"url": "https://github.com/barryvdh", "type": "github"}], "time": "2020-08-30T07:08:17+00:00"}, {"name": "doctrine/instantiator", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/0a0fa9780f5d4e507415a065172d26a98d02047b", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^11", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.16 || ^1", "phpstan/phpstan": "^1.4", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.30 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-12-30T00:15:36+00:00"}, {"name": "filp/whoops", "version": "2.16.0", "source": {"type": "git", "url": "https://github.com/filp/whoops.git", "reference": "befcdc0e5dce67252aa6322d82424be928214fa2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/filp/whoops/zipball/befcdc0e5dce67252aa6322d82424be928214fa2", "reference": "befcdc0e5dce67252aa6322d82424be928214fa2", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "require-dev": {"mockery/mockery": "^1.0", "phpunit/phpunit": "^7.5.20 || ^8.5.8 || ^9.3.3", "symfony/var-dumper": "^4.0 || ^5.0"}, "suggest": {"symfony/var-dumper": "Pretty print complex values better with var-dumper available", "whoops/soap": "Formats errors as SOAP responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Whoops\\": "src/Whoops/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/filp", "role": "Developer"}], "description": "php error handling for cool kids", "homepage": "https://filp.github.io/whoops/", "keywords": ["error", "exception", "handling", "library", "throwable", "whoops"], "funding": [{"url": "https://github.com/denis-so<PERSON><PERSON>", "type": "github"}], "time": "2024-09-25T12:00:00+00:00"}, {"name": "fzaninotto/faker", "version": "v1.9.2", "source": {"type": "git", "url": "https://github.com/fzaninotto/Faker.git", "reference": "848d8125239d7dbf8ab25cb7f054f1a630e68c2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fzaninotto/Faker/zipball/848d8125239d7dbf8ab25cb7f054f1a630e68c2e", "reference": "848d8125239d7dbf8ab25cb7f054f1a630e68c2e", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"ext-intl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7", "squizlabs/php_codesniffer": "^2.9.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "abandoned": true, "time": "2020-12-11T09:56:16+00:00"}, {"name": "hamcrest/hamcrest-php", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "shasum": ""}, "require": {"php": "^5.3|^7.0|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["hamcrest"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "time": "2020-07-09T08:09:16+00:00"}, {"name": "maximebf/debugbar", "version": "v1.23.2", "source": {"type": "git", "url": "https://github.com/maximebf/php-debugbar.git", "reference": "689720d724c771ac4add859056744b7b3f2406da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maximebf/php-debugbar/zipball/689720d724c771ac4add859056744b7b3f2406da", "reference": "689720d724c771ac4add859056744b7b3f2406da", "shasum": ""}, "require": {"php": "^7.2|^8", "psr/log": "^1|^2|^3", "symfony/var-dumper": "^4|^5|^6|^7"}, "require-dev": {"dbrekelmans/bdi": "^1", "phpunit/phpunit": "^8|^9", "symfony/panther": "^1|^2.1", "twig/twig": "^1.38|^2.7|^3.0"}, "suggest": {"kriswallsmith/assetic": "The best way to manage assets", "monolog/monolog": "Log using Monolog", "predis/predis": "Redis storage"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.23-dev"}}, "autoload": {"psr-4": {"DebugBar\\": "src/DebugBar/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "maxime.bouroume<PERSON>@gmail.com", "homepage": "http://maximebf.com"}, {"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "Debug bar in the browser for php application", "homepage": "https://github.com/maximebf/php-debugbar", "keywords": ["debug", "debugbar"], "time": "2024-09-16T11:23:09+00:00"}, {"name": "mockery/mockery", "version": "1.3.6", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "dc206df4fa314a50bbb81cf72239a305c5bbd5c0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mockery/mockery/zipball/dc206df4fa314a50bbb81cf72239a305c5bbd5c0", "reference": "dc206df4fa314a50bbb81cf72239a305c5bbd5c0", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": ">=5.6.0"}, "require-dev": {"phpunit/phpunit": "^5.7.10|^6.5|^7.5|^8.5|^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-0": {"Mockery": "library/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://blog.astrumfutura.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://davedevelopment.co.uk"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "time": "2022-09-07T15:05:49+00:00"}, {"name": "myclabs/deep-copy", "version": "1.12.0", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "3a6b9a42cd8f8771bd4295d13e1423fa7f3d942c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/3a6b9a42cd8f8771bd4295d13e1423fa7f3d942c", "reference": "3a6b9a42cd8f8771bd4295d13e1423fa7f3d942c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2024-06-12T14:39:25+00:00"}, {"name": "phar-io/manifest", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "7761fcacf03b4d4f16e7ccb606d4879ca431fcf4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/7761fcacf03b4d4f16e7ccb606d4879ca431fcf4", "reference": "7761fcacf03b4d4f16e7ccb606d4879ca431fcf4", "shasum": ""}, "require": {"ext-dom": "*", "ext-phar": "*", "phar-io/version": "^2.0", "php": "^5.6 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "time": "2018-07-08T19:23:20+00:00"}, {"name": "phar-io/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "45a2ec53a73c70ce41d55cedef9063630abaf1b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/45a2ec53a73c70ce41d55cedef9063630abaf1b6", "reference": "45a2ec53a73c70ce41d55cedef9063630abaf1b6", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "time": "2018-07-08T19:19:57+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.3.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/622548b623e81ca6d78b721c5e029f4ce664f170", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170", "shasum": ""}, "require": {"ext-filter": "*", "php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.3", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.2", "psalm/phar": "^4.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "time": "2021-10-19T17:43:47+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "77a32518733312af16a44300404e945338981de3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/77a32518733312af16a44300404e945338981de3", "reference": "77a32518733312af16a44300404e945338981de3", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "*", "psalm/phar": "^4.8"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "time": "2022-03-15T21:29:03+00:00"}, {"name": "phpspec/prophecy", "version": "v1.19.0", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "67a759e7d8746d501c41536ba40cd9c0a07d6a87"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/67a759e7d8746d501c41536ba40cd9c0a07d6a87", "reference": "67a759e7d8746d501c41536ba40cd9c0a07d6a87", "shasum": ""}, "require": {"doctrine/instantiator": "^1.2 || ^2.0", "php": "^7.2 || 8.0.* || 8.1.* || 8.2.* || 8.3.*", "phpdocumentor/reflection-docblock": "^5.2", "sebastian/comparator": "^3.0 || ^4.0 || ^5.0 || ^6.0", "sebastian/recursion-context": "^3.0 || ^4.0 || ^5.0 || ^6.0"}, "require-dev": {"phpspec/phpspec": "^6.0 || ^7.0", "phpstan/phpstan": "^1.9", "phpunit/phpunit": "^8.0 || ^9.0 || ^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Prophecy\\": "src/Prophecy"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "dev", "fake", "mock", "spy", "stub"], "time": "2024-02-29T11:52:51+00:00"}, {"name": "phpunit/php-code-coverage", "version": "6.1.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "807e6013b00af69b6c5d9ceb4282d0393dbb9d8d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/807e6013b00af69b6c5d9ceb4282d0393dbb9d8d", "reference": "807e6013b00af69b6c5d9ceb4282d0393dbb9d8d", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlwriter": "*", "php": "^7.1", "phpunit/php-file-iterator": "^2.0", "phpunit/php-text-template": "^1.2.1", "phpunit/php-token-stream": "^3.0", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^3.1 || ^4.0", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "suggest": {"ext-xdebug": "^2.6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "time": "2018-10-31T16:06:48+00:00"}, {"name": "phpunit/php-file-iterator", "version": "2.0.6", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "69deeb8664f611f156a924154985fbd4911eb36b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/69deeb8664f611f156a924154985fbd4911eb36b", "reference": "69deeb8664f611f156a924154985fbd4911eb36b", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-01T13:39:50+00:00"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "time": "2015-06-21T13:50:34+00:00"}, {"name": "phpunit/php-timer", "version": "2.1.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "a691211e94ff39a34811abd521c31bd5b305b0bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-timer/zipball/a691211e94ff39a34811abd521c31bd5b305b0bb", "reference": "a691211e94ff39a34811abd521c31bd5b305b0bb", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-01T13:42:41+00:00"}, {"name": "phpunit/php-token-stream", "version": "3.1.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "9c1da83261628cb24b6a6df371b6e312b3954768"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/9c1da83261628cb24b6a6df371b6e312b3954768", "reference": "9c1da83261628cb24b6a6df371b6e312b3954768", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "abandoned": true, "time": "2021-07-26T12:15:06+00:00"}, {"name": "phpunit/phpunit", "version": "7.5.20", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "9467db479d1b0487c99733bb1e7944d32deded2c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/9467db479d1b0487c99733bb1e7944d32deded2c", "reference": "9467db479d1b0487c99733bb1e7944d32deded2c", "shasum": ""}, "require": {"doctrine/instantiator": "^1.1", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "myclabs/deep-copy": "^1.7", "phar-io/manifest": "^1.0.2", "phar-io/version": "^2.0", "php": "^7.1", "phpspec/prophecy": "^1.7", "phpunit/php-code-coverage": "^6.0.7", "phpunit/php-file-iterator": "^2.0.1", "phpunit/php-text-template": "^1.2.1", "phpunit/php-timer": "^2.1", "sebastian/comparator": "^3.0", "sebastian/diff": "^3.0", "sebastian/environment": "^4.0", "sebastian/exporter": "^3.1", "sebastian/global-state": "^2.0", "sebastian/object-enumerator": "^3.0.3", "sebastian/resource-operations": "^2.0", "sebastian/version": "^2.0.1"}, "conflict": {"phpunit/phpunit-mock-objects": "*"}, "require-dev": {"ext-pdo": "*"}, "suggest": {"ext-soap": "*", "ext-xdebug": "*", "phpunit/php-invoker": "^2.0"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "7.5-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "time": "2020-01-08T08:45:45+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "92a1a52e86d34cde6caa54f1b5ffa9fda18e5d54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/92a1a52e86d34cde6caa54f1b5ffa9fda18e5d54", "reference": "92a1a52e86d34cde6caa54f1b5ffa9fda18e5d54", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-01T13:45:45+00:00"}, {"name": "sebastian/comparator", "version": "3.0.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "1dc7ceb4a24aede938c7af2a9ed1de09609ca770"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/1dc7ceb4a24aede938c7af2a9ed1de09609ca770", "reference": "1dc7ceb4a24aede938c7af2a9ed1de09609ca770", "shasum": ""}, "require": {"php": ">=7.1", "sebastian/diff": "^3.0", "sebastian/exporter": "^3.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-09-14T12:31:48+00:00"}, {"name": "sebastian/diff", "version": "3.0.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "98ff311ca519c3aa73ccd3de053bdb377171d7b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/diff/zipball/98ff311ca519c3aa73ccd3de053bdb377171d7b6", "reference": "98ff311ca519c3aa73ccd3de053bdb377171d7b6", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.0", "symfony/process": "^2 || ^3.3 || ^4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:16:36+00:00"}, {"name": "sebastian/environment", "version": "4.2.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "56932f6049a0482853056ffd617c91ffcc754205"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/56932f6049a0482853056ffd617c91ffcc754205", "reference": "56932f6049a0482853056ffd617c91ffcc754205", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.5"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-01T13:49:59+00:00"}, {"name": "sebastian/exporter", "version": "3.1.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "1939bc8fd1d39adcfa88c5b35335910869214c56"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/1939bc8fd1d39adcfa88c5b35335910869214c56", "reference": "1939bc8fd1d39adcfa88c5b35335910869214c56", "shasum": ""}, "require": {"php": ">=7.2", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:21:38+00:00"}, {"name": "sebastian/global-state", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/global-state/zipball/e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4", "reference": "e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "time": "2017-04-27T15:39:26+00:00"}, {"name": "sebastian/object-enumerator", "version": "3.0.5", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "ac5b293dba925751b808e02923399fb44ff0d541"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/ac5b293dba925751b808e02923399fb44ff0d541", "reference": "ac5b293dba925751b808e02923399fb44ff0d541", "shasum": ""}, "require": {"php": ">=7.0", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-01T13:54:02+00:00"}, {"name": "sebastian/object-reflector", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "1d439c229e61f244ff1f211e5c99737f90c67def"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/object-reflector/zipball/1d439c229e61f244ff1f211e5c99737f90c67def", "reference": "1d439c229e61f244ff1f211e5c99737f90c67def", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-01T13:56:04+00:00"}, {"name": "sebastian/recursion-context", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "9bfd3c6f1f08c026f542032dfb42813544f7d64c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/9bfd3c6f1f08c026f542032dfb42813544f7d64c", "reference": "9bfd3c6f1f08c026f542032dfb42813544f7d64c", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-01T14:07:30+00:00"}, {"name": "sebastian/resource-operations", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "72a7f7674d053d548003b16ff5a106e7e0e06eee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/resource-operations/zipball/72a7f7674d053d548003b16ff5a106e7e0e06eee", "reference": "72a7f7674d053d548003b16ff5a106e7e0e06eee", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-01T13:59:09+00:00"}, {"name": "sebastian/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/99732be0ddb3361e16ad77b68ba41efc8e979019", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "time": "2016-10-03T07:35:21+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:36:25+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "time": "2022-06-03T18:03:27+00:00"}], "aliases": [], "minimum-stability": "dev", "stability-flags": [], "prefer-stable": true, "prefer-lowest": false, "platform": {"php": "^7.1.3"}, "platform-dev": [], "plugin-api-version": "1.1.0"}